<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <import resource="classpath:spring/config.xml"/>
    <context:component-scan base-package="com.facishare.paas.appframework"/>
    <context:annotation-config/>

<!--    <bean id="autoConf"-->
<!--          class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"-->
<!--          p:fileEncoding="UTF-8"-->
<!--          p:ignoreResourceNotFound="true"-->
<!--          p:ignoreUnresolvablePlaceholders="false"-->
<!--          p:location="classpath:application.properties"-->
<!--          p:configName="fs-paas-metadata-mongo"/>-->

<!--    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"-->
<!--          c:placeholderConfigurer-ref="autoConf"/>-->


</beans>
