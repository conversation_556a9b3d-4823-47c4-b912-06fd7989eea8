package com.facishare.paas.appframework.config


import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Created by liyigua<PERSON> on 2017/11/10.
 */

class ConfigServiceProxyTest extends Specification {

//    @Autowired
//    ConfigServiceProxy configServiceProxy
//
//    def "test create"() {
//        given:
//        def arg = CreateConfig.Arg.builder()
//                .pkg("CRM")
//                .key("key3")
//                .valueJson("""{"ab":2}""").build()
//        when:
//        CreateConfig.Result result = configServiceProxy.create(arg, tenantId, userId)
//        then:
//        result.isFailed() == expect
//        where:
//        tenantId | userId || expect
//        "2"      | "1000" || false
//    }
//
//
//    def "test update"() {
//        given:
//        def arg = UpdateConfig.Arg.builder()
//                .pkg("CRM")
//                .key("key3")
//                .valueJson("""{"ab":3}""").build()
//        when:
//        UpdateConfig.Result result = configServiceProxy.update(arg, tenantId, userId)
//        then:
//        result.isFailed() == expect
//        where:
//        tenantId | userId || expect
//        "2"      | "1000" || false
//
//    }
//
//    def "test query"() {
//        given:
//        def arg = QueryConfig.Arg.builder()
//                .pkg("CRM")
//                .key("key3").build()
//        when:
//        QueryConfig.Result result = configServiceProxy.query(arg, tenantId, userId)
//        then:
//        result.isFailed() == expect
//        println result
//        where:
//        tenantId | userId || expect
//        "2"      | "1000" || false
//    }
//
//    def "test delete"() {
//        given:
//        def arg = DeleteConfig.Arg.builder()
//                .pkg("CRM")
//                .key("key3")
//                .build()
//        when:
//        DeleteConfig.Result result = configServiceProxy.delete(arg, tenantId, userId)
//        then:
//        result.isFailed() == expect
//        where:
//        tenantId | userId || expect
//        "2"      | "1000" || false
//    }

}
