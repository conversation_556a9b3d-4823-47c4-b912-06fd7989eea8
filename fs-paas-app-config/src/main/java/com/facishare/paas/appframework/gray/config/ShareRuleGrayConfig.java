package com.facishare.paas.appframework.gray.config;

import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/04/04
 */
@Slf4j
public abstract class ShareRuleGrayConfig {

    private static final Splitter CONFIG_SPLITTER = Splitter.on(",").omitEmptyStrings();

    private static final String ALL = "ALL";

    private static volatile boolean grayOpen;
    private static Set<String> grayTenantIds = Sets.newHashSet();

    static {
        ConfigFactory.getConfig("fs-paas-share-rule-gray-config", config -> {
            log.warn("reload config fs-paas-share-rule-gray-config,content:{}", config.getString());
            grayOpen = config.getBool("gray.open", false);
            grayTenantIds = getSetFromConfig(config, "gray.tenant.ids");
        });
    }

    private static Set<String> getSetFromConfig(IConfig config, String key) {
        return Sets.newHashSet(CONFIG_SPLITTER.split(config.get(key, "")));
    }

    public static boolean isGrayOpen(String tenantId) {
        if (!grayOpen) {
            return false;
        }
        if (grayTenantIds.contains(ALL)) {
            return true;
        }
        return grayTenantIds.contains(tenantId);
    }
}
