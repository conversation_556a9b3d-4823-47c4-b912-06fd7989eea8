package com.facishare.paas.appframework.config;

import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;

/**
 * Created by liyiguang on 2017/11/10.
 */
public class ConfigException extends APPException{

    public ConfigException(String message) {
        super(message);
    }

    public ConfigException(String message, Throwable cause) {
        super(message, cause);
    }

    public ConfigException(SystemErrorCode errorCode) {
        super(errorCode);
    }

    public ConfigException(SystemErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }
}
