package com.facishare.paas.appframework.config;

import com.facishare.paas.appframework.core.model.User;
import com.github.autoconf.ConfigFactory;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * create by z<PERSON><PERSON> on 2020/06/13
 */
@Slf4j
@Component
public final class PartnerStatusConfig implements ApplicationContextAware {
    @Autowired
    private ConfigService configService;

    private Cache<String, String> cache;

    private static PartnerStatusConfig instance;
    private static final String IS_OPEN_PARTNER = "config_partner_open";
    private static final String OPEN = "open";
    private static volatile boolean USE_CACHE;

    private PartnerStatusConfig() {
        cache = CacheBuilder.newBuilder()
                .expireAfterAccess(8, TimeUnit.HOURS)
                .maximumSize(1000)
                .initialCapacity(600)
                .concurrencyLevel(8)
                .recordStats()
                .build();
        load();
    }

    private void load() {
        ConfigFactory.getConfig("fs-paas-appframework-config", config -> {
            if (!(USE_CACHE = config.getBool("check_is_open_partner_use_cache", true))) {
                cache.cleanUp();
            }
        });
    }

    public static PartnerStatusConfig getInstance() {
        return instance;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        instance = applicationContext.getBean(PartnerStatusConfig.class);
    }

    public boolean isOpenPartner(User user) {
        if (!USE_CACHE) {
            return OPEN.equals(getConfig(user));
        }
        try {
            return OPEN.equals(cache.get(user.getTenantId(), () -> getConfig(user)));
        } catch (ExecutionException e) {
            log.warn("get cache fail, ei:{}, userId:{}", user.getTenantId(), user.getUserId(), e);
        }
        return false;
    }

    private String getConfig(User user) {
        try {
            return configService.findTenantConfig(user, IS_OPEN_PARTNER);
        } catch (Exception e) {
            log.warn("check isOpenPartner fail, ei:{}, userId:{}", user.getTenantId(), user.getUserId(), e);
        }
        return "";
    }
}
