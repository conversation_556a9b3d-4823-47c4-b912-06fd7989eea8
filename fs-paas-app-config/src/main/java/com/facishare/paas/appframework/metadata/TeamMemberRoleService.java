package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.TeamRoleInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface TeamMemberRoleService {

    List<TeamRoleInfo> queryTeamRoleInfo(String tenantId, String describeApiName);

    Map<String, List<TeamRoleInfo>> batchQueryTeamRoleInfo(String tenantId, Set<String> describeApiNames);
}
