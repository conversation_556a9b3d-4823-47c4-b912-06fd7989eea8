package com.facishare.paas.appframework.config;

import com.facishare.paas.appframework.core.model.User;
import com.fxiaoke.bizconf.arg.ConfigArg;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 全局租户配置和用户配置服务
 * <p>
 * Created by liyigua<PERSON> on 2017/11/8.
 */
public interface ConfigService {

    void createTenantConfig(User user, String key, String value, ConfigValueType type);

    void createTenantConfig(User user, String key, String value, ConfigValueType type, String pkg);

    void updateTenantConfig(User user, String key, String value, ConfigValueType type);

    void updateTenantConfig(User user, String key, String value, ConfigValueType type, String pkg);

    void deleteTenantConfig(User user, String key);

    void deleteTenantConfig(User user, String key, String pkg);

    String findTenantConfig(User user, String key);

    String findTenantConfig(User user, String key, String pkg);

    Map<String, String> queryTenantConfigs(User user, List<String> keys);

    Map<String, String> queryTenantConfigs(User user, List<String> keys, String pkg);

    void createUserConfig(User user, String key, String value, ConfigValueType type);

    void updateUserConfig(User user, String key, String value, ConfigValueType type);

    void upsertTenantConfig(User user, String key, String value, ConfigValueType type);

    void upsertUserConfig(User user, String key, String value, ConfigValueType type);

    void batchUpsertTenantConfig(User user, List<ConfigArg> configArgs);

    void deleteUserConfig(User user, String key);

    String findUserConfig(User user, String key);

    Map<String, String> queryUserConfigs(User user, Collection<String> keys);

    void batchUpsertUserConfig(User user, List<ConfigArg> configArgs);

}
