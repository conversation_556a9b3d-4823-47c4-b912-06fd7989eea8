package com.facishare.paas.appframework.config;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2024/5/8
 */
@Service
public class ApplicationLayeredGrayServiceImpl implements ApplicationLayeredGrayService {
    private static final String SUPPORT_APP_LAYERED_GRAY_PRE_KEY = "support_app_layered_gray_pre";
    private static final String ENABLE = "1";
    private static final String DISABLE = "0";

    private final ConfigService configService;
    private Map<String, ApplicationLayeredConfig> applicationLayeredConfigMap = Maps.newHashMap();

    public ApplicationLayeredGrayServiceImpl(ConfigService configService) {
        this.configService = configService;
        loadConfig();
    }

    @Override
    public Set<String> defineAppId(User user) {
        return ImmutableSet.copyOf(applicationLayeredConfigMap.keySet());
    }

    @Override
    public Set<String> getDefineAppIdByDescribeApiNames(User user, String describeApiName) {
        return defineAppId(user).stream()
                .filter(appId -> supportAppLayered(user, appId, describeApiName))
                .collect(Collectors.toSet());
    }

    @Override
    public boolean supportAppLayered(User user, String appId) {
        return isAllow(appId, user.getTenantId());
    }


    @Override
    public boolean supportAppLayered(User user, String appId, String describeApiName) {
        if (Strings.isNullOrEmpty(appId) || Strings.isNullOrEmpty(describeApiName)) {
            return false;
        }
        return supportAppLayered(user, appId)
                && Objects.equals(ENABLE, configService.findTenantConfig(user, SUPPORT_APP_LAYERED_GRAY_PRE_KEY + "_" + appId + "_" + describeApiName));
    }

    @Override
    public void openApplicationLayer(User user, String appId, String describeApiName) {
        if (StringUtils.isAnyEmpty(appId, describeApiName)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        if (!supportAppLayered(user, appId)) {
            throw new ValidateException(I18NExt.text(I18NKey.UNSUPPORT_OPERATION));
        }
        if (supportAppLayered(user, appId, describeApiName)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        configService.upsertTenantConfig(user, SUPPORT_APP_LAYERED_GRAY_PRE_KEY + "_" + appId + "_" + describeApiName, ENABLE, ConfigValueType.STRING);
    }

    @Override
    public void closeApplicationLayer(User user, String appId, String describeApiName) {
        if (!supportAppLayered(user, appId, describeApiName)) {
            return;
        }
        configService.upsertTenantConfig(user, SUPPORT_APP_LAYERED_GRAY_PRE_KEY + "_" + appId + "_" + describeApiName, DISABLE, ConfigValueType.STRING);
    }

    @Override
    public boolean canOpenApplicationLayer(User user, String appId, String describeApiName) {
        if (StringUtils.isAnyEmpty(appId, describeApiName)) {
            return false;
        }
        if (!supportAppLayered(user, appId)) {
            return false;
        }
        return !supportAppLayered(user, appId, describeApiName);
    }

    @Data
    public static class ApplicationLayeredConfig {
        private final String appId;
        private final GrayRule grayRule;

        @JsonCreator
        public ApplicationLayeredConfig(@JsonProperty("appId") String appId, @JsonProperty("grayRule") GrayRule grayRule) {
            this.appId = appId;
            this.grayRule = grayRule;
        }
    }

    public boolean isAllow(String appId, String tenantId) {
        ApplicationLayeredConfig applicationLayeredConfig = applicationLayeredConfigMap.get(appId);
        if (Objects.isNull(applicationLayeredConfig) || Objects.isNull(applicationLayeredConfig.grayRule)) {
            return false;
        }
        return applicationLayeredConfig.grayRule.isAllow(tenantId);
    }

    private void loadConfig() {
        ConfigFactory.getConfig("fs-application-layered-gray", config -> {
            List<ApplicationLayeredConfig> applicationLayeredGrayList = JacksonUtils.fromJson(config.getString(), new TypeReference<List<ApplicationLayeredConfig>>() {
            });
            if (CollectionUtils.isEmpty(applicationLayeredGrayList)) {
                return;
            }
            applicationLayeredConfigMap = applicationLayeredGrayList.stream()
                    .collect(Collectors.toMap(ApplicationLayeredConfig::getAppId, Function.identity()));
        });
    }
}
