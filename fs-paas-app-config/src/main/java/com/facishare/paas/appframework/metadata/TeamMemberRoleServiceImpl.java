package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.exception.MetadataTeamMemberException;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.service.ITeamRightsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service("teamMemberRoleService")
public class TeamMemberRoleServiceImpl implements TeamMemberRoleService {
    @Autowired
    private ITeamRightsService teamRightsService;

    @Override
    public List<TeamRoleInfo> queryTeamRoleInfo(String tenantId, String describeApiName) {
        try {
            return teamRightsService.queryTeamRoleInfo(tenantId, describeApiName);
        } catch (Exception e) {
            log.warn("Error in query team role info, tenantId:{}, apiName:{}", tenantId, describeApiName, e);
            throw new MetadataTeamMemberException(e.getMessage());
        }
    }

    @Override
    public Map<String, List<TeamRoleInfo>> batchQueryTeamRoleInfo(String tenantId, Set<String> describeApiNames) {
        try {
            return teamRightsService.batchQueryTeamRoleInfo(tenantId, describeApiNames);
        } catch (Exception e) {
            log.warn("Error in batch query team role info, tenantId:{}, apiName:{}", tenantId, describeApiNames, e);
            throw new MetadataTeamMemberException(e.getMessage());
        }
    }
}
