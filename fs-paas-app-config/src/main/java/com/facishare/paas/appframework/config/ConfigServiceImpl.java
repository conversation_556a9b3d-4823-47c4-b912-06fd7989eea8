package com.facishare.paas.appframework.config;

import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.bizconf.arg.BatchQueryConfigArg;
import com.fxiaoke.bizconf.arg.ConfigArg;
import com.fxiaoke.bizconf.arg.DeleteConfigArg;
import com.fxiaoke.bizconf.arg.QueryConfigByRankArg;
import com.fxiaoke.bizconf.bean.Rank;
import com.fxiaoke.bizconf.bean.ValueType;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by liyiguang on 2017/11/8.
 * create接口: 0:创建成功 1:已存在 -1:创建失败
 * update接口: 0:修改成功 1:配置不存在 -1:修改失败
 * delete接口: 0:删除成功 1:配置不存在 -1:删除失败
 */
@Slf4j
@Service("configService")
@Primary
public class ConfigServiceImpl implements ConfigService {

    private static final String PACKAGE = "CRM";
    //private final Cache<String, String> configLocalCache;

//    public ConfigServiceImpl() {
//        configLocalCache = CacheBuilder.newBuilder()
//                .expireAfterWrite(3, TimeUnit.MINUTES)
//                .maximumSize(100000)
//                .build();
//    }

    @Autowired
    private BizConfClient bizConfClient;

    @PostConstruct
    public void init() {
        try {
            SentinelConfig instance = SentinelConfig.getInstance();
            instance.getResourceNames(ConfigHelper.getProcessInfo().getProfile());
        } catch (Exception e) {
            throw new APPException("SentinelConfig init error!", e);
        }

    }

    @Override
    public void createTenantConfig(User user, String key, String value, ConfigValueType type) {
        createTenantConfig(user, key, value, type, PACKAGE);
    }

    @Override
    public void createTenantConfig(User user, String key, String value, ConfigValueType type, String pkg) {
        pkg = Strings.isNullOrEmpty(pkg) ? PACKAGE : pkg;
        createConfig(user, key, value, type, false, pkg);
    }

    @Override
    public void updateTenantConfig(User user, String key, String value, ConfigValueType type) {
        updateTenantConfig(user, key, value, type, PACKAGE);
    }

    @Override
    public void updateTenantConfig(User user, String key, String value, ConfigValueType type, String pkg) {
        pkg = Strings.isNullOrEmpty(pkg) ? PACKAGE : pkg;
        updateConfig(user, key, value, type, false, pkg);
    }

    @Override
    public void deleteTenantConfig(User user, String key) {
        deleteTenantConfig(user, key, PACKAGE);
    }

    @Override
    public void deleteTenantConfig(User user, String key, String pkg) {
        pkg = Strings.isNullOrEmpty(pkg) ? PACKAGE : pkg;
        deleteConfig(user, key, false, pkg);
    }

    @Override
    public String findTenantConfig(User user, String key) {
        return findTenantConfig(user, key, PACKAGE);
    }

    @Override
    public String findTenantConfig(User user, String key, String pkg) {
        pkg = Strings.isNullOrEmpty(pkg) ? PACKAGE : pkg;
        return findConfig(user, key, false, pkg);
    }

    @Override
    public Map<String, String> queryTenantConfigs(User user, List<String> keys) {
        return queryTenantConfigs(user, keys, PACKAGE);
    }

    @Override
    public Map<String, String> queryTenantConfigs(User user, List<String> keys, String pkg) {
        pkg = Strings.isNullOrEmpty(pkg) ? PACKAGE : pkg;
        return queryConfigs(user, keys, false, pkg);
    }

    @Override
    public void createUserConfig(User user, String key, String value, ConfigValueType type) {
        createConfig(user, key, value, type, true, PACKAGE);
    }

    @Override
    public void updateUserConfig(User user, String key, String value, ConfigValueType type) {
        updateConfig(user, key, value, type, true, PACKAGE);
    }

    @Override
    public void upsertTenantConfig(User user, String key, String value, ConfigValueType type) {
        upsertConfig(user, key, value, type, PACKAGE, false);
    }

    @Override
    public void upsertUserConfig(User user, String key, String value, ConfigValueType type) {
        upsertConfig(user, key, value, type, PACKAGE, true);
    }

    private void upsertConfig(User user, String key, String value, ConfigValueType type, String pkg, boolean userLevel) {
        ConfigArg arg = getConfigArg(user, key, value, pkg);
        if (type == ConfigValueType.JSON) {
            arg.setValueType(ValueType.JSON);
        } else {
            arg.setValueType(ValueType.STRING);
        }
        if (userLevel) {
            arg.setUserId(user.getUserIdOrOutUserIdIfOutUser());
            arg.setRank(Rank.USER);
        } else {
            arg.setRank(Rank.TENANT);
        }
        try {
            int result = bizConfClient.upsertConfig(arg);
            if (result != 0) {
                log.error("upsertConfig failed! arg:{},result:{}", arg, result);
                throw new ConfigException(SystemErrorCode.CONFIG_UPDATE_ERROR);
            }
        } catch (ConfigException e) {
            throw e;
        } catch (Exception e) {
            log.error("upsertConfig failed! arg:{}", arg, e);
            throw new ConfigException(SystemErrorCode.CONFIG_UPDATE_ERROR, e);
        }

    }

    private ConfigArg getConfigArg(User user, String key, String value, String pkg) {
        return ConfigArg.builder().tenantId(user.getTenantId()).key(key).pkg(pkg).value(value).build();
    }

    @Override
    public void batchUpsertTenantConfig(User user, List<ConfigArg> configArgs) {
        if (CollectionUtils.isEmpty(configArgs)) {
            return;
        }
        batchUpsertConfig(user, configArgs, false, PACKAGE);
    }

    private void batchUpsertConfig(User user, List<ConfigArg> configArgs, boolean userLevel, String pkg) {
        configArgs.forEach(configArg -> {
            configArg.setRank(userLevel ? Rank.USER : Rank.TENANT);
            if (userLevel) {
                configArg.setUserId(user.getUserIdOrOutUserIdIfOutUser());
            }
            configArg.setTenantId(user.getTenantId());
            configArg.setPkg(pkg);
        });
        try {
            int result = bizConfClient.batchUpsertConfig(user.getTenantId(), configArgs);
            if (result != 0) {
                log.error("batchUpsertTenantConfig failed! arg:{},result:{}", configArgs, result);
                throw new ConfigException(SystemErrorCode.CONFIG_UPDATE_ERROR);
            }
        } catch (FRestClientException e) {
            log.error("batchUpsertTenantConfig failed! arg:{}", configArgs, e);
            throw new ConfigException(SystemErrorCode.CONFIG_UPDATE_ERROR, e);
        }
    }

    @Override
    public void deleteUserConfig(User user, String key) {
        deleteConfig(user, key, true, PACKAGE);
    }

    @Override
    public String findUserConfig(User user, String key) {
        return findConfig(user, key, true, PACKAGE);
    }

    @Override
    public Map<String, String> queryUserConfigs(User user, Collection<String> keys) {
        return queryConfigs(user, Lists.newArrayList(keys), true, PACKAGE);
    }

    @Override
    public void batchUpsertUserConfig(User user, List<ConfigArg> configArgs) {
        if (CollectionUtils.isEmpty(configArgs)) {
            return;
        }
        batchUpsertConfig(user, configArgs, true, PACKAGE);
    }


    private void createConfig(User user, String key, String value, ConfigValueType type, boolean userLevel,
                              String pkg) {
        log.debug("createTenantConfig user:{} key:{} value:{} type:{} userLevel:{} pkg:{}", user, key, value, type,
                userLevel, pkg);
        ConfigArg arg = ConfigArg.builder().tenantId(user.getTenantId()).key(key).pkg(pkg).value(value).build();
        if (type == ConfigValueType.JSON) {
            arg.setValueType(ValueType.JSON);
        } else {
            arg.setValueType(ValueType.STRING);
        }
        if (userLevel) {
            arg.setUserId(user.getUserIdOrOutUserIdIfOutUser());
            arg.setRank(Rank.USER);
        } else {
            arg.setRank(Rank.TENANT);
        }

        try {
            int result = bizConfClient.createConfig(arg);
            if (result != 0) {
                if (Objects.equals(result, 1)) {
                    //配置已存在
                    throw new ValidateException(I18NExt.text(I18NKey.CONFIG_ALREADY_EXIST));
                } else {
                    log.error("createTenantConfig failed! arg:{},result:{}", arg, result);
                    throw new ConfigException(SystemErrorCode.CONFIG_CREATE_ERROR);
                }
            }
        } catch (ValidateException | ConfigException e) {
            throw e;
        } catch (Exception e) {
            log.error("createTenantConfig failed! arg:{}", arg, e);
            throw new ConfigException(SystemErrorCode.CONFIG_CREATE_ERROR, e);
        }
    }


    private void updateConfig(User user, String key, String value, ConfigValueType type, boolean userLevel,
                              String pkg) {
        log.debug("updateTenantConfig user:{} key:{} value:{} type:{} userLevel:{} pkg:{}", user, key, value, type,
                userLevel, pkg);
        ConfigArg arg = ConfigArg.builder().tenantId(user.getTenantId()).key(key).pkg(pkg).value(value).build();
        if (type == ConfigValueType.JSON) {
            arg.setValueType(ValueType.JSON);
        } else {
            arg.setValueType(ValueType.STRING);
        }
        if (userLevel) {
            arg.setUserId(user.getUserIdOrOutUserIdIfOutUser());
            arg.setRank(Rank.USER);
        } else {
            arg.setRank(Rank.TENANT);
        }

        //String cacheKey = getCacheKey(user, key, userLevel);
        try {
            int result = bizConfClient.updateConfig(arg);
            if (result != 0) {
                log.error("updateTenantConfig failed! arg:{},result:{}", arg, result);
                throw new ConfigException(SystemErrorCode.CONFIG_UPDATE_ERROR);
            }
            //configLocalCache.invalidate(cacheKey);
        } catch (ConfigException e) {
            throw e;
        } catch (Exception e) {
            log.error("updateTenantConfig failed! arg:{}", arg, e);
            throw new ConfigException(SystemErrorCode.CONFIG_UPDATE_ERROR, e);
        }
    }


    private void deleteConfig(User user, String key, boolean userLevel, String pkg) {
        log.debug("deleteTenantConfig user:{} key:{} userLevel:{} pkg:{}", user, key, userLevel, pkg);
        DeleteConfigArg arg = DeleteConfigArg.builder().tenantId(user.getTenantId()).pkg(pkg).key(key).build();
        if (userLevel) {
            arg.setUserId(user.getUserIdOrOutUserIdIfOutUser());
            arg.setRank(Rank.USER);
        } else {
            arg.setRank(Rank.TENANT);
        }

        //String cacheKey = getCacheKey(user, key, userLevel);
        try {
            int result = bizConfClient.deleteConfig(arg);
            if (result != 0 && result != 1) {
                log.error("deleteTenantConfig failed! arg:{},result:{}", arg, result);
                throw new ConfigException(SystemErrorCode.CONFIG_DELETE_ERROR);
            }
            //configLocalCache.invalidate(cacheKey);
        } catch (ConfigException e) {
            throw e;
        } catch (Exception e) {
            log.error("deleteTenantConfig failed! arg:{}", arg, e);
            throw new ConfigException(SystemErrorCode.CONFIG_DELETE_ERROR, e);
        }
    }


    private String findConfig(User user, String key, boolean userLevel, String pkg) {
        log.debug("findConfig user:{} key:{} userLevel:{} pkg:{}", user, key, userLevel, pkg);
        QueryConfigByRankArg arg =
                QueryConfigByRankArg.builder().tenantId(user.getTenantId()).key(key).pkg(pkg).build();

        if (userLevel) {
            arg.setRank(Rank.USER);
            arg.setUserId(user.getUserIdOrOutUserIdIfOutUser());
        } else {
            arg.setRank(Rank.TENANT);
        }

        //String cacheKey = getCacheKey(user, key, userLevel);
        try {
            return bizConfClient.queryConfigByRank(arg);
            //String value = configLocalCache.getIfPresent(cacheKey);
//            if(Strings.isNullOrEmpty(value)) {
//                String valueFromServer = bizConfApi.queryConfigByRank(arg);
//                //configLocalCache.put(cacheKey, valueFromServer);
//                return valueFromServer;
//            } else {
//                return value;
//            }
        } catch (Exception e) {
            log.error("findConfig failed! arg:{}", arg, e);
            throw new ConfigException(SystemErrorCode.CONFIG_QUERY_ERROR, e);
        }
    }

//    private String getCacheKey(User user, String key, boolean userLevel) {
//        String cacheKey;
//        if (userLevel) {
//            cacheKey = String.format("user-%s-%s-%s", user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser(), key);
//        } else {
//
//            cacheKey = String.format("tenant-%s-%s", user.getTenantId(), key);
//        }
//        return cacheKey;
//    }

    private Map<String, String> queryConfigs(User user, List<String> keys, boolean userLevel, String pkg) {
        log.debug("queryConfigs user:{} key:{} userLevel:{} pkg:{}", user, keys, userLevel, pkg);
        BatchQueryConfigArg arg = BatchQueryConfigArg.builder()
                .tenantId(user.getTenantId())
                .keyList(keys)
                .pkg(pkg)
                .build();
        if (userLevel) {
            arg.setRank(Rank.USER);
            arg.setUserId(user.getUserIdOrOutUserIdIfOutUser());
        } else {
            arg.setRank(Rank.TENANT);
        }
        try {
            return bizConfClient.batchQueryConfig(arg);
        } catch (FRestClientException e) {
            log.error("QueryConfigs failed! arg:{}", arg, e);
            throw new ConfigException(SystemErrorCode.CONFIG_BATCH_QUERY_ERROR, e);
        }
    }
}
