package com.facishare.paas.appframework.core.util

import spock.lang.Specification
import spock.lang.Unroll

class FileSizeConverterTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试文件大小转换方法，验证不同大小范围的文件尺寸是否能正确转换为对应的单位(B/KB/MB/GB/TB)
     */
    @Unroll
    def "convertFileSizeTest: 文件大小=#fileSize, 期望结果=#expected"() {
        when: "调用文件大小转换方法"
        def result = FileSizeConverter.convertFileSize(fileSize as long)

        then: "验证转换结果是否符合预期"
        result == expected

        where: "测试不同范围的文件大小"
        fileSize                            | expected
        0L                                  | "0B"
        100L                                | "100B"
        1024L                               | "1KB"
        1536L                               | "1.5KB"
        1024L * 1024L                       | "1MB"
        1024L * 1024L * 2.5                 | "2.5MB"
        1024L * 1024L * 1024L               | "1GB"
        1024L * 1024L * 1024L * 5           | "5GB"
        1024L * 1024L * 1024L * 1024L       | "1TB"
        1024L * 1024L * 1024L * 1024L * 2.5 | "2.5TB"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界值和特殊情况的文件大小转换
     */
    @Unroll
    def "convertFileSizeTest_边界值: 文件大小=#fileSize, 期望结果=#expected"() {
        when: "调用文件大小转换方法"
        def result = FileSizeConverter.convertFileSize(fileSize)

        then: "验证转换结果是否符合预期"
        result == expected

        where: "测试边界值和特殊情况"
        fileSize                                   | expected
        1024L - 1L                                 | "1023B"
        1024L * 1024L - 1L                         | "1024KB"
        1024L * 1024L * 1024L - 1L                 | "1024MB"
        1024L * 1024L * 1024L * 1024L - 1L         | "1024GB"
        1024L * 1024L * 1024L * 1024L * 1024L - 1L | "1024TB"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试负数文件大小的情况
     */
    def "convertFileSizeTest_负数"() {
        when: "传入负数文件大小"
        FileSizeConverter.convertFileSize(-1L)

        then: "无异常抛出"
        noExceptionThrown()
    }
} 