package com.facishare.paas.appframework.core.util

import com.facishare.paas.appframework.core.exception.NotElementPresentException
import spock.lang.Specification
import spock.lang.Unroll

class ObjectUtilsTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试requireNotEmpty方法在输入为空时抛出异常
     */
    @Unroll
    def "requireNotEmptyTest should throw exception for empty input: #input"() {
        when:
        ObjectUtils.requireNotEmpty(input)

        then:
        thrown(NotElementPresentException)

        where:
        input << [null, "", []]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试requireNotEmpty方法在输入不为空时返回原值
     */
    @Unroll
    def "requireNotEmptyTest should return input for non-empty input: #input"() {
        when:
        def result = ObjectUtils.requireNotEmpty(input)

        then:
        result == input
        noExceptionThrown()

        where:
        input << ["test", [1, 2], new Object()]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带自定义错误消息的requireNotEmpty方法
     * 验证异常消息的正确性
     */
    def "requireNotEmptyTest with custom message"() {
        given:
        def customMessage = "Custom error message"

        when:
        ObjectUtils.requireNotEmpty(null, customMessage)

        then:
        def ex = thrown(NotElementPresentException)
        ex.message == customMessage
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带错误码的requireNotEmpty方法
     * 验证异常消息和错误码的正确性
     */
    def "requireNotEmptyTest with error code"() {
        given:
        def customMessage = "Custom error message"
        def errorCode = 1001

        when:
        ObjectUtils.requireNotEmpty(null, customMessage, errorCode)

        then:
        def ex = thrown(NotElementPresentException)
        ex.message == customMessage
        ex.errorCode == errorCode
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带消息提供者的requireNotEmpty方法
     * 验证使用Supplier提供的错误消息
     */
    def "requireNotEmptyTest with message supplier"() {
        given:
        def messageSupplier = { -> "Supplied error message" }

        when:
        ObjectUtils.requireNotEmpty(null, messageSupplier)

        then:
        def ex = thrown(NotElementPresentException)
        ex.message == "Supplied error message"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isNumber方法
     * 验证集合中的元素是否都可以转换为数字
     */
    @Unroll
    def "isNumberTest"() {
        expect:
        ObjectUtils.isNumber(input, { it.toString() }) == expected

        where:
        input          | expected
        null           | true
        []             | true
        ["1", "2"]     | true
        ["1", "abc"]   | false
        ["1.5", "2.0"] | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试humanReadableByteCount方法
     * 验证文件大小的人类可读格式转换
     */
    @Unroll
    def "humanReadableByteCountTest"() {
        expect:
        ObjectUtils.humanReadableByteCount(bytes, si) == expected

        where:
        bytes          | si    | expected
        0              | true  | "0 B"
        1000           | true  | "1.0 kB"
        1024           | false | "1.0 KiB"
        1_000_000      | true  | "1.0 MB"
        1_048_576      | false | "1.0 MiB"
        -1024          | false | "-1.0 KiB"
        Long.MAX_VALUE | true  | "9.2 EB"
    }
} 