package com.facishare.paas.appframework.core.util

import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.rest.CEPXHeader
import com.facishare.paas.appframework.core.rest.InnerHeaders
import spock.lang.Specification
import spock.lang.Unroll

class RestUtilsTest extends Specification {

    def cleanup() {
        RequestContextManager.setContext(null)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildHeaders方法，验证当传入不同的User对象时，生成的headers是否符合预期
     */
    @Unroll
    def "buildHeadersTest with different user info"() {
        given:
        def user = new User(tenantId, userId, outUserId, outTenantId, upstreamOwnerId)

        when:
        def headers = RestUtils.buildHeaders(user)

        then:
        headers[CEPXHeader.TENANT_ID.key()] == tenantId
        headers[InnerHeaders.TENANT_ID] == tenantId
        if (userId) {
            headers[CEPXHeader.USER_ID.key()] == userId
            headers[InnerHeaders.USER_ID] == userId
        }
        headers[CEPXHeader.OUT_TENANT_ID.key()] == outTenantId
        headers[InnerHeaders.OUT_TENANT_ID] == outTenantId
        headers[CEPXHeader.OUT_USER_ID.key()] == outUserId
        headers[InnerHeaders.OUT_USER_ID] == outUserId
        headers[CEPXHeader.UPSTREAM_OWNER_ID.key()] == upstreamOwnerId
        headers[InnerHeaders.UPSTREAM_OWNER_ID] == upstreamOwnerId

        where:
        tenantId | userId | outTenantId | outUserId | upstreamOwnerId
        "T1"     | "U1"   | "OT1"       | "OU1"     | "UO1"
        "T2"     | null   | "OT2"       | "OU2"     | "UO2"
        "T3"     | ""     | "OT3"       | "OU3"     | "UO3"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildHeaders方法，验证当传入describeApiName时，生成的headers是否包含对应的API名称
     */
    @Unroll
    def "buildHeadersTest with describeApiName"() {
        given:
        def user = new User("T1", "U1")

        when:
        def headers = RestUtils.buildHeaders(user, describeApiName)

        then:
        headers[InnerHeaders.OBJECT_API_NAME] == expectedApiName

        where:
        describeApiName | expectedApiName
        "testApi"       | "testApi"
        null            | null
        ""              | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildHeaders方法，验证当RequestContext包含各种属性时，生成的headers是否正确包含这些属性
     */
    def "buildHeadersTest with RequestContext attributes"() {
        given:
        def context = RequestContext.builder()
            .appId("app123")
            .modelName("model123")
            .peerHost("host123")
            .peerName("peer123")
            .peerDisplayName("displayName123")
            .peerReason("reason123")
            .eventId("event123")
            .ea("ea123")
            .bizId("biz123")
            .clientInfo("client123")
            .build()
        RequestContextManager.setContext(context)

        when:
        def headers = RestUtils.buildHeaders((User) null)

        then:
        headers[InnerHeaders.APP_ID] == "app123"
        headers[InnerHeaders.MODEL_NAME] == "model123"
        headers[InnerHeaders.PEER_HOST] == "host123"
        headers[InnerHeaders.PEER_NAME] == "peer123"
        headers[InnerHeaders.PEER_DISPLAY_NAME] == RequestUtil.encode("displayName123")
        headers[InnerHeaders.PEER_REASON] == RequestUtil.encode("reason123")
        headers[InnerHeaders.EVENT_ID] == "event123"
        headers[InnerHeaders.ENTERPRISE_ACCOUNT] == "ea123"
        headers[InnerHeaders.BIZ_ID] == "biz123"
        headers[InnerHeaders.CLIENT_INFO] == "client123"
        headers[CEPXHeader.CLIENT_INFO.key()] == "client123"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildQueryParams方法，验证是否正确构建查询参数
     */
    def "buildQueryParamsTest"() {
        given:
        def context = RequestContext.builder().build()
        context.setAttribute(RequestContext.Attributes.TRIGGER_FLOW, "true")
        context.setAttribute(RequestContext.Attributes.SKIP_BASE_VALIDATE, "false")
        context.setAttribute("not_validate", "1")
        context.setAttribute("invalid_param", "should_not_include")
        RequestContextManager.setContext(context)

        when:
        def queryParams = RestUtils.buildQueryParams()

        then:
        queryParams[RequestContext.Attributes.TRIGGER_FLOW] == "true"
        queryParams[RequestContext.Attributes.SKIP_BASE_VALIDATE] == "false"
        queryParams["not_validate"] == "1"
        !queryParams.containsKey("invalid_param")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildQueryParams方法，验证空值和null值的处理
     */
    def "buildQueryParamsTest with null and empty values"() {
        given:
        def context = RequestContext.builder().build()
        context.setAttribute(RequestContext.Attributes.TRIGGER_FLOW, null)
        context.setAttribute(RequestContext.Attributes.SKIP_BASE_VALIDATE, "")
        context.setAttribute("not_validate", "")
        RequestContextManager.setContext(context)

        when:
        def queryParams = RestUtils.buildQueryParams()

        then:
        !queryParams.containsKey(RequestContext.Attributes.TRIGGER_FLOW)
        queryParams[RequestContext.Attributes.SKIP_BASE_VALIDATE] == ""
        queryParams["not_validate"] == ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildFlowHeaders方法，验证是否正确构建flow相关的headers
     */
    def "buildFlowHeadersTest"() {
        given:
        def user = new User("T1", "U1")

        when:
        def headers = RestUtils.buildFlowHeaders(user)

        then:
        headers["x-tenant-id"] == "T1"
        headers["x-user-id"] == "U1"
        headers[CEPXHeader.TENANT_ID.key()] == "T1"
        headers[CEPXHeader.USER_ID.key()] == "U1"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildHeaders方法，验证当传入objectModule和更多RequestContext属性时，生成的headers是否正确
     */
    def "buildHeadersTest with objectModule and more context attributes"() {
        given:
        def user = new User("T1", "U1")
        def context = RequestContext.builder()
            .appId("app123")
            .modelName("model123")
            .outLinkType("outLink123")
            .lang(Lang.zh_CN)
            .upstreamOwnerId("upstream123")
            .outIdentityType("identity123")
            .thirdAppId("thirdApp123")
            .thirdUserId("thirdUser123")
            .thirdType("third123")
            .requestSource(RequestContext.RequestSource.CEP)
            .build()
        RequestContextManager.setContext(context)

        when:
        def headers = RestUtils.buildHeaders(user, "testApi", "testModule")

        then:
        headers[InnerHeaders.TENANT_ID] == "T1"
        headers[InnerHeaders.USER_ID] == "U1"
        headers[InnerHeaders.OBJECT_API_NAME] == "testApi"
        headers[InnerHeaders.OBJECT_MODULE] == "testModule"
        headers[InnerHeaders.OUT_LINK_TYPE] == "outLink123"
        headers[InnerHeaders.LOCALE] == "zh-CN"
        headers[InnerHeaders.UPSTREAM_OWNER_ID] == "upstream123"
        headers[InnerHeaders.OUT_IDENTITY_TYPE] == "identity123"
        headers[InnerHeaders.THIRD_APP_ID] == "thirdApp123"
        headers[InnerHeaders.THIRD_USER_ID] == "thirdUser123"
        headers[InnerHeaders.THIRD_TYPE] == "third123"
        headers[InnerHeaders.ORIGINAL_REQUEST_SOURCE] == "CEP"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildSendEmailHeaders方法，验证是否正确构建邮件发送相关的headers
     */
    @Unroll
    def "buildSendEmailHeadersTest"() {
        given:
        def user = new User(tenantId, userId, outUserId, outTenantId, upstreamOwnerId)

        when:
        def headers = RestUtils.buildSendEmailHeaders(user)

        then:
        headers["x-tenant-id"] == tenantId
        headers["x-user-id"] == expectedUserId
        headers[CEPXHeader.TENANT_ID.key()] == tenantId
        headers[CEPXHeader.USER_ID.key()] == userId
        headers[CEPXHeader.OUT_USER_ID.key()] == outUserId
        headers[CEPXHeader.OUT_TENANT_ID.key()] == outTenantId

        where:
        tenantId | userId | outUserId | outTenantId | upstreamOwnerId | expectedUserId
        "T1"     | "U1"   | null      | "OT1"       | "UO1"           | "U1"
        "T2"     | null   | "OU2"     | "OT2"       | "UO2"           | "OU2"
        "T3"     | "U3"   | "OU3"     | "OT3"       | "UO3"           | "OU3"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildQueryParams方法，验证多个有效参数组合的处理
     */
    @Unroll
    def "buildQueryParamsTest with multiple valid parameters"() {
        given:
        def context = RequestContext.builder().build()
        context.setAttribute(RequestContext.Attributes.TRIGGER_FLOW, triggerFlow)
        context.setAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW, triggerWorkFlow)
        context.setAttribute(RequestContext.Attributes.SKIP_FUNCTION_ACTION, skipFunctionAction)
        context.setAttribute(RequestContext.Attributes.SKIP_BUTTON_CONDITIONS, skipButtonConditions)
        context.setAttribute("isSpecifyTime", isSpecifyTime)
        context.setAttribute("isSpecifyCreatedBy", isSpecifyCreatedBy)
        RequestContextManager.setContext(context)

        when:
        def queryParams = RestUtils.buildQueryParams()

        then:
        queryParams[RequestContext.Attributes.TRIGGER_FLOW] == triggerFlow
        queryParams[RequestContext.Attributes.TRIGGER_WORK_FLOW] == triggerWorkFlow
        queryParams[RequestContext.Attributes.SKIP_FUNCTION_ACTION] == skipFunctionAction
        queryParams[RequestContext.Attributes.SKIP_BUTTON_CONDITIONS] == skipButtonConditions
        queryParams["isSpecifyTime"] == isSpecifyTime
        queryParams["isSpecifyCreatedBy"] == isSpecifyCreatedBy

        where:
        triggerFlow | triggerWorkFlow | skipFunctionAction | skipButtonConditions | isSpecifyTime | isSpecifyCreatedBy
        "true"      | "true"          | "true"            | "true"              | "true"        | "true"
        "false"     | "false"         | "false"           | "false"             | "false"       | "false"
        "true"      | "false"         | "true"            | "false"             | "true"        | "false"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildQueryParams方法，验证RequestContext为null时的处理
     */
    def "buildQueryParamsTest with null RequestContext"() {
        given:
        RequestContextManager.removeContext()

        when:
        def queryParams = RestUtils.buildQueryParams()

        then:
        queryParams.isEmpty()
    }
} 