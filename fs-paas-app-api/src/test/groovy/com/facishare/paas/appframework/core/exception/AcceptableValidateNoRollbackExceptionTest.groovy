package com.facishare.paas.appframework.core.exception

import spock.lang.Specification
import spock.lang.Unroll

class AcceptableValidateNoRollbackExceptionTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试AcceptableValidateNoRollbackException构造函数，验证异常对象创建后validateResult是否正确设置
     */
    @Unroll
    def "constructor test with different validateResults"() {
        when: "创建AcceptableValidateNoRollbackException实例"
        def exception = new AcceptableValidateNoRollbackException(validateResult)

        then: "验证validateResult是否正确设置"
        exception.validateResult == validateResult

        where: "不同的validateResult测试数据"
        validateResult << [
            "测试错误信息",
            123,
            new HashMap<String, String>(),
            null
        ]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AcceptableValidateNoRollbackException是否正确继承自AcceptableValidateException
     */
    def "inheritance test"() {
        given: "准备测试数据"
        def validateResult = "test result"

        when: "创建异常实例"
        def exception = new AcceptableValidateNoRollbackException(validateResult)

        then: "验证异常继承关系"
        exception instanceof AcceptableValidateException
    }
} 