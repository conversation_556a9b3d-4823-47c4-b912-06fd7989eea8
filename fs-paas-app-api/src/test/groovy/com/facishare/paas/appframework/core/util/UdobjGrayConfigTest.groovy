package com.facishare.paas.appframework.core.util

import com.fxiaoke.release.GrayRule
import spock.lang.Specification

/**
 * <AUTHOR> create by liy on 2025/5/20
 */
class UdobjGrayConfigTest extends Specification {

    def "test isAllow method"() {
        given: "setup test data"
        def rule = "test^rule"
        def euid = "test_euid"

        when: "call isAllow method"
        def result = UdobjGrayConfig.isAllow(rule, euid)

        then: "verify result"
        result != null
    }

    def "test isAllowByKeys method"() {
        given: "setup test data"
        def keys = ["key1", "key2"] as String[]
        def value = "test_value"

        when: "call isAllowByKeys method"
        def result = UdobjGrayConfig.isAllowByKeys(keys, value)

        then: "verify result"
        result != null
    }

    def "test isAllowByGrayRule method"() {
        given: "setup test data"
        def grayRule = new GrayRule("test_rule")
        def euid = "test_euid"

        when: "call isAllowByGrayRule method"
        def result = UdobjGrayConfig.isAllowByGrayRule(grayRule, euid)

        then: "verify result"
        result != null
    }

    def "test isAllowByGrayRule with null inputs"() {
        given: "setup test data"
        def grayRule = null
        def euid = "test_euid"

        when: "call isAllowByGrayRule method with null grayRule"
        def result = UdobjGrayConfig.isAllowByGrayRule(grayRule, euid)

        then: "verify result is false"
        !result
    }

    def "test isGrayWithDescribeApiName method"() {
        given: "setup test data"
        def grayRuleMap = [
            "test_api": new GrayRule("test_rule")
        ]
        def tenantId = "test_tenant"
        def objectApiName = "test_api"
        def supportDefault = true

        when: "call isGrayWithDescribeApiName method"
        def result = UdobjGrayConfig.isGrayWithDescribeApiName(grayRuleMap, tenantId, objectApiName, supportDefault)

        then: "verify result"
        result != null
    }

    def "test isGrayWithDescribeApiName with empty map"() {
        given: "setup test data"
        def grayRuleMap = [:]
        def tenantId = "test_tenant"
        def objectApiName = "test_api"
        def supportDefault = true

        when: "call isGrayWithDescribeApiName method with empty map"
        def result = UdobjGrayConfig.isGrayWithDescribeApiName(grayRuleMap, tenantId, objectApiName, supportDefault)

        then: "verify result is false"
        !result
    }

    def "test getWithDescribeApiName method"() {
        given: "setup test data"
        def paramMap = [
            "test_api": "test_value",
            "udobj": "udobj_value",
            "default": "default_value"
        ]
        def objectApiName = "test_api"
        def supportDefault = true

        when: "call getWithDescribeApiName method"
        def result = UdobjGrayConfig.getWithDescribeApiName(paramMap, objectApiName, supportDefault)

        then: "verify result"
        result == "test_value"
    }

    def "test getWithDescribeApiName with custom object"() {
        given: "setup test data"
        def paramMap = [
            "test_api__c": null,
            "udobj": "udobj_value",
            "default": "default_value"
        ]
        def objectApiName = "test_api__c"
        def supportDefault = true

        when: "call getWithDescribeApiName method with custom object"
        def result = UdobjGrayConfig.getWithDescribeApiName(paramMap, objectApiName, supportDefault)

        then: "verify result"
        result == "udobj_value"
    }

    def "test parseGrayRules method"() {
        given: "setup test data"
        def grayRuleValueMap = [
            "key1|key2": [
                "rule1": "value1",
                "rule2": "value2"
            ]
        ]

        when: "call parseGrayRules method"
        def result = UdobjGrayConfig.parseGrayRules(grayRuleValueMap)

        then: "verify result"
        result != null
        result.size() == 2
        result.containsKey("key1")
        result.containsKey("key2")
    }

    def "test parseGrayRulesWithMap method"() {
        given: "setup test data"
        def ruleValues = [
            "rule1|rule2": "value1"
        ]

        when: "call parseGrayRulesWithMap method"
        def result = UdobjGrayConfig.parseGrayRulesWithMap(ruleValues)

        then: "verify result"
        result != null
        result.size() == 2
        result.containsKey("rule1")
        result.containsKey("rule2")
    }

    def "test isCustomObject method"() {
        expect: "verify custom object detection"
        UdobjGrayConfig.isCustomObject("test__c")
        !UdobjGrayConfig.isCustomObject("test")
    }
}
