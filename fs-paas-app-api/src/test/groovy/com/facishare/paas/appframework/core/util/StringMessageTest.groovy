package com.facishare.paas.appframework.core.util

import com.facishare.paas.appframework.core.model.MessageResourceManager
import com.facishare.paas.appframework.core.model.MessageResource
import spock.lang.Specification
import spock.lang.Unroll

class StringMessageTest extends Specification {

    def messageResource = Mock(MessageResource)

    def setup() {
        MessageResourceManager.setMessageResource(messageResource)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取指定key的国际化消息
     */
    @Unroll
    def "getStringTest with key=#key should return #expected"() {
        given:
        messageResource.getString(key) >> expected

        when:
        def result = StringMessage.getString(key)

        then:
        result == expected

        where:
        key          | expected
        "test.key"   | "测试消息"
        "empty.key"  | ""
        "null.key"   | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取带参数的国际化消息
     */
    @Unroll
    def "getStringTest with key=#key and words=#words should return #expected"() {
        given:
        messageResource.getString(key, words as Object[]) >> expected

        when:
        def result = StringMessage.getString(key, words as Object[])

        then:
        result == expected

        where:
        key             | words                  | expected
        "welcome.msg"   | ["张三"]               | "欢迎张三"
        "user.info"     | ["李四", 25]           | "用户李四,年龄25岁"
        "empty.params"  | []                     | "无参数消息"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当MessageResource返回异常时的情况
     */
    def "getStringError when MessageResource throws exception"() {
        given:
        messageResource.getString(_ as String) >> { throw new RuntimeException("Resource not found") }

        when:
        StringMessage.getString("error.key")

        then:
        thrown(RuntimeException)
    }

    def cleanup() {
        MessageResourceManager.removeResource()
    }
} 