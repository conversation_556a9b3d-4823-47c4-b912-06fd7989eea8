package com.facishare.paas.appframework.core.i18n;

import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * I18NExt单元测试 - JUnit5版本
 * create by z<PERSON><PERSON> on 2020/09/24
 */
@ExtendWith(MockitoExtension.class)
class I18NExtTest {

    /**
     * GenerateByAI
     * 测试MessageFormat.format的正确性
     */
    @ParameterizedTest
    @MethodSource("provideMessageFormatTestData")
    @DisplayName("测试MessageFormat.format - 消息格式化")
    void testMessageFormat(String key, List<String> args, String expected) {
        // 执行被测试方法
        String result = MessageFormat.format(key, args.toArray(new String[0]));
        
        // 验证结果
        assertEquals(expected, result);
    }

    /**
     * 提供MessageFormat测试数据
     */
    private static Stream<Arguments> provideMessageFormatTestData() {
        return Stream.of(
            Arguments.of("没有{0}这个{1}", Arrays.asList("张三", "李四"), "没有张三这个李四"),
            Arguments.of("No {0}'s {1}", Arrays.asList("张三", "李四"), "No 张三s {1}"),
            Arguments.of("No {0}''s {1}", Arrays.asList("张三", "李四"), "No 张三's 李四")
        );
    }

    /**
     * GenerateByAI
     * 测试getLocalization方法，当传入正确的参数时，返回包含目标字符串的Localization
     */
    @Test
    @DisplayName("测试getLocalization - 成功找到匹配的Localization")
    void testGetLocalizationSuccess() {
        // 准备测试数据
        String targetString = "测试字符串";
        String ei = "123456";
        List<String> preKeys = Arrays.asList("key1", "key2");

        // 使用MockedStatic模拟静态方法
        try (MockedStatic<I18nClient> i18nClientMock = mockStatic(I18nClient.class)) {
            I18nClient mockI18nClient = mock(I18nClient.class);
            i18nClientMock.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            // 创建测试数据
            Localization localization1 = new Localization();
            Map<Byte, String> data1 = new HashMap<>();
            data1.put((byte) 1, "其他字符串");
            localization1.setData(data1);

            Localization localization2 = new Localization();
            Map<Byte, String> data2 = new HashMap<>();
            data2.put((byte) 1, "测试字符串");
            localization2.setData(data2);

            Map<String, Localization> localizationMap = new HashMap<>();
            localizationMap.put("key1", localization1);
            localizationMap.put("key2", localization2);

            when(mockI18nClient.get(preKeys, 123456L)).thenReturn(localizationMap);

            // 执行被测试方法
            Optional<Localization> result = I18NExt.getLocalization(targetString, ei, preKeys);

            // 验证结果
            assertTrue(result.isPresent());
            assertEquals(localization2, result.get());
        }
    }

    /**
     * GenerateByAI
     * 测试getLocalization方法，当未找到匹配的Localization时，返回empty
     */
    @Test
    @DisplayName("测试getLocalization - 未找到匹配的Localization")
    void testGetLocalizationNotFound() {
        // 准备测试数据
        String targetString = "测试字符串";
        String ei = "123456";
        List<String> preKeys = Arrays.asList("key1", "key2");

        // 使用MockedStatic模拟静态方法
        try (MockedStatic<I18nClient> i18nClientMock = mockStatic(I18nClient.class)) {
            I18nClient mockI18nClient = mock(I18nClient.class);
            i18nClientMock.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            // 创建测试数据 - 不包含目标字符串
            Localization localization1 = new Localization();
            Map<Byte, String> data1 = new HashMap<>();
            data1.put((byte) 1, "其他字符串1");
            localization1.setData(data1);

            Localization localization2 = new Localization();
            Map<Byte, String> data2 = new HashMap<>();
            data2.put((byte) 1, "其他字符串2");
            localization2.setData(data2);

            Map<String, Localization> localizationMap = new HashMap<>();
            localizationMap.put("key1", localization1);
            localizationMap.put("key2", localization2);

            when(mockI18nClient.get(preKeys, 123456L)).thenReturn(localizationMap);

            // 执行被测试方法
            Optional<Localization> result = I18NExt.getLocalization(targetString, ei, preKeys);

            // 验证结果
            assertFalse(result.isPresent());
        }
    }

    /**
     * GenerateByAI
     * 测试getLocalization方法，当ei不是数字时，默认使用0
     */
    @Test
    @DisplayName("测试getLocalization - ei不是数字时使用默认值0")
    void testGetLocalizationInvalidEi() {
        // 准备测试数据
        String targetString = "测试字符串";
        String ei = "not_a_number";
        List<String> preKeys = Arrays.asList("key1");

        // 使用MockedStatic模拟静态方法
        try (MockedStatic<I18nClient> i18nClientMock = mockStatic(I18nClient.class)) {
            I18nClient mockI18nClient = mock(I18nClient.class);
            i18nClientMock.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            // 创建测试数据
            Localization localization = new Localization();
            Map<Byte, String> data = new HashMap<>();
            data.put((byte) 1, "测试字符串");
            localization.setData(data);

            Map<String, Localization> localizationMap = new HashMap<>();
            localizationMap.put("key1", localization);

            // 验证使用0L作为参数
            when(mockI18nClient.get(preKeys, 0L)).thenReturn(localizationMap);

            // 执行被测试方法
            Optional<Localization> result = I18NExt.getLocalization(targetString, ei, preKeys);

            // 验证结果
            assertTrue(result.isPresent());
            assertEquals(localization, result.get());
            
            // 验证调用了正确的参数
            verify(mockI18nClient).get(preKeys, 0L);
        }
    }

    /**
     * GenerateByAI
     * 测试getLocalization方法，当I18nClient抛出异常时，返回empty
     */
    @Test
    @DisplayName("测试getLocalization - I18nClient抛出异常时返回empty")
    void testGetLocalizationException() {
        // 准备测试数据
        String targetString = "测试字符串";
        String ei = "123456";
        List<String> preKeys = Arrays.asList("key1");

        // 使用MockedStatic模拟静态方法
        try (MockedStatic<I18nClient> i18nClientMock = mockStatic(I18nClient.class)) {
            I18nClient mockI18nClient = mock(I18nClient.class);
            i18nClientMock.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            // 模拟抛出异常
            when(mockI18nClient.get(preKeys, 123456L)).thenThrow(new RuntimeException("模拟异常"));

            // 执行被测试方法
            Optional<Localization> result = I18NExt.getLocalization(targetString, ei, preKeys);

            // 验证结果
            assertFalse(result.isPresent());
        }
    }

    /**
     * GenerateByAI
     * 测试getLocalization方法，当返回的localizationMap为null时，返回empty
     */
    @Test
    @DisplayName("测试getLocalization - localizationMap为null时返回empty")
    void testGetLocalizationNullMap() {
        // 准备测试数据
        String targetString = "测试字符串";
        String ei = "123456";
        List<String> preKeys = Arrays.asList("key1");

        // 使用MockedStatic模拟静态方法
        try (MockedStatic<I18nClient> i18nClientMock = mockStatic(I18nClient.class)) {
            I18nClient mockI18nClient = mock(I18nClient.class);
            i18nClientMock.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            // 模拟返回null
            when(mockI18nClient.get(preKeys, 123456L)).thenReturn(null);

            // 执行被测试方法
            Optional<Localization> result = I18NExt.getLocalization(targetString, ei, preKeys);

            // 验证结果
            assertFalse(result.isPresent());
        }
    }

    /**
     * GenerateByAI
     * 测试getLocalization方法，当preKeys为空时的处理
     */
    @Test
    @DisplayName("测试getLocalization - preKeys为空时的处理")
    void testGetLocalizationEmptyPreKeys() {
        // 准备测试数据
        String targetString = "测试字符串";
        String ei = "123456";
        List<String> preKeys = Collections.emptyList();

        // 使用MockedStatic模拟静态方法
        try (MockedStatic<I18nClient> i18nClientMock = mockStatic(I18nClient.class)) {
            I18nClient mockI18nClient = mock(I18nClient.class);
            i18nClientMock.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            // 模拟返回空Map
            when(mockI18nClient.get(preKeys, 123456L)).thenReturn(Collections.emptyMap());

            // 执行被测试方法
            Optional<Localization> result = I18NExt.getLocalization(targetString, ei, preKeys);

            // 验证结果
            assertFalse(result.isPresent());
        }
    }
} 