package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.core.exception.InitHandlerException;
import com.facishare.paas.appframework.core.util.HandlerGrayConfig;
import com.facishare.paas.appframework.core.util.Types;
import com.fxiaoke.common.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Supplier;

/**
 * Action 抽象类
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
public abstract class AbstractAction<A, R> implements Action<A, R> {

    protected Logger log = LoggerFactory.getLogger(getClass());

    protected Class<A> argClass;
    protected A arg;
    protected ActionContext actionContext;
    private boolean doActComplete;
    private boolean processComplete;

    public AbstractAction() {
        argClass = Types.detectFirstGenericArgType(getClass());
    }

    protected abstract R doAct(A arg);

    protected void before(A arg) {

    }

    protected R after(A arg, R result) {
        return result;
    }

    protected void finallyDo() {

    }

    protected R buildValidateResult() {
        return null;
    }


    /**
     * @return 本次请求是否需要创建批量任务
     */
    protected boolean isBatchAction() {
        return false;
    }

    protected void pluginInit(A arg) {

    }

    protected void pluginBefore(A arg) {

    }

    protected void pluginAfter(A arg, R result) {

    }

    protected void pluginFinallyDo(A arg, R result) {

    }

    protected void domainPluginBefore(A arg) {

    }

    protected void domainPluginPreAct(A arg) {

    }

    protected void domainPluginPostAct(A arg, R result) {

    }

    protected void domainPluginAfter(A arg, R result) {

    }

    protected void domainPluginFinallyDo(A arg, R result) {

    }

    protected boolean customSkipHandler() {
        return false;
    }

    protected void handlerInit() {

    }

    protected void handlerBefore() {

    }

    protected R handlerDoAct() {
        return null;
    }

    protected void handlerAfter() {

    }

    protected void handlerFinallyDo() {

    }

    protected abstract R callWithGlobalTransaction(Supplier<R> supplier);

    @Override
    public R act(A arg) {
        if (supportHandler()) {
            return actByHandler(arg);
        }
        return actByAction(arg);
    }

    private R actByHandler(A arg) {
        StopWatch sw = StopWatch.createStarted(getClass().getSimpleName() + ".actByHandler()");
        try {
            handlerInit();
            sw.lap("handlerInit");
        } catch (InitHandlerException e) {
            log.warn("initHandler failed,tenantId:{},objectApiName:{},interfaceCode:{} ", actionContext.getTenantId(),
                    actionContext.getObjectApiName(), actionContext.getActionCode(), e);
            return actByAction(arg);
        }
        try {
            handlerBefore();
            sw.lap("handlerBefore");
            R result = callWithGlobalTransaction(() -> {
                R ret = handlerDoAct();
                this.doActComplete = true;
                sw.lap("handlerDoAct");
                return ret;
            });
            handlerAfter();
            sw.lap("handlerAfter");
            this.processComplete = true;
            log.debug("Action arg:{},result:{}", arg, result);
            return result;
        } finally {
            handlerFinallyDo();
            sw.lap("handlerFinallyDo");
            finallyDo();
            sw.lap("finallyDo");
            sw.logSlow(500);
        }
    }

    protected abstract void registerGlobalTransactionHook();

    private R actByAction(A arg) {
        StopWatch sw = StopWatch.createStarted(getClass().getSimpleName() + ".actByAction()");
        R result = null;
        try {
            checkArg(arg);
            sw.lap("checkArg");
            processArg(arg);
            sw.lap("processArg");
            pluginInit(arg);
            sw.lap("pluginInit");
            before(arg);
            sw.lap("before");
            pluginBefore(arg);
            sw.lap("pluginBefore");
            domainPluginBefore(arg);
            sw.lap("domainPluginBefore");
            preAction(arg);
            sw.lap("preAction");
            domainPluginPreAct(arg);
            sw.lap("domainPluginPreAct");
            result = doAct(arg);
            this.doActComplete = true;
            sw.lap("doAct");
            domainPluginPostAct(arg, result);
            sw.lap("domainPluginPostAct");
            after(arg, result);
            sw.lap("after");
            pluginAfter(arg, result);
            sw.lap("pluginAfter");
            domainPluginAfter(arg, result);
            sw.lap("domainPluginAfter");
            postAction(arg, result);
            sw.lap("postAction");
            processResult(result);
            sw.lap("processResult");
            this.processComplete = true;
            log.debug("Action arg:{},result:{}", arg, result);
            return result;
        } finally {
            pluginFinallyDo(arg, result);
            sw.lap("pluginFinallyDo");
            domainPluginFinallyDo(arg, result);
            sw.lap("domainPluginFinallyDo");
            finallyDo();
            sw.lap("finallyDo");
            sw.logSlow(500);
        }
    }

    protected void processResult(R result) {

    }

    protected void checkArg(A arg) {

    }

    protected void processArg(A arg) {
    }

    protected void preAction(A arg) {

    }

    protected void postAction(A arg, R result) {

    }

    public Class<A> getArgClass() {
        return argClass;
    }

    public A getArg() {
        return arg;
    }

    public void setArg(A arg) {
        this.arg = arg;
    }

    public ActionContext getActionContext() {
        return actionContext;
    }

    public void setActionContext(ActionContext actionContext) {
        this.actionContext = actionContext;
    }

    protected final boolean supportHandler() {
        if (customSkipHandler()) {
            return false;
        }
        return HandlerGrayConfig.supportHandler(actionContext.getTenantId(), actionContext.getActionCode(), actionContext.getObjectApiName());
    }

    protected final boolean isDoActComplete() {
        return doActComplete;
    }

    protected final boolean isProcessComplete() {
        return processComplete;
    }

}
