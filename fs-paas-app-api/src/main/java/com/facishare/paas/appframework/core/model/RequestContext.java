package com.facishare.paas.appframework.core.model;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.Lang;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.time.ZoneId;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 请求上下文
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
@Getter
@ToString(doNotUseGetters = true)
@JsonIgnoreProperties({"timeZone"})
public class RequestContext {

    private static final String REQUEST_URI = "requestUri";

    /**
     * 标记：调用元数据时是否直接从国际化平台查询名称
     */
    public static final String DIRECT_KEY = "direct";

    public static final String IS_NOT_NEED_DEEP_COPY = "not_need_deep_copy";

    /**
     * 是否是上游复制到下游需求
     */
    public static final String UPSTREAM_COPY_DESCRIBE = "upstream_copy_describe";

    /**
     * 请求代表的租户
     */
    private final String tenantId;

    /**
     * 请求上下文中的认证用户，不是所有请求中都有认证用户
     */
    private final User user;

    /**
     * 请求内容格式
     */
    @Setter
    private ContentType contentType;

    /**
     * 请求ID用于请求去重
     */
    private final String postId;

    /**
     * 终端类型及版本信息
     */
    private final String clientInfo;

    private final Map<String, Object> attributes = Maps.newHashMap();

    /**
     * 请求来源，处理异常的时候用来区分返回数据的格式
     */
    private final RequestSource requestSource;

    /**
     * rest请求中调用方地址
     */
    private final String peerHost;
    /**
     * rest请求中调用的业务方
     */
    private final String peerName;

    /**
     * 以后统一使用的peerName
     */
    private final String x_peerName;

    /**
     * 程序调用的RPC深度
     */
    private int stackDepth;

    /**
     * Event Id
     */
    private final String eventId;

    /**
     * 应用Id
     */
    private final String appId;

    /**
     * 互联类型 1: 企业互联 2: 客户互联
     */
    private final String outLinkType;

    /**
     * 语言
     */
    private final Lang lang;

    /**
     * 时区
     */
    private final ZoneId timeZone;

    private boolean batch;

    private final String peerReason;

    private final String peerDisplayName;

    private final String peerDisplayNameI18nKey;

    private final String modelName;

    private final String clientIp;

    private final String upstreamOwnerId;

    private final String bizId;
    /**
     * 企业账号
     */
    private final String ea;

    private final String sessionId;

    private BizInfo bizInfo;

    private boolean paramsIdempotent;

    private String outIdentityType;

    private String thirdAppId;
    private String thirdUserId;
    private String thirdType;

    //判断请求是从详情页布局进来的还是导入导出，打印进来的，用来控制引用字段引用部门展示（已停用）
    @Setter
    private String apiResource;

    @Builder
    private RequestContext(String tenantId, User user, ContentType contentType, String postId,
                           String clientInfo, RequestSource requestSource, String peerHost, String peerName, String eventId,
                           String appId, String outLinkType, Lang lang, String timeZone, Boolean batch, String requestUri, String peerReason, String peerDisplayName,
                           String modelName, String clientIp, String upstreamOwnerId, String ea, String bizId, String sessionId,
                           boolean paramsIdempotent, String outIdentityType, String thirdAppId, String thirdUserId, String thirdType,
                           String x_peerName, String apiResource, String peerDisplayNameI18nKey) {
        this(null, tenantId, user, contentType, postId, clientInfo, requestSource, peerHost, peerName, eventId, appId, outLinkType,
                lang, timeZone, batch, requestUri, peerReason, peerDisplayName, modelName, clientIp, upstreamOwnerId, ea,
                bizId, sessionId, paramsIdempotent, outIdentityType, thirdAppId, thirdUserId, thirdType, x_peerName, apiResource, peerDisplayNameI18nKey);
    }

    @JsonCreator
    private RequestContext(@JsonProperty("attributes") Map<String, Object> attributes, @JsonProperty("tenantId") String tenantId,
                           @JsonProperty("user") User user, @JsonProperty("contentType") ContentType contentType,
                           @JsonProperty("postId") String postId, @JsonProperty("clientInfo") String clientInfo,
                           @JsonProperty("requestSource") RequestSource requestSource, @JsonProperty("peerHost") String peerHost,
                           @JsonProperty("peerName") String peerName, @JsonProperty("eventId") String eventId,
                           @JsonProperty("appId") String appId, @JsonProperty("outLinkType") String outLinkType, @JsonProperty("lang") Lang lang,
                           @JsonProperty("timeZone") String timeZone, @JsonProperty("batch") Boolean batch,
                           @JsonProperty("requestUri") String requestUri, @JsonProperty("peerReason") String peerReason,
                           @JsonProperty("peerDisplayName") String peerDisplayName,
                           @JsonProperty("modelName") String modelName,
                           @JsonProperty("clientIp") String clientIp, @JsonProperty("upstreamOwnerId") String upstreamOwnerId,
                           @JsonProperty("ea") String ea, @JsonProperty("bizId") String bizId,
                           @JsonProperty("sessionId") String sessionId, @JsonProperty("paramsIdempotent") boolean paramsIdempotent,
                           @JsonProperty("outIdentityType") String outIdentityType, @JsonProperty("thirdAppId") String thirdAppId,
                           @JsonProperty("thirdUserId") String thirdUserId, @JsonProperty("thirdType") String thirdType,
                           @JsonProperty("x_peerName") String x_peerName, @JsonProperty("apiResource") String apiResource,
                           @JsonProperty("peerDisplayNameI18nKey") String peerDisplayNameI18nKey) {
        if (Objects.nonNull(attributes)) {
            this.attributes.putAll(attributes);
        }
        this.tenantId = tenantId;
        this.user = user;
        this.contentType = contentType;
        this.postId = postId;
        this.clientInfo = clientInfo;
        this.requestSource = requestSource;
        this.peerHost = peerHost;
        this.peerName = peerName;
        this.eventId = eventId;
        this.appId = appId;
        this.outLinkType = outLinkType;
        this.lang = Objects.isNull(lang) ? Lang.zh_CN : lang;
        this.timeZone = Strings.isNullOrEmpty(timeZone) ? null : ZoneId.of(timeZone);
        //CEP请求写死batch=false，其他的请求默认都是true，除非手动传false
        this.batch = !Boolean.FALSE.equals(batch);
        this.peerReason = peerReason;
        this.modelName = modelName;
        this.peerDisplayName = peerDisplayName;
        this.peerDisplayNameI18nKey = peerDisplayNameI18nKey;
        this.clientIp = clientIp;
        this.upstreamOwnerId = upstreamOwnerId;
        this.ea = ea;
        this.bizId = bizId;
        this.sessionId = sessionId;
        this.paramsIdempotent = paramsIdempotent;
        this.outIdentityType = outIdentityType;
        this.thirdAppId = thirdAppId;
        this.thirdUserId = thirdUserId;
        this.thirdType = thirdType;
        if (!Strings.isNullOrEmpty(clientInfo)) {
            setAttribute(CLIENT_INFO, clientInfo);
        }
        if (!Strings.isNullOrEmpty(requestUri)) {
            setAttribute(REQUEST_URI, requestUri);
        }
        this.x_peerName = x_peerName;
        this.apiResource = apiResource;
    }

    @JsonIgnore
    public boolean isAppModuleRequest() {
        return !Strings.isNullOrEmpty(appId);
    }

    public <V> V getAttribute(String key) {
        return (V) attributes.get(key);
    }

    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }

    public void removeAttribute(String key) {
        attributes.remove(key);
    }

    public User getUser() {
        return getUserOpt().orElseThrow(() -> new PermissionError(I18N.text(I18NKey.PERMISSION_ERROR)));
    }

    public User getUserOrElse(Supplier<User> other) {
        return getUserOpt().orElseGet(other);
    }

    private Optional<User> getUserOpt() {
        return Optional.ofNullable(user);
    }

    public void setBatch(boolean batch) {
        this.batch = batch;
    }

    public void setBizInfo(BizInfo bizInfo) {
        this.bizInfo = bizInfo;
    }

    public void setStackDepth(int stackDepth) {
        this.stackDepth = stackDepth;
    }

    public static final String CLIENT_INFO = "client_info";

    public static final String Android_CLIENT_INFO_PREFIX = "Android";

    public static final String IOS_CLIENT_INFO_PREFIX = "iOS";

    // 云之家
    public static final String WEB_CLOUD_HUB_CLIENT_INFO_PREFIX = "CloudHub";
    // 企业微信
    public static final String WEB_WX_WORK_CLIENT_INFO_PREFIX = "WXwork";
    // 微信
    public static final String WEB_WX_CLIENT_INFO_PREFIX = "WX";
    // 纷享客户端
    public static final String WEB_FS_BROWSER = "FSBrowser";
    // 微信小程序
    public static final String WEB_WX_MINI_PROGRAM = "WEB.WXMiniProgram";

    public static final String OPENAPI_PEER_NAME = "OpenAPI";

    public static final String OPENAPI_PEER_NAME_TWO = "OpenAPI-V2.0";

    public static final String SMARTFORM_PEER_NAME = "SmartForm";

    public static final String BULK_ACTION_CALL_BACK = "actionCallBack";

    public static final String FUNCTION_PEER_NAME = "function";

    public static final String FS_DEVICE_TYPE = "fs-device-type";
    public static final String FS_DEVICE_TYPE_MOBILE = "mobile";
    public static final String FS_DEVICE_ID = "x-fs-device-id";
    /**
     * sfa 会员小程序token
     */
    public static final String FS_MARKETING_MEMBER = "memberid";

    /**
     * 请求来自openAPI
     *
     * @return
     */
    @JsonIgnore
    public boolean isFromOpenAPI() {
        return StringUtils.startsWith(getPeerName(), OPENAPI_PEER_NAME) ||
                StringUtils.startsWith(getPeerName(), OPENAPI_PEER_NAME_TWO);
    }

    /**
     * 请求来自智能表单
     *
     * @return
     */
    @JsonIgnore
    public boolean isFromSmartForm() {
        return SMARTFORM_PEER_NAME.equalsIgnoreCase(getPeerName())
                || StringUtils.contains(getPeerName(), "fs-crm-smartform");
    }

    @JsonIgnore
    public boolean isFromFunction() {
        if (Strings.isNullOrEmpty(getPeerName())) {
            return false;
        }
        return getPeerName().contains(FUNCTION_PEER_NAME);
    }

    @JsonIgnore
    public boolean isBulkActionCallBack() {
        return BULK_ACTION_CALL_BACK.equals(getPeerName())
                || StringUtils.contains(getPeerName(), "fs-paas-app-task");
    }

    public boolean needCheckDuplicateSearch() {
        final Boolean attribute = getAttribute(Attributes.CHECK_DUPLICATE_SEARCH);
        if (Objects.nonNull(attribute)) {
            return attribute;
        }
        return !isFromSmartForm() && !isFromOpenAPI();
    }

    public boolean needCheckUniquenessRule() {
        final Boolean attribute = getAttribute(Attributes.CHECK_UNIQUENESS_RULE);
        if (Objects.nonNull(attribute)) {
            return attribute;
        }
        return isFromSmartForm() || isFromOpenAPI();
    }

    public void setFromManage(boolean fromManage) {
        setAttribute(Attributes.FROM_MANAGE, fromManage);
    }

    public boolean isFromManage() {
        return Boolean.TRUE.equals(getAttribute(Attributes.FROM_MANAGE));
    }

    public void skipUniqueRuleCheck() {
        setAttribute(Attributes.CHECK_UNIQUENESS_RULE, false);
    }

    public void skipDuplicatedRuleCheck() {
        setAttribute(Attributes.CHECK_DUPLICATE_SEARCH, false);
    }

    public boolean needTriggerApprovalFlow() {
        return !Boolean.FALSE.equals(getAttribute(Attributes.TRIGGER_FLOW));
    }

    public boolean needTriggerWorkFlow() {
        return !Boolean.FALSE.equals(getAttribute(Attributes.TRIGGER_WORK_FLOW));
    }

    public boolean skipBaseValidate() {
        return Boolean.TRUE.equals(getAttribute(Attributes.SKIP_BASE_VALIDATE));
    }

    public boolean skipImmutableFieldValidate() {
        return Boolean.TRUE.equals(getAttribute(Attributes.SKIP_IMMUTABLE_FIELD_VALIDATE));
    }

    public enum ContentType {
        SIMPLE_JSON,
        FULL_JSON,
        NONNULL_SIMPLE_JSON,
        NONNULL_FULL_JSON;

        public ContentType convertToNonnullType() {
            switch (this) {
                case SIMPLE_JSON:
                    return NONNULL_SIMPLE_JSON;
                case FULL_JSON:
                    return NONNULL_FULL_JSON;
                default:
                    return this;
            }
        }

        public static ContentType getType(String contentType) {
            return getType(contentType, true);
        }

        public static ContentType getType(String contentType, boolean serializeEmpty) {
            if (Strings.isNullOrEmpty(contentType)) {
                return FULL_JSON;
            }
            String formatted = Splitter.on(';').trimResults().splitToList(contentType).get(0).toLowerCase();
            ContentType type = map.get(formatted);
            type = Objects.nonNull(type) ? type : FULL_JSON;

            //不返回空值需要转换一下
            if (!serializeEmpty) {
                type = type.convertToNonnullType();
            }

            return type;
        }

        private static Map<String, ContentType> map = Maps.newHashMap();

        static {
            map.put("application/json", FULL_JSON);
            map.put("application/vnd.fs+json", SIMPLE_JSON);
            map.put("application/simplejson", SIMPLE_JSON);
        }
    }

    public enum RequestSource {
        CEP, INNER, REST
    }

    public interface Attributes {
        String DEBUG = "debug";
        String TRIGGER_FLOW = "triggerFlow";
        String TRIGGER_WORK_FLOW = "triggerWorkflow";
        String TRIGGER_WORK_FLOW_OPEN_API = "triggerWorkFlow";
        //是否使用快照数据
        String USE_SNAPSHOT_FOR_APPROVAL = "useSnapshotForApproval";
        //修改记录是否隐藏
        String MODIFY_LOG_HIDDEN = "modifyLogHidden";
        String SKIP_BASE_VALIDATE = "skipBaseValidate";
        // 跳过按钮前后动作函数
        String SKIP_FUNCTION_ACTION = "skipFunctionAction";
        // 跳过按钮显示条件校验
        String SKIP_BUTTON_CONDITIONS = "skipButtonConditions";
        // 更新时，跳过必填字段的校验
        String NOT_VALIDATE = "notValidate";
        //是否开启缓存
        String OPEN_CONTEXT_CACHE = "OPEN_CONTEXT_CACHE";
        // 增量更新是否需要校验数据权限
        String APPLY_DATA_PRIVILEGE_CHECK = "applyDataPrivilegeCheck";
        // trigger invoke
        String TRIGGER_INVOKE = "trigger_invoke";
        // 强制判断redis中是否有最近变更标识，如果有则在pg查询数据
        String ES_RECENT_UPDATE = "esRecentUpdate";
        // 工商查询的数据源
        String INDUSTRY_DATASOURCE = "industry_datasource";
        String INDUSTRY_SUPPLY_TAG = "industry_supply_tag";

        // 唯一性规则
        String CHECK_UNIQUENESS_RULE = "checkUniquenessRule";
        // 查重规则
        String CHECK_DUPLICATE_SEARCH = "checkDuplicateSearch";
        //不查相关团队
        String SKIP_RELEVANT_TEAM = "skipRelevantTeam";
        // 按钮函数支持幂等
        String FUNCTION_IDEMPOTENT = "functionIdempotent";

        String UPDATE_ORIGIN_SOURCE = "update_origin_source";

        //是否计算服务
        String IS_CALCULATE_CONTEXT = "isCalculateContext";
        /**
         * 查询描述不走缓存
         */
        String DESCRIBE_SKIP_STATIC = "skip_static";
        /**
         * id > orderBy id 时走 searchAfter
         */
        String ES_CURSOR_SEARCH = "esCursorSearch";
        String ACTION_CODE = "action_code";
        String RECORD_CALCULATE_LOG = "record_calculate_log";
        String SKIP_INIT_INTERNATIONAL_CONTEXT = "skipInternationalInitContext";
        /**
         * 当此属性被设置为true时，表示应跳过对不可变字段的验证。
         */
        String SKIP_IMMUTABLE_FIELD_VALIDATE = "skipImmutableFieldValidate";
        /*
         * 透传是否来自管理后台的flag
         */
        String FROM_MANAGE = "fromManage";

        /**
         * 是否需要预匹配审批流程
         */
        String PRE_MATCH_APPROVAL = "preMatchApproval";
    }

    public interface CacheKeys {
        //功能权限
        String FUNCTION_PRIVILEGE = "FP_%s_%s";
        //数据权限
        String DATA_PRIVILEGE = "DP_%s_%s";
        //字段权限
        String FIELD_PERMISSION = "FIP_%s_%s";
        //对象描述
        String OBJECT_DESCRIBE = "OD_%s_%s";
        //个人场景配置
        String USER_SCENE_CONFIG = "USC_%s_%s";
        // 企业币种
        String EXCHANGE_RATES = "ExchangeRates_%s";
    }

    @Data
    public static class PeerName {
        String id;
        String name;
        public static Map<String, PeerName> peerNameMap;

        static {
            ConfigFactory.getConfig("fs-paas-appframework-peer-name", (config) -> {
                List<PeerName> peerName = JSON.parseArray(config.get("peerName"), PeerName.class);
                peerNameMap = peerName.stream().collect(Collectors.toMap(PeerName::getId, x -> x));
            });
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BizInfo {
        //业务方代码
        private String biz;
        //业务id
        private String bizId;
        //业务附加id
        private String otherBizId;
    }

    public enum Biz {
        ApprovalFlow("approvalflow"), WorkFlow("workflow"), WorkFlowBpm("workflow_bpm"), Stage("stage");

        private String code;

        Biz(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public Biz getByCode(String code) {
            return Arrays.stream(Biz.values()).filter(x -> x.getCode().equals(code)).findFirst().orElse(null);
        }
    }

}
