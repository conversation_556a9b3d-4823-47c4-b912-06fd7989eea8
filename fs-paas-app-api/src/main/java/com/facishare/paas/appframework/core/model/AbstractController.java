package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.core.exception.InitHandlerException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.HandlerGrayConfig;
import com.facishare.paas.appframework.core.util.ObjectUtils;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.Types;
import com.fxiaoke.common.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Abstract controller
 * <p>
 * Created by liyiguang on 2017/7/3.
 */
public abstract class AbstractController<A, R> implements Controller<A, R> {

    protected Logger log = LoggerFactory.getLogger(getClass());

    protected Class<A> argClass;
    protected A arg;
    protected R result;
    protected ControllerContext controllerContext;
    private boolean doActComplete;
    private boolean processComplete;

    public AbstractController() {
        argClass = Types.detectFirstGenericArgType(getClass());
    }

    protected abstract R doService(A arg);

    protected void before(A arg) {

    }

    protected abstract void pluginBefore(A arg);

    protected abstract void pluginAfter(A arg, R result);

    protected void domainPluginBefore(A arg) {

    }

    protected void domainPluginAfter(A arg, R result) {

    }

    protected R after(A arg, R result) {
        return result;
    }

    protected void finallyDo() {

    }

    protected void handlerInit() {

    }

    protected void handlerBefore() {

    }

    protected void handlerDoAct() {

    }

    protected void handlerAfter() {

    }

    protected void handlerFinallyDo() {

    }

    protected boolean customSkipHandler() {
        return false;
    }

    protected final boolean supportHandler() {
        if (customSkipHandler()) {
            return false;
        }
        return HandlerGrayConfig.supportHandler(controllerContext.getTenantId(), controllerContext.getMethodName(),
                controllerContext.getObjectApiName());
    }

    @Override
    public final R service(A arg) {
        ObjectUtils.requireNotEmpty(arg, I18NExt.text(I18NKey.PARAM_EMPTY));
        RequestUtil.setRecordCalculateLog(false);
        if (supportHandler()) {
            return actByHandler(arg);
        }
        return actByController(arg);
    }

    private R actByHandler(A arg) {
        StopWatch sw = StopWatch.createStarted(getClass().getSimpleName() + ".actByHandler()");
        try {
            handlerInit();
            sw.lap("handlerInit");
        } catch (InitHandlerException e) {
            log.warn("initHandler failed,tenantId:{},objectApiName:{},interfaceCode:{} ", controllerContext.getTenantId(),
                    controllerContext.getObjectApiName(), controllerContext.getMethodName(), e);
            return actByController(arg);
        }
        try {
            handlerBefore();
            sw.lap("handlerBefore");
            handlerDoAct();
            this.doActComplete = true;
            sw.lap("handlerDoAct");
            handlerAfter();
            sw.lap("handlerAfter");
            this.processComplete = true;
            log.debug("Controller arg:{},result:{}", arg, result);
            return result;
        } finally {
            handlerFinallyDo();
            sw.lap("handlerFinallyDo");
            sw.logSlow(500);
        }
    }

    private R actByController(A arg) {
        StopWatch sw = StopWatch.createStarted(getClass().getSimpleName() + ".actByController()");
        try {
            before(arg);
            sw.lap("before");
            pluginBefore(arg);
            sw.lap("pluginBefore");
            domainPluginBefore(arg);
            sw.lap("domainPluginBefore");
            result = doService(arg);
            sw.lap("doService");
            this.doActComplete = true;
            after(arg, result);
            sw.lap("after");
            pluginAfter(arg, result);
            sw.lap("pluginAfter");
            domainPluginAfter(arg, result);
            sw.lap("domainPluginAfter");
            this.processComplete = true;
            log.debug("Controller arg:{},result:{}", arg, result);
            return result;
        } finally {
            finallyDo();
            sw.lap("finallyDo");
            sw.logSlow(500);
        }
    }

    public Class<A> getArgClass() {
        return argClass;
    }

    @Override
    public final A getArg() {
        return arg;
    }

    public final void setArg(A arg) {
        this.arg = arg;
    }

    public final ControllerContext getControllerContext() {
        return controllerContext;
    }

    public final void setControllerContext(ControllerContext controllerContext) {
        this.controllerContext = controllerContext;
    }

    protected final boolean isDoActComplete() {
        return doActComplete;
    }

    protected final boolean isProcessComplete() {
        return processComplete;
    }
}
