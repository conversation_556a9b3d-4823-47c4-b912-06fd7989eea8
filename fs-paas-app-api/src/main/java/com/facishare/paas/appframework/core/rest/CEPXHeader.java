package com.facishare.paas.appframework.core.rest;

import com.facishare.paas.appframework.core.model.RequestContext;

/**
 * CEP 扩展Headers
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 2017/6/17.
 */
public enum CEPXHeader {

    TENANT_ID("X-fs-Enterprise-Id"),
    USER_ID("X-fs-Employee-Id"),
    FAILURE_CODE("X-fs-Fail-Code"), //兼容老的Failure code
    FAILURE_MESSAGE("X-fs-Fail-Message"), //兼容老的Failure message
    ERROR_CODE("X-fs-Error-Code"),
    POST_ID("X-fs-Post-Id"), //用于请求去重复
    CLIENT_INFO("X-fs-Client-Info"), //客户及版本信息如:Android.543015
    APP_ID("x-app-id"),
    OUT_LINK_TYPE("x-out-link-type"),
    OUT_TENANT_ID("x-out-tenant-id"),
    OUT_USER_ID("x-out-user-id"),
    LOCALE("X-fs-Locale"),
    CLIENT_IP("x-fs-client-ip"),
    UPSTREAM_OWNER_ID("x-fs-upstream-owner-id"),
    ENTERPRISE_ACCOUNT("x-fs-enterprise-account"),
    BIZ_ID("x-fs-biz-id"),
    OUT_IDENTITY_TYPE("x-out-identity-type"),
    THIRD_USER_ID("x-fs-thirdUserId"),
    THIRD_APP_ID("x-fs-thirdAppId"),
    THIRD_TYPE("x-fs-thirdType"),
    UPDATE_ORIGIN_SOURCE("x-fs-update-origin-source"),
    FS_DEVICE_TYPE(RequestContext.FS_DEVICE_TYPE),
    FS_DEVICE_ID(RequestContext.FS_DEVICE_ID),
    FS_MARKETING_MEMBER(RequestContext.FS_MARKETING_MEMBER),
    ;
    private String headerKey;

    public String key() {
        return headerKey;
    }

    CEPXHeader(String key) {
        this.headerKey = key;
    }
}
