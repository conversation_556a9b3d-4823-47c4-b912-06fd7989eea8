package com.facishare.paas.appframework.core.util;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

/**
 * 业务处理器灰度配置器，可以按照对象ApiName、接口Code配置灰度企业，格式如下：
 * <p/>
 * {
 * "interfaceGrayRules": {
 * "Add": {
 * "udobj": "white:74255|78057",
 * "SalesOrderObj": "white:74255"
 * }
 * },
 * "handlerGrayRules": {
 * "testAddHandler": {
 * "default": "white:74255|78057",
 * "udobj": "white:78057",
 * "SalesOrderObj": "white:74255"
 * }
 * }
 * }
 * <p/>
 * 其中，interfaceGrayRules表示按接口配置Handler的灰度企业，第一级key是interfaceCode，第二级key是objectApiName或udobj（对所有自定义对象生效），
 * value是标准的灰度规则格式的灰度企业id。
 * <p/>
 * handlerGrayRules表示按照Handler配置灰度企业，第一级key是handlerApiName，第二级key是objectApiName或udobj（对所有自定义对象生效）
 * 或default（对所有对象生效），value是标准的灰度规则格式的灰度企业id。
 * <p/>
 * Created by zhouwr on 2023/1/9.
 */
@Slf4j
public class HandlerGrayConfig {

    private static Map<String, Map<String, GrayRule>> interfaceGrayRules;
    private static Map<String, Map<String, GrayRule>> handlerGrayRules;
    private static Map<String, Map<String, GrayRule>> managementGrayRules;
    private static Map<String, Map<String, GrayRule>> globalTransactionGrayRules;
    private static Map<String, Map<String, GrayRule>> preMatchApprovalGrayRules;
    private static GrayRule handlerConfigSystemGrayRule;

    static {
        ConfigFactory.getConfig("fs-paas-handler-config", config -> {
            try {
                log.info("reload {},content:{}", config.getName(), config.getString());
                reload(config);
            } catch (Exception e) {
                log.error("reload {} failed", config.getName(), e);
            }
        });
    }

    private static void reload(IConfig config) {
        ConfigData configData = JSON.parseObject(config.getString(), ConfigData.class);
        interfaceGrayRules = parseGrayRules(configData.getInterfaceGrayRules());
        handlerGrayRules = parseGrayRules(configData.getHandlerGrayRules());
        managementGrayRules = parseGrayRules(configData.getManagementGrayRules());
        globalTransactionGrayRules = parseGrayRules(configData.getGlobalTransactionGrayRules());
        preMatchApprovalGrayRules = parseGrayRules(configData.getPreMatchApprovalGrayRules());
        handlerConfigSystemGrayRule = new GrayRule(configData.getHandlerConfigSystemGrayRule());
    }

    private static Map<String, Map<String, GrayRule>> parseGrayRules(Map<String, Map<String, String>> grayRuleValueMap) {
        return UdobjGrayConfig.parseGrayRules(grayRuleValueMap);
    }

    public static boolean supportHandler(String tenantId, String interfaceCode, String objectApiName) {
        return isInGrayList(interfaceGrayRules, tenantId, interfaceCode, objectApiName, false);
    }

    public static boolean supportManagement(String tenantId, String interfaceCode, String objectApiName) {
        return supportHandler(tenantId, interfaceCode, objectApiName)
                && isInGrayList(managementGrayRules, tenantId, interfaceCode, objectApiName, false);
    }

    public static boolean isHandlerInWhiteList(String tenantId, String handlerApiName, String objectApiName) {
        return isInGrayList(handlerGrayRules, tenantId, handlerApiName, objectApiName, true);
    }

    public static boolean supportGlobalTransaction(String tenantId, String interfaceCode, String objectApiName) {
        return isInGrayList(globalTransactionGrayRules, tenantId, interfaceCode, objectApiName, false);
    }

    public static boolean supportPreMatchApproval(String tenantId, String interfaceCode, String objectApiName) {
        return isInGrayList(preMatchApprovalGrayRules, tenantId, interfaceCode, objectApiName, false);
    }

    private static boolean isInGrayList(Map<String, Map<String, GrayRule>> grayRules, String tenantId, String firstKey,
                                        String objectApiName, boolean supportDefault) {
        if (Objects.isNull(grayRules) || grayRules.isEmpty()) {
            return false;
        }
        Map<String, GrayRule> grayRuleMap = grayRules.get(firstKey);
        return UdobjGrayConfig.isGrayWithDescribeApiName(grayRuleMap, tenantId, objectApiName, supportDefault);
    }

    public static boolean isHandlerConfigSystemGrayRule(String tenantId) {
        return handlerConfigSystemGrayRule.isAllow(tenantId);
    }

    public static String getSystemTenantId(String tenantId) {
        if (isHandlerConfigSystemGrayRule(tenantId)) {
            return "-101";
        }
        return "-100";
    }

    @Data
    private static class ConfigData {
        private Map<String, Map<String, String>> interfaceGrayRules;
        private Map<String, Map<String, String>> handlerGrayRules;
        private Map<String, Map<String, String>> managementGrayRules;
        private Map<String, Map<String, String>> globalTransactionGrayRules;
        private Map<String, Map<String, String>> preMatchApprovalGrayRules;
        private String handlerConfigSystemGrayRule;
    }
}
