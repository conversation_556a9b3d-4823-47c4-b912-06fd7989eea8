package com.facishare.paas.appframework.core.util;

import com.facishare.paas.timezone.TimeZoneContext;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.facishare.paas.timezone.config.TimeZoneConfig;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/07/13
 */
public final class TimezoneBizUtil {

    private TimezoneBizUtil() {
    }

    public static boolean skipTimezoneBusinessProcess(String tenantId) {
        if (!RequestUtil.isCepRequest()) {
            return true;
        }
        if (isNoConvertTimeZone(tenantId)) {
            return true;
        }
        return TimeZoneContext.DEFAULT_TIME_ZONE.equals(TimeZoneContextHolder.getUserTimeZone());
    }

    public static boolean isNoConvertTimeZone(String tenantId) {
        if (!TimeZoneConfig.INSTANCE.isGray(tenantId)) {
            return true;
        }
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.TIME_ZONE_NO_CONVERT_GRAY_EI, tenantId);
    }
}
