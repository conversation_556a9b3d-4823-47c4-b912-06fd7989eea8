package com.facishare.paas.appframework.core.model.plugin;

/**
 * Created by zhouwr on 2022/4/14.
 */
public interface PluginFunction {

    interface BuildArgFunction {
        Plugin.Arg apply(Plugin plugin, String method);
    }

    interface RunFunction {
        Plugin.Result apply(Plugin plugin, Plugin.Arg arg);
    }

    interface ProcessResultFunction {
        void apply(Plugin plugin, String method, Plugin.Arg arg, Plugin.Result result);
    }

}
