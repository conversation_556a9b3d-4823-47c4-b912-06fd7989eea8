package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/4/27
 * @Description :
 */
public class StandardSendEmailUIAction extends AbstractStandardAction<StandardSendEmailUIAction.Arg, StandardSendEmailUIAction.Result> {

	private List<String> ccEmails;    // 抄送给

	@Override
	protected String getButtonApiName() {
		return ObjectAction.SEND_MAIL.getDefaultButtonApiName();
	}

	@Override
	protected boolean skipCheckButtonConditions() {
		return true;
	}

	@Override
	protected boolean skipPreFunction() {
		return false;
	}

	@Override
	protected boolean skipPreFunctionArgValidate() {
		return true;
	}

	@Override
	protected Map<String, Object> getActionParams(Arg arg) {
		Map<String, Object> params = super.getActionParams(arg);
		params.put("dataIds", CollectionUtils.nullToEmpty(arg.getDataIds()));
		return params;
	}

	// 解析函数回参
	@Override
	protected void processValidatedFunctionResult() {
		super.processValidatedFunctionResult();
		Object functionResult = validatedFunctionResult.getFunctionResult();
		try {
			if (!(functionResult instanceof Map)) {
				return;
			}

			Map<String, Object> resultMap = CollectionUtils.nullToEmpty((Map<String, Object>) functionResult);
			Object ccEmailsObj = resultMap.get("ccList");

			if (ccEmailsObj instanceof List) {
				ccEmails = (List<String>) ccEmailsObj;
			}
		} catch (Exception e) {
			log.warn("StandardSendEmailUIAction解析函数回参失败:{}", validatedFunctionResult.getFunctionResult(), e);
		} finally {
			if (Objects.isNull(ccEmails)) {
				log.warn("SendEmailUI 函数返回结果解析失败{}", functionResult);
			}
		}
	}

	@Override
	protected List<String> getFuncPrivilegeCodes() {
		return Collections.emptyList();
	}

	@Override
	protected List<String> getDataPrivilegeIds(Arg arg) {
		return CollectionUtils.nullToEmpty(arg.getDataIds());
	}

	@Override
	protected Result doAct(Arg arg) {
		return Result.builder().ccList(ccEmails).build();
	}

	// TODO L: finally 埋点

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	public static class Arg {
		// 列表和详情均使用 dataIds, 详情就是 size=1
		private List<String> dataIds;
		// 只带出抄送人, 不需要其他参数

	}

	@Data
	@Builder
	public static class Result {
		private List<String> ccList;    // 抄送给
	}
}
