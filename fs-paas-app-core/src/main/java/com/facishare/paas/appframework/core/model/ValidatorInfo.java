package com.facishare.paas.appframework.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by zhouwr on 2024/1/5.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ValidatorInfo implements Serializable {
    //校验器的类型（比如DomainPluginBefore、DomainPluginPreact）
    private String validatorType;
    //校验器的唯一标识（比如DomainPlugin的apiName）
    private String validatorKey;
    //校验器中返回提示信息的校验逻辑（用于继续提交以后判断跳过哪一步校验）
    private String stepKey;
    //继续提交时是否自动跳过整个Validator的执行
    private boolean skipWhenForceSubmit;

    public static ValidatorInfo of(ValidatorType validatorType, String validatorKey, ValidationResult validationResult) {
        return ValidatorInfo.builder()
                .validatorType(validatorType.getCode())
                .validatorKey(validatorKey)
                .stepKey(validationResult.getStepKey())
                .skipWhenForceSubmit(validationResult.isSkipWhenForceSubmit())
                .build();
    }
}
