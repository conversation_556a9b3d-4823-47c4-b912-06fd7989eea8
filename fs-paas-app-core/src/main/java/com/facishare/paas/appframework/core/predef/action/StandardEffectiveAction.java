package com.facishare.paas.appframework.core.predef.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.facade.dto.DiffDetailData;
import com.facishare.paas.appframework.core.predef.facade.dto.EffectiveAndUpdateData;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderEffectiveStatus;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by zhaooju on 2023/4/13
 */
public class StandardEffectiveAction extends AbstractStandardAction<StandardEffectiveAction.Arg, StandardEffectiveAction.Result> {
    protected IObjectData objectData;
    protected Map<String, List<IObjectData>> detailDataMap;
    protected Map<String, IObjectDescribe> detailDescribeMap = Maps.newHashMap();

    protected IObjectDescribe originalDescribe;

    private IObjectData originalData;
    private List<IObjectData> originalDetailsToUpdate;
    private List<IObjectData> originalDetailsToDelete;
    private List<IObjectData> originalDetailsToAdd;
    protected Map<String, IObjectDescribe> originalObjectDescribes = Maps.newHashMap();
    protected Map<String, Map<String, Map<String, Object>>> originalDetailChangeMap = Maps.newHashMap();
    protected Map<String, List<IObjectData>> dbOriginalDetailDataMap = Maps.newHashMap();
    protected IObjectData dbOriginalMasterData;

    private String masterLogId;
    private Map<String, Map<String, Object>> detailChangeMap;

    protected Map<String, Object> callBackData;

    @Override
    protected void init() {
        super.init();
        objectData = dataList.get(0);
        stopWatch.lap("supper-init");
        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(actionContext.getTenantId(), objectDescribe.getApiName());
        detailDescribes.forEach(describe -> detailDescribeMap.put(describe.getApiName(), describe));
        stopWatch.lap("findDetailDescribes");
        detailDataMap = serviceFacade.findDetailObjectDataList(detailDescribes, objectData, actionContext.getUser());
        stopWatch.lap("findDetailObject");

        initOriginalData();
        stopWatch.lap("initOriginalData");
    }

    private void initOriginalData() {
        originalDescribe = serviceFacade.findObject(actionContext.getTenantId(), objectDescribe.getOriginalDescribeApiName());
        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                .user(actionContext.getUser())
                .skipRelevantTeam(true)
                .includeInvalid(needInvalidData())
                .searchRichTextExtra(true)
                .keepAllMultiLangValue(keepAllMultiLangValue())
                .build();
        String id = ObjectDataExt.of(objectData).getOriginalDataId();
        List<IObjectData> dbOriginalMasterDataList = serviceFacade.findObjectDataByIdsWithQueryContext(queryContext, Lists.newArrayList(id), originalDescribe.getApiName());
        if (CollectionUtils.empty(dbOriginalMasterDataList)) {
            log.warn("original data not exit, id:{}", id);
            throw new ValidateException(I18NExt.text(I18NKey.ORIGINAL_DATA_NOT_EXIT));
        }
        dbOriginalMasterData = dbOriginalMasterDataList.get(0);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        //发送mq消息
        sendEditMq();
        stopWatch.lap("sendEditMq");
        recordLog();
        stopWatch.lap("recordLog");
        startWorkFlow();
        stopWatch.lap("startWorkFlow");
        return super.after(arg, result);
    }

    private void sendEditMq() {
        //先拷贝一下数据，防止并发修改报错
        List<IObjectData> cpOldData = ObjectDataExt.copyList(fillOldData(dbOriginalMasterData, ObjectDataDocument.of(originalData)));
        Map<String, List<IObjectData>> cpUpdateDetails = ObjectDataExt.groupByDescribeApiName(ObjectDataExt.copyList(originalDetailsToUpdate));
        Map<String, List<IObjectData>> cpDeleteDetails = ObjectDataExt.groupByDescribeApiName(ObjectDataExt.copyList(originalDetailsToDelete));
        Map<String, List<IObjectData>> cpAddDetails = ObjectDataExt.groupByDescribeApiName(ObjectDataExt.copyList(originalDetailsToAdd));

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            serviceFacade.sendActionMq(actionContext.getUser(), cpOldData, ObjectAction.UPDATE);
            cpUpdateDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(actionContext.getUser(), v, ObjectAction.UPDATE));
            cpDeleteDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(actionContext.getUser(), v, ObjectAction.DELETE));
            cpAddDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(actionContext.getUser(), v, ObjectAction.CREATE));
        });
        parallelTask.run();
    }

    private List<IObjectData> fillOldData(IObjectData updateData, Map<String, Object> callbackData) {
        IObjectData ret = ObjectDataExt.of(updateData).copy();
        ret.set("eventId", actionContext.getEventId());
        ObjectDataExt.of(ret).putAll(callbackData);
        if (!Utils.NEW_OPPORTUNITY_API_NAME.equals(objectDescribe.getApiName())) {
            return Lists.newArrayList(ret);
        }

        // copy一份ObjectData，防止后续updateData的变更，对old_data造成影响
        IObjectData oldData = ObjectDataExt.of(updateData).copy();
        // fillOldData 在处理时updateData使用了新的map。old_data和oldData公用同一个map
        // 后续操作对updateData的变动不会影响到返回的结果，对oldData的变动会影响到old_data的值
        return ObjectDataDocument.fillOldData(Lists.newArrayList(ret), Lists.newArrayList(oldData));
    }

    private void startWorkFlow() {
        if (!needTriggerWorkFlow()) {
            return;
        }
        triggerCreateWorkFlow();
        stopWatch.lap("triggerCreateWorkFlow");
    }

    private void triggerCreateWorkFlow() {
        Map<String, List<IObjectData>> dataToTriggerCreateWorkFlow = ObjectDataExt.groupByDescribeApiName(ObjectDataExt.copyList(originalDetailsToAdd));
        triggerWorkFlow(dataToTriggerCreateWorkFlow, ApprovalFlowTriggerType.CREATE);
    }

    private void triggerWorkFlow(Map<String, List<IObjectData>> dataToTriggerWorkFlow, ApprovalFlowTriggerType approvalFlowTriggerType) {
        if (CollectionUtils.empty(dataToTriggerWorkFlow)) {
            return;
        }
        dataToTriggerWorkFlow.forEach((apiName, dataList) -> dataList.forEach(objectData -> {
            try {
                infraServiceFacade.startWorkFlow(objectData.getId(), apiName, approvalFlowTriggerType.getId(),
                        actionContext.getUser(),
                        Collections.emptyMap(), actionContext.getEventId());
            } catch (Exception e) {
                log.error("startWorkFlow error,tenantId:{},triggerType:{},apiName:{},dataId:{}", actionContext.getTenantId(),
                        approvalFlowTriggerType.getId(), apiName, objectData.getId(), e);
            }
        }));
    }

    /**
     * 记录修改记录
     */
    private void recordLog() {
        IObjectData cpObjectData = copyAndFillSourceInfo(originalData);
        List<IObjectData> cpDetailsToUpdate = copyAndFillSourceInfo(originalDetailsToUpdate);
        List<IObjectData> cpDetailsToDelete = copyAndFillSourceInfo(originalDetailsToDelete);
        List<IObjectData> cpDetailsToAdd = copyAndFillSourceInfo(originalDetailsToAdd);
        DiffDetailData.Arg diffDetailDataArg = DiffDetailData.Arg.builder()
                .detailsToAdd(cpDetailsToAdd)
                .detailsToUpdate(cpDetailsToUpdate)
                .detailsToDelete(cpDetailsToDelete)
                .objectDescribes(originalObjectDescribes)
                .dbDetailDataMap(dbOriginalDetailDataMap)
                .build();
        DiffDetailData.Result diffDetailData = infraServiceFacade.diffDetailData(actionContext.getUser(), diffDetailDataArg);

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        detailChangeMap = diffDetailData.getDetailChangeMap();
        parallelTask.submit(() -> {
            masterModifyLog(cpObjectData);
            ObjectDataExt.groupByDescribeApiName(cpDetailsToUpdate).forEach((objectApiName, dataList) -> detailModifyLog(objectApiName, dataList));
            ObjectDataExt.groupByDescribeApiName(cpDetailsToDelete).forEach((objectApiName, dataList) -> detailDeleteAuditLog(objectApiName, dataList));
            ObjectDataExt.groupByDescribeApiName(cpDetailsToAdd).forEach((objectApiName, dataList) -> detailAddAuditLog(objectApiName, dataList));
        });
        parallelTask.run();
    }

    private IObjectData copyAndFillSourceInfo(IObjectData objectData) {
        return copyAndFillSourceInfo(Lists.newArrayList(objectData)).get(0);
    }

    // 修改记录中记录修改的来源是变更单
    private List<IObjectData> copyAndFillSourceInfo(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return Lists.newArrayList();
        }
        List<IObjectData> result = ObjectDataExt.copyList(objectDataList);
        for (IObjectData data : result) {
            data.set(BaseObjectSaveAction.OptionInfo.CREATE_FROM, BaseObjectSaveAction.OptionInfo.FROM_CHANGE);
        }
        return result;
    }

    private void masterModifyLog(IObjectData cpObjectData) {
        Map detailChange = detailChangeMap;
        LogService.MasterLogInfo masterLog = serviceFacade.fillMasterModifyLog(actionContext.getUser(), ActionType.Modify, originalObjectDescribes,
                cpObjectData, getUpdatedFieldMapForLog(), dbOriginalMasterData, detailChange, getLogExtendsInfo());
        masterLogId = masterLog.getMasterLogId();
        serviceFacade.sendLog(masterLog.getLogList());
    }

    private void detailModifyLog(String apiName, List<IObjectData> dataList) {
        serviceFacade.detailModifyLog(actionContext.getUser(), ActionType.Modify, originalObjectDescribes.get(apiName), dataList, getDetailUpdateFieldMap(apiName),
                dbOriginalDetailDataMap.get(apiName), masterLogId);
    }

    private Map<String, Map<String, Object>> getDetailUpdateFieldMap(String apiName) {
        if (CollectionUtils.empty(detailChangeMap.get(apiName))) {
            return Maps.newHashMap();
        }
        return (Map<String, Map<String, Object>>) detailChangeMap.get(apiName).get(ObjectAction.UPDATE.getActionCode());
    }

    private void detailDeleteAuditLog(String objectApiName, List<IObjectData> dataList) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, originalObjectDescribes.get(objectApiName));
        serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Delete, describeMap, dataList, masterLogId);
    }

    private void detailAddAuditLog(String objectApiName, List<IObjectData> dataList) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectApiName, originalObjectDescribes.get(objectApiName));
        serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Add, describeMap, dataList, masterLogId);
    }

    protected final Map<String, Object> getLogExtendsInfo() {
        Map<String, Object> map = Maps.newHashMap();
        map.put(LogInfo.TRIGGER_WORK_FLOW, needTriggerWorkFlow());
        map.put(LogInfo.TRIGGER_APPROVAL_FLOW, needTriggerApprovalFlow());
        return map;
    }

    private Map<String, Map<String, Map<String, Object>>> getUpdatedFieldMapForLog() {
        Map<String, Map<String, Map<String, Object>>> allUpdatedFieldMap = Maps.newHashMap();
        // 获取主对象的更新字段
        Map<String, Map<String, Object>> masterUpdateMap = Maps.newHashMap();
        IObjectDescribe originalDescribe = originalObjectDescribes.get(dbOriginalMasterData.getDescribeApiName());
        Map<String, Object> updateFields = ObjectDataExt.of(dbOriginalMasterData).diff(originalData, originalDescribe);
        masterUpdateMap.put(originalData.getId(), updateFields);
        allUpdatedFieldMap.put(originalData.getDescribeApiName(), masterUpdateMap);
        return allUpdatedFieldMap;
    }

    protected boolean needTriggerWorkFlow() {
        return Optional.ofNullable(callBackData)
                .map(it -> (Boolean) it.get(ExtraDataKeys.TRIGGER_WORK_FLOW))
                .orElseGet(() -> !Boolean.FALSE.equals(actionContext.getAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW)));
    }

    protected boolean needTriggerApprovalFlow() {
        return !Boolean.FALSE.equals(actionContext.getAttribute(RequestContext.Attributes.TRIGGER_FLOW));
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validateByEffectiveStatus();
        stopWatch.lap("validateByEffectiveStatus");
        // 校验变更规则
        validateChangeRuleWithChangeData();
        stopWatch.lap("validateChangeRuleWithChangeData");
    }

    private void validateByEffectiveStatus() {
        ChangeOrderEffectiveStatus effectiveStatus = ObjectDataExt.of(objectData).getChangeOrderEffectiveStatus();
        if (ChangeOrderEffectiveStatus.INEFFECTIVE != effectiveStatus) {
            log.warn("validateByEffectiveStatus ei:{}, describeApiName:{}, dataId:{}, effectiveStatus:{}", actionContext.getTenantId(),
                    objectData.getDescribeApiName(), objectData.getId(), effectiveStatus.getCode());
            if (isFlowCompleted()) {
                // 流程回调，当前数据已经生效，直接结束
                throw new AcceptableValidateException(new Result());
            }
            throw new ValidateException(I18NExt.text(I18NKey.CHANGED_STATUS_NOT_INEFFECTIVE));
        }
    }

    private boolean isFlowCompleted() {
        return BooleanUtils.isTrue(arg.getFlowCompleted());
    }

    private void validateChangeRuleWithChangeData() {
        infraServiceFacade.validateChangeRuleWithChangeData(actionContext.getUser(), objectDescribe, objectData, MtChangeOrderRule.CalibrationType.EFFECTIVE);
    }

    @Override
    protected Result doAct(Arg arg) {
        EffectiveAndUpdateData.Arg effectiveArg = EffectiveAndUpdateData.Arg.builder()
                .describe(objectDescribe)
                .objectData(objectData)
                .detailDataMap(detailDataMap)
                .detailDescribeMap(detailDescribeMap)
                .build();
        EffectiveAndUpdateData.Result updateResult = infraServiceFacade.effectiveAndUpdateData(actionContext.getUser(), effectiveArg);
        stopWatch.lap("effectiveAndUpdateData");
        originalData = updateResult.getOriginalData();
        originalDetailsToAdd = updateResult.getDetailsToAdd();
        originalDetailsToUpdate = updateResult.getDetailsToUpdate();
        originalDetailsToDelete = updateResult.getDetailsToDelete();
        originalObjectDescribes = updateResult.getObjectDescribeMap();

        for (IObjectData detailData : originalDetailsToUpdate) {
            originalDetailChangeMap.computeIfAbsent(detailData.getDescribeApiName(), x -> Maps.newHashMap())
                    .put(detailData.getId(), ObjectDataDocument.of(detailData));
        }
        dbOriginalMasterData = updateResult.getDbOriginalMasterData();
        dbOriginalDetailDataMap = updateResult.getDbOriginalDetailDataMap();

        callBackData = parseCallbackData(updateResult);
        stopWatch.lap("parseCallbackData");
        doBusinessAction();
        stopWatch.lap("doBusinessAction");
        doPostAction();
        stopWatch.lap("doPostAction");
        return Result.builder()
                .originalDataApiName(originalData.getDescribeApiName())
                .originalDataId(originalData.getId())
                .build();
    }

    protected void doBusinessAction() {

    }

    protected Map<String, Object> parseCallbackData(EffectiveAndUpdateData.Result updateResult) {
        return updateResult.getCallBackData();
    }

    // 执行后动作函数
    private void doPostAction() {
        String actualButtonApiName = getPostActionButtonApiName();
        ButtonExecutor.Arg executorArg = getButtonPostActionArg(actualButtonApiName, originalData, Maps.newHashMap(), getOriginalDetails());
        executorArg.setActionStage("post");
        //回调后函数支持幂等
        actionContext.getRequestContext().setAttribute(RequestContext.Attributes.FUNCTION_IDEMPOTENT, Boolean.TRUE);
        infraServiceFacade.triggerValidationFunction(actionContext.getUser(), executorArg);
    }

    private Map<String, List<IObjectData>> getOriginalDetails() {
        return dbOriginalDetailDataMap;
    }

    private String getPostActionButtonApiName() {
        if (!AppFrameworkConfig.isAddEditUIActionGray(actionContext.getTenantId(), originalDescribe.getApiName())) {
            return ObjectAction.UPDATE.getButtonApiName();
        }
        return ObjectAction.UPDATE_SAVE.getButtonApiName();
    }

    private ButtonExecutor.Arg getButtonPostActionArg(String buttonApiName, IObjectData objectData, Map<String, Object> actionParam, Map<String, List<IObjectData>> detailObjectData) {
        return ButtonExecutor.Arg.builder()
                .actionStage("post")
                .actionParams(actionParam)
                .buttonApiName(buttonApiName)
                .describeApiName(originalDescribe.getApiName())
                .objectData(objectData)
                .details(detailObjectData)
                .build();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Effective.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void doFunPrivilegeCheck() {
        super.doFunPrivilegeCheck();
        findDescribe();
        // 校验原单对象「变更」的功能权限
        serviceFacade.doFunPrivilegeCheck(actionContext.getUser(), objectDescribe.getOriginalDescribeApiName(), StandardAction.Change.getFunPrivilegeCodes());
    }

    @Override
    protected void doDataPrivilegeCheck() {
        super.doDataPrivilegeCheck();
        serviceFacade.doDataPrivilegeCheck(actionContext.getUser(), Lists.newArrayList(dbOriginalMasterData), originalDescribe,
                StandardAction.Change.getFunPrivilegeCodes().get(0));
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.EFFECTIVE.getButtonApiName();
    }

    @Data
    public static class Arg {
        @JsonProperty("object_id")
        private String objectDataId;
        private Boolean flowCompleted;

        public static Arg createByFlowCompleted(String objectDataId) {
            StandardEffectiveAction.Arg arg = new StandardEffectiveAction.Arg();
            arg.setObjectDataId(objectDataId);
            arg.setFlowCompleted(true);
            return arg;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String originalDataId;
        private String originalDataApiName;

    }
}
