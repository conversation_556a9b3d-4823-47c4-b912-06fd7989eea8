package com.facishare.paas.appframework.core.predef.action;


import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.stream.Collectors;

//@Idempotent(serializer = Serializer.Type.java)
public class StandardLockAction extends BaseObjectLockAction {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Lock.getFunPrivilegeCodes();
    }

    @Override
    protected boolean isLock() {
        return true;
    }

    @Override
    protected ActionType getActionType() {
        return ActionType.Lock;
    }

    protected List<IObjectData> filterByLockStatus(List<IObjectData> objectDataList) {
        return objectDataList.stream().filter(it -> !ObjectDataExt.of(it).isLock()).collect(Collectors.toList());
    }

    @Override
    protected List<IObjectData> filterMasterData(List<IObjectData> masterDataList) {
        return filterByLockStatus(masterDataList);
    }

    @Override
    protected List<IObjectData> filterDetailData(List<IObjectData> detailDataList) {
        return filterByLockStatus(detailDataList);
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.LOCK.getButtonApiName();
    }

}
