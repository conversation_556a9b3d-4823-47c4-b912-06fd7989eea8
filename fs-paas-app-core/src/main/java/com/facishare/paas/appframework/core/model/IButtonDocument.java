package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.LayoutButtonExt;
import com.facishare.paas.appframework.metadata.config.ButtonConfig;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.LayoutButtonExt.*;

public class IButtonDocument extends DocumentBaseEntity {

    public static final String API_NAME = "api_name";

    public IButtonDocument() {
    }

    public IButtonDocument(Map map) {
        super(map);
    }

    public static List<IButtonDocument> ofList(List<IButton> buttons) {
        if (CollectionUtils.empty(buttons)) {
            return Lists.newArrayList();
        }
        return buttons.stream().map(IButtonDocument::of).collect(Collectors.toList());
    }

    public static Map<String, List<IButtonDocument>> ofMap(Map<String, List<IButton>> buttonMap) {
        if (CollectionUtils.empty(buttonMap)) {
            return Maps.newHashMap();
        }
        Map<String, List<IButtonDocument>> result = Maps.newHashMap();
        buttonMap.forEach((k, v) -> result.put(k, ofList(v)));
        return result;
    }

    public static IButtonDocument of(IButton button) {
        return new IButtonDocument(LayoutButtonExt.of(button).toMap());
    }

    @Deprecated
    public static List<IButtonDocument> handleTerminal(List<IButtonDocument> buttonDocuments) {
        for (IButtonDocument buttonDocument : buttonDocuments) {
            List<String> terminals = Lists.newArrayList();
            String buttonApi = (String) buttonDocument.get(API_NAME);
            if (ButtonConfig.isListLayoutWebOnlyButtons(buttonApi)) {
                terminals.add(TERMINAL_TYPE_WEB);
            }
            if (ButtonConfig.isListLayoutMobileOnlyButtons(buttonApi)) {
                terminals.add(TERMINAL_TYPE_MOBILE);
            }
            if (CollectionUtils.notEmpty(terminals)) {
                buttonDocument.put(TERMINAL, String.join("|", terminals));
            }
            if (!ObjectAction.CREATE.getButtonApiName().equals(buttonApi)) {
                buttonDocument.put(PAGE_TYPE, PAGE_TYPE_LIST);
            }
        }
        return buttonDocuments;
    }

    public static List<IButtonDocument> handleTerminal(List<IButtonDocument> buttonDocuments, String tenantId) {
        for (IButtonDocument buttonDocument : buttonDocuments) {
            String apiName = (String) buttonDocument.get(API_NAME);
            List<String> terminals = Lists.newArrayList();
            if (ButtonConfig.isListLayoutWebOnlyButtons(apiName)
                    || ButtonConfig.listLayoutButtonDisplayGray(apiName, TERMINAL_TYPE_WEB, tenantId)) {
                terminals.add(TERMINAL_TYPE_WEB);
            }
            if (ButtonConfig.listLayoutButtonDisplayGray(apiName, TERMINAL_TYPE_MOBILE, tenantId)) {
                terminals.add(TERMINAL_TYPE_MOBILE);
            }
            if (CollectionUtils.notEmpty(terminals)) {
                buttonDocument.put(TERMINAL, String.join("|", terminals));
            }


            if (!ObjectAction.CREATE.getButtonApiName().equals(apiName)) {
                buttonDocument.put(PAGE_TYPE, PAGE_TYPE_LIST);
            }
        }
        return buttonDocuments;
    }
}
