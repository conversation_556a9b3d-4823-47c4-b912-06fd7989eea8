package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class StandardInsertImportVerifyAction extends BaseImportVerifyAction {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.InsertImportVerify.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void recordLog() {
        serviceFacade.log(actionContext.getUser(), EventType.ADD, ActionType.Import, objectDescribe.getApiName(), objectDescribe.getDisplayName());
    }

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        return infraServiceFacade.getTemplateField(actionContext.getUser(), objectDescribe);
    }

    @Override
    protected String customVerify() {
        return null;
    }

    @Override
    protected String validateFieldsByID(List<IFieldDescribe> validFieldList, Set<Map.Entry<String, Object>> entrySet) {
        String error = validateUniqueRule(validFieldList, entrySet);
        if (!Strings.isNullOrEmpty(error)) {
            return error;
        }
        return super.validateFieldsByID(validFieldList, entrySet);
    }

    @Override
    protected String validateFieldsByName(List<IFieldDescribe> validFieldList, Set<Map.Entry<String, Object>> entrySet) {
        String error = validateUniqueRule(validFieldList, entrySet);
        if (!Strings.isNullOrEmpty(error)) {
            return error;
        }
        return super.validateFieldsByName(validFieldList, entrySet);
    }

    @Override
    protected String validateFieldsByUniqueRule(List<IFieldDescribe> validFieldList, Set<Map.Entry<String, Object>> entrySet) {
        throw new ValidateException(I18N.text(I18NKey.NOT_SUPPORTED_INSERT_IMPORT_BY_UNIQUENESS));
    }

    @Override
    protected String verifyFields(List<IFieldDescribe> validFieldList, IObjectData data) {
        String error = verifyUniqueRule(validFieldList, data);
        if (!Strings.isNullOrEmpty(error)) {
            return error;
        }
        return super.verifyFields(validFieldList, data);
    }

    @Override
    protected String verifyFieldsByUniqueRule(List<IFieldDescribe> validFieldList, IObjectData data) {
        throw new ValidateException(I18N.text(I18NKey.NOT_SUPPORTED_INSERT_IMPORT_BY_UNIQUENESS));
    }
}
