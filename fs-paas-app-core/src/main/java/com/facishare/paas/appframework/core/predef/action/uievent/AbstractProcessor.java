package com.facishare.paas.appframework.core.predef.action.uievent;

import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IUIEvent;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Map;

/**
 * 抽象处理器
 * 所有处理器都需要用到服务接口serviceFacade
 *
 * <AUTHOR>
 * @date 2019-08-05 18:26
 */
public abstract class AbstractProcessor implements Processor {

    IUIEvent event;
    ActionContainer container;
    ServiceFacade serviceFacade;
    InfraServiceFacade infraServiceFacade;
    RequestContext requestContext;
    IObjectDescribe objectDescribe;
    Map<String, IObjectDescribe> detailObjectDescribeMap;

    AbstractProcessor(ActionContainer container) {
        this.container = container;
        serviceFacade = container.getServiceFacade();
        infraServiceFacade = container.getInfraServiceFacade();
        requestContext = container.getContext();
        objectDescribe = container.getObjectDescribe();
        detailObjectDescribeMap = container.getDetailDescribe();
    }

    AbstractProcessor(Builder builder) {
        container = builder.container;
        serviceFacade = container.getServiceFacade();
        infraServiceFacade = container.getInfraServiceFacade();
        requestContext = container.getContext();
        objectDescribe = container.getObjectDescribe();
        detailObjectDescribeMap = container.getDetailDescribe();
    }

    @Override
    public void setEvent(IUIEvent event) {
        this.event = event;
    }

    public static abstract class Builder {
        ActionContainer container;

        public Builder(ActionContainer container) {
            this.container = container;
        }

        public abstract Processor build();
    }

}
