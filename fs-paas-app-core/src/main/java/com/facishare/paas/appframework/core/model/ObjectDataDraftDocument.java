package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.metadata.ObjectDataDraftExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectDataDraft;
import com.facishare.paas.metadata.impl.ObjectDataDraft;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 封装草稿箱实体
 *
 * <AUTHOR>
 * @date 2019/11/12 10:25 上午
 */
public class ObjectDataDraftDocument extends DocumentBaseEntity {
    public ObjectDataDraftDocument() { }
    private ObjectDataDraftDocument(Map<String, Object> data) {
        super(data);
    }


    public IObjectDataDraft toObjectDataDraft() {
        return ObjectDataDraftExt.of(data).getObjectDataDraft();
    }

    public static ObjectDataDraftDocument of(IObjectDataDraft draft) {
        if (Objects.isNull(draft)) {
            return null;
        }
        Map<String, Object> masterMap = ObjectDataExt.of(draft.getMasterDraftData()).toMap();
        draft.set(IObjectDataDraft.MASTER_DRAFT_DATA, masterMap);
        Map<String, Object> slaveMap = Maps.newHashMap();
        Optional.ofNullable(draft.getSlaveDraftData()).ifPresent(slaves -> {
            slaves.forEach((apiName, dataList) -> {
                List<Map<String,Object>> maps =
                        dataList.stream().map(ObjectDataExt::of).map(d -> d.toMap()).collect(Collectors.toList());
                slaveMap.put(apiName, maps);
            });
        });
        draft.set(IObjectDataDraft.SLAVE_DRAFT_DATA, slaveMap);
        return new ObjectDataDraftDocument(ObjectDataDraftExt.of(draft).toMap());
    }

    public static List<ObjectDataDraftDocument> ofList(List<IObjectDataDraft> dataDrafts) {
        List<ObjectDataDraftDocument> result = Lists.newArrayList();
        for (IObjectDataDraft draft : dataDrafts) {
            result.add(of(draft));
        }
        return result;
    }
}
