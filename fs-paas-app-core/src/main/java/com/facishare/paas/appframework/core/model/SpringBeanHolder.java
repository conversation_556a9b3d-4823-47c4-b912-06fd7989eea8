package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.core.model.domain.DomainPluginManager;
import com.facishare.paas.appframework.core.model.handler.HandlerManager;
import com.facishare.paas.appframework.core.model.plugin.PluginManager;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2023/3/17.
 */
@Getter
@Component
public class SpringBeanHolder {
    @Autowired
    private HandlerManager handlerManager;
    @Autowired
    private HandlerLogicService handlerLogicService;
    @Autowired
    private PluginManager pluginManager;
    @Autowired
    private DomainPluginManager domainPluginManager;
}
