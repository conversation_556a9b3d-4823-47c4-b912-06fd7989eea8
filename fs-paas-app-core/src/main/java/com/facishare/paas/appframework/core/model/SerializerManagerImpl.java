package com.facishare.paas.appframework.core.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.core.model.RequestContext.ContentType;

/**
 * 序列化管理器实现
 * <p>
 * Created by liyiguang on 2017/6/20.
 */
@Slf4j
@Service
public class SerializerManagerImpl implements SerializerManager {

    private Map<ContentType, JSONSerializer> serializerMap = Maps.newEnumMap(ContentType.class);

    public SerializerManagerImpl() {
        serializerMap.put(ContentType.FULL_JSON, new FullJSONSerializer());
        serializerMap.put(ContentType.SIMPLE_JSON, new SimpleJSONSerializer());
        serializerMap.put(ContentType.NONNULL_FULL_JSON, new NonnullFullJSONSerializer());
        serializerMap.put(ContentType.NONNULL_SIMPLE_JSON, new NonnullSimpleJSONSerializer());
    }

    @Override
    public JSONSerializer getSerializer(ContentType contentType) {
        JSONSerializer ret = serializerMap.get(contentType);
        Objects.requireNonNull(ret);
        return ret;
    }

    private static class FullJSONSerializer implements JSONSerializer {

        @Override
        public String encode(Object object) {
            return JacksonUtils.toJson(object);
        }

        @Override
        public <T> T decode(Class<T> clazz, String json) {
            Object body;
            try {
                body = JacksonUtils.fromJson(json, clazz);
            } catch (Exception jse) {
                log.warn(I18NExt.text(I18NKey.ARGUMENT_DECODE_FROM_JSON), jse);
                throw new ValidateException(I18NExt.text(I18NKey.ARGUMENT_DECODE_FROM_JSON));
            }
            return (T) body;
        }
    }

    private static class NonnullFullJSONSerializer implements JSONSerializer {

        @Override
        public String encode(Object object) {
            return JacksonUtils.toJsonExcludeNullValue(object);
        }

        @Override
        public <T> T decode(Class<T> clazz, String json) {
            Object body;
            try {
                body = JacksonUtils.fromJson(json, clazz);
            } catch (Exception jse) {
                log.warn(I18NExt.text(I18NKey.ARGUMENT_DECODE_FROM_JSON), jse);
                throw new ValidateException(I18NExt.text(I18NKey.ARGUMENT_DECODE_FROM_JSON));
            }
            return (T) body;
        }
    }

    private static class SimpleJSONSerializer implements JSONSerializer {
        @Override
        public String encode(Object object) {
            return JSON.toJSONString(object, SerializerFeature.WriteMapNullValue,
                    SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteBigDecimalAsPlain);
        }

        @Override
        public <T> T decode(Class<T> clazz, String json) {
            Object body;
            try {
                body = JSON.parseObject(json, clazz);
            } catch (Exception jse) {
                log.warn(I18NExt.text(I18NKey.ARGUMENT_DECODE_FROM_JSON), jse);
                throw new ValidateException(I18NExt.text(I18NKey.ARGUMENT_DECODE_FROM_JSON));
            }
            return (T) body;
        }
    }

    private static class NonnullSimpleJSONSerializer implements JSONSerializer {
        @Override
        public String encode(Object object) {
            return JSON.toJSONString(object, SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteBigDecimalAsPlain);
        }

        @Override
        public <T> T decode(Class<T> clazz, String json) {
            Object body;
            try {
                body = JSON.parseObject(json, clazz);
            } catch (Exception jse) {
                log.warn(I18NExt.text(I18NKey.ARGUMENT_DECODE_FROM_JSON), jse);
                throw new ValidateException(I18NExt.text(I18NKey.ARGUMENT_DECODE_FROM_JSON));
            }
            return (T) body;
        }
    }

}
