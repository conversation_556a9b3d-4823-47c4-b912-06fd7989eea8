package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.metadata.api.IUIEvent;
import com.facishare.paas.metadata.impl.UIEvent;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-07-29 16:09
 */
public class UIEventDocument extends DocumentBaseEntity {

    public UIEventDocument() {

    }

    public UIEventDocument(Map<String, Object> data) {
        super(data);
    }

    public IUIEvent toUIEvent() {
        return new UIEvent(data);
    }

    public static UIEventDocument of(IUIEvent event) {
        if (event == null) {
            return null;
        }
        @SuppressWarnings("unchecked") UIEventDocument eventDocument =
                new UIEventDocument(((UIEvent) event).getContainerDocument());
        return eventDocument;
    }

    public static UIEventDocument of(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        return new UIEventDocument(data);
    }


    public static List<UIEventDocument> ofList(List<IUIEvent> events) {
        List<UIEventDocument> result = Lists.newArrayList();
        if (CollectionUtils.empty(events)) {
            return result;
        }
        events.forEach(event -> result.add(of(event)));
        return result;
    }

    public Map<String, Object> getMap() {
        return data;
    }
}
