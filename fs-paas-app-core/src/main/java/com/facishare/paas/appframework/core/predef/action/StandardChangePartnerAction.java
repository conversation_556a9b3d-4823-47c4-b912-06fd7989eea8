package com.facishare.paas.appframework.core.predef.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.OutInfoChangeModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.PartnerRemindOutUserService;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class StandardChangePartnerAction extends BaseUpdatePartnerAction<StandardChangePartnerAction.Arg, BaseUpdatePartnerAction.Result> {
    protected PartnerRemindOutUserService partnerRemindOutUserService = SpringUtil.getContext().getBean(PartnerRemindOutUserService.class);
    protected StandardChangePartnerAction.Result result = StandardChangePartnerAction.Result.builder().errorCode("0").message(I18N.text(I18NKey.CHANGE_PARTNER_SUCCESS)).build();

    protected IObjectData partnerObjData = null;
    protected Tuple<Integer, Long> newOutInfo;


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(StandardAction.ChangePartner.getFunPrivilegeCodes());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getDataIds();
    }

    private List<OutInfoChangeModel> outInfoChangeModelList = Lists.newArrayList();

    @Override
    protected void before(Arg arg) {
        log.info("StandardChangePartnerAction before arg {}", arg);
        super.before(arg);
        if (CollectionUtils.isEmpty(arg.getDataIds())) {
            throw new ValidateException(I18N.text(I18NKey.DATA_ID_EMPTY));
        }
        if (StringUtils.isBlank(arg.getPartnerId())) {
            throw new ValidateException(I18N.text(I18NKey.PARNTER_ID_EMPTY));
        }
        objectDataList = this.findByIdsIncludeLookUpName(this.actionContext.getUser(), this.objectDescribe.getApiName(), arg.getDataIds());
        if (CollectionUtils.isEmpty(objectDataList)) {
            throw new ValidateException(I18N.text(I18NKey.OBJECT_DATA_NOT_FOUND));
        }
        partnerObjData = serviceFacade.findObjectData(this.actionContext.getUser(), arg.getPartnerId(), Utils.PARTNER_API_NAME);

        for (IObjectData objectData : objectDataList) {
            ObjectDataExt objectExt = ObjectDataExt.of(objectData);
            if (objectExt.isLock()) {
                throw new ValidateException(objectExt.getName() + I18N.text(I18NKey.DATA_LOCKED));
            }
            if (objectExt.isInvalid()) {
                throw new ValidateException(objectExt.getName() + I18N.text(I18NKey.DATA_INVALID));
            }
            OutInfoChangeModel model = OutInfoChangeModel.builder().dataId(objectData.getId()).dataName(objectData.getName()).isPreDefineObj("package".equals(objectDescribe.getDefineType()) ? true : false).build();
            Long outUserId = 0L;
            model.setOldOutEI(StringUtils.isBlank(objectData.getOutTenantId()) ? 0 : Integer.parseInt(objectData.getOutTenantId()));
            if (CollectionUtils.isNotEmpty(objectData.getOutOwner())) {
                outUserId = StringUtils.isBlank(objectData.getOutOwner().get(0).toString()) ? 0L : Long.parseLong(objectData.getOutOwner().get(0).toString());
            }
            model.setOldOutUserId(outUserId.intValue());
            outInfoChangeModelList.add(model);
        }

        dataList.removeIf(objectData -> arg.getPartnerId().equals(objectData.get(PrmConstant.FIELD_PARTNER_ID, String.class)));
    }

    @Override
    protected Result doAct(Arg arg) {
        if (!result.isSuccess()) {
            return result;
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return result;
        }
        if (needTriggerApprovalFlow()) {
            Map<String, Object> data = Maps.newHashMap();
            Map<String, Map<String, Object>> dataMap = Maps.newHashMap();
            Map<String, Object> callbackData = Maps.newHashMap();
            Map<String, Map<String, Object>> callbackDataMap = Maps.newHashMap();
            dataList.forEach(x -> {
                data.put("partner_id", arg.getPartnerId());
                dataMap.put(x.getId(), data);
                callbackData.put("partnerId", arg.getPartnerId());
                callbackData.put("update_out_owner", arg.getUpdateOutOwner());
                //将原状态放入callbackData，用于审批通过和驳回之后恢复原状态
                callbackData.put(ObjectLifeStatus.LAST_LIFE_STATUS_API_NAME, ObjectDataExt.of(x).getLifeStatusText());
                callbackDataMap.put(x.getId(), callbackData);
            });
            if (dataList.size() > 1) {
                startApprovalFlowAsynchronous(dataList, ApprovalFlowTriggerType.CHANGE_PARTNER, dataMap, callbackDataMap);
                return BaseUpdatePartnerAction.Result.builder().errorCode("0").message(I18N.text(I18NKey.CHANGE_PARTNER_SUCCESS)).build();
            } else {
                startApprovalFlow(dataList, ApprovalFlowTriggerType.CHANGE_PARTNER, dataMap, callbackDataMap, null);
                if (isApprovalFlowStartSuccess(dataList.get(0).getId())) {
                    return BaseUpdatePartnerAction.Result.builder().errorCode("0").message(I18N.text(I18NKey.CHANGE_PARTNER_SUCCESS)).build();
                }
            }
        }

        // 更换合作伙伴id并将主负责人加入到相关团队中
        newOutInfo = changePartnerAndAddTeam(this.getActionContext().getUser(), dataList, arg.getPartnerId(), arg.getUpdateOutOwner());
        outInfoChangeModelList.forEach(model -> {
            model.setNewOutEI(newOutInfo.getKey());
            model.setNewOutUserId(newOutInfo.getValue().intValue());
            model.setDisplayName(objectDescribe.getDisplayName());
            model.setIsPreDefineObj("package".equals(objectDescribe.getDefineType()) ? true : false);
        });
        return result;
    }

    private Tuple<Integer, Long> changePartnerAndAddTeam(User user, List<IObjectData> dataList, String partnerId, Boolean updateOutOwner) {
        if (StringUtils.isEmpty(partnerId)) {
            return Tuple.of(0, 0L);
        }
        if (Boolean.FALSE.equals(updateOutOwner)) {
            OutUser outUser = getOutUser(user, partnerId);
            for (IObjectData data : dataList) {
                data.set(ObjectDataExt.PARTNER_ID, partnerId);
                // 将新合作伙伴的主对接人加入到普通成员中
                updateTeamNormalMember(data, outUser.getOutTenantId(), outUser.getOutUserId());
            }
            List<String> updateFields = Lists.newArrayList(ObjectDataExt.PARTNER_ID);
            serviceFacade.batchUpdateByFields(user, dataList, updateFields);
            serviceFacade.batchUpdateRelevantTeam(user, dataList, false);
            return Tuple.of(outUser.getOutTenantId(), outUser.getOutUserId());
        } else if (Boolean.TRUE.equals(updateOutOwner)) {
            // 更换外部负责人，页面走更换外部负责人接口
            for (IObjectData data : dataList) {
                data.set(ObjectDataExt.PARTNER_ID, partnerId);
            }
            List<String> updateFields = Lists.newArrayList(ObjectDataExt.PARTNER_ID);
            serviceFacade.batchUpdateByFields(user, dataList, updateFields);
            return Tuple.of(0, 0L);
        } else {
            OutUser outUser = getOutUser(user, partnerId);
            for (IObjectData data : dataList) {
                data.set(ObjectDataExt.PARTNER_ID, partnerId);
                // 更换外部负责人和外部相关团队
                String outTenant = outUser.getOutTenantId() == 0 ? null : outUser.getOutTenantId().toString();
                data.setOutTenantId(outTenant);
                data.setOutOwner(outUser.getOutUserId() == 0 ? null : Lists.newArrayList(outUser.getOutUserId().toString()));
                String outOwner = outUser.getOutUserId() == 0 ? null : outUser.getOutUserId().toString();
                updateOwnerToTeamMember(data, outOwner, outTenant);
            }
            List<String> updateFields = Lists.newArrayList(ObjectDataExt.PARTNER_ID, ObjectDataExt.OUTER_OWNER, ObjectDataExt.OUTER_TENANT);
            serviceFacade.batchUpdateByFields(user, dataList, updateFields);
            serviceFacade.batchUpdateRelevantTeam(user, dataList, false);
            return Tuple.of(outUser.getOutTenantId(), outUser.getOutUserId());
        }
    }

    private OutUser getOutUser(User user, String partnerId) {
        Integer outTenantId = 0;
        Long outOwnerId = 0L;
        Map<String, RelationDownstreamResult> downstreamMap = enterpriseRelationService.getRelationDownstreamInfo(user.getTenantId(), Sets.newHashSet(partnerId));
        RelationDownstreamResult downstream = downstreamMap.get(partnerId);
        if (downstream == null) {
            return OutUser.builder().outUserId(outOwnerId).outTenantId(outTenantId).partnerId(partnerId).build();
        }
        if (downstream.getDownstreamOuterTenantId() == null || downstream.getRelationOwnerOuterUid() == null) {
            log.warn("getOutUser but out user info is empty, tenant:{}, partnerId:{}", user.getTenantId(), partnerId);
            return OutUser.builder().outUserId(outOwnerId).outTenantId(outTenantId).partnerId(partnerId).build();
        }
        outTenantId = downstream.getDownstreamOuterTenantId();
        outOwnerId = downstream.getRelationOwnerOuterUid();
        return OutUser.builder().outUserId(outOwnerId).outTenantId(outTenantId).partnerId(partnerId).build();
    }

    private void updateTeamNormalMember(IObjectData data, Integer outTenantId, Long outOwnerId) {
        if (outTenantId != null && outOwnerId != null && outTenantId != 0 && outOwnerId != 0L) {
            TeamMember teamMember = new TeamMember(String.valueOf(outOwnerId), TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READANDWRITE, String.valueOf(outTenantId));
            //如果是从对象,则不需要处理相关团队,因为从对象的相关团队是绑定在主上的。
            if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
                return;
            }
            ObjectDataExt objectDataExt = ObjectDataExt.of(data);
            List<TeamMember> originalTeamMembers = objectDataExt.getTeamMembers();
            Optional<TeamMember> any = originalTeamMembers.stream().filter(m -> teamMember.getOutTenantId().equals(m.getOutTenantId()) && teamMember.getEmployee().equals(m.getEmployee())).findAny();
            if (any.isPresent()) {
                return;
            }
            originalTeamMembers.add(teamMember);
            objectDataExt.setTeamMembers(originalTeamMembers);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (!result.isSuccess()) {
            return result;
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return result;
        }
        // 异步触发审批流或成功触发审批流不需要执行后续操作
        if (isApprovalFlowStartSuccessOrAsynchronous(arg.getDataIds().get(0))) {
            return result;
        }
        result = super.after(arg, result);
        dealDetail(dataList, arg.getPartnerId(), newOutInfo.getKey(), newOutInfo.getValue());
        String apiName = actionContext.getObjectApiName();
        //表示是老对象
        Integer remindRecordType = 0;
        if (PrmConstant.unSupportOldObject.contains(apiName)) {
            remindRecordType = partnerRemindOutUserService.getChangePartnerRemindRecordType(apiName);
        } else {
            remindRecordType = 92;
        }
        //先拷贝一下再给异步线程使用，防止其他线程报ConcurrentModificationException
        List<IObjectData> dataListForLog = ObjectDataExt.copyList(objectDataList);
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        task.submit(() -> {
            for (IObjectData data : dataListForLog) {
                this.logChangePartner(actionContext.getUser(), EventType.MODIFY, ActionType.CHANGE_PARTNER, data, partnerObjData, objectDescribe);
            }
        });

        try {
            task.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        partnerRemindOutUserService.remindOutUser(actionContext.getUser(), apiName, remindRecordType, outInfoChangeModelList);


        return result;
    }


    protected void logChangePartner(User user, EventType eventType, ActionType actionType, IObjectData oldData, IObjectData partnerObjData, IObjectDescribe objectDescribe) {
        String partnerName = partnerObjData.getName();

        // 因查询接口不下发__r数据，需要查询合作伙伴信息
        String oldPartnerName = "";
        String partnerId = oldData.get("partner_id", String.class);
        if (StringUtils.isEmpty(partnerId)) {
            oldPartnerName = "";
        } else {
            IActionContext context = ActionContextExt.of(actionContext.getUser(), actionContext.getRequestContext()).getContext();
            List<INameCache> recordName = serviceFacade.findRecordName(context, Utils.PARTNER_API_NAME, Lists.newArrayList(partnerId));
            if (CollectionUtils.isNotEmpty(recordName)) {
                oldPartnerName = recordName.get(0).getName();
            }
        }


        List<LogInfo.LintMessage> textMsg = new ArrayList<>();
        StringBuilder sb = new StringBuilder(" , ");
        if (StringUtils.isNotEmpty(oldPartnerName)) {
            sb.append(I18N.text(I18NKey.ORIG_PARTNER, oldPartnerName));
        }
        sb.append(I18N.text(I18NKey.NEW_PARTNER, partnerName));
        textMsg.add(new LogInfo.LintMessage(sb.toString(), oldData.getId(), objectDescribe.getApiName()));
        LogInfo.ObjectSnapshot snapshot = LogInfo.ObjectSnapshot.builder().textMsg(textMsg).build();
        serviceFacade.logWithCustomMessage(user, eventType, actionType, objectDescribe, oldData, snapshot.getMessage());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CHANGE_PARTNER.getButtonApiName();
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        /**
         * 数据ID列表
         */
        private List<String> dataIds;

        /**
         * 合作伙伴数据id
         */
        private String partnerId;

        /**
         * 更新外部负责人
         * updateOutOwner true 的时候只更新合作伙伴
         * updateOutOwner false 的时候更换合作伙伴并根据合作伙伴更换外部负责人
         * 默认 null == false
         * 这个参数代表的是页面上是否已经由用户更换了外部负责人，如果 true 代表已经更换过了，那么接下来自然就不需要再更换了
         */
        private Boolean updateOutOwner;

        public static StandardChangePartnerAction.Arg of(String dataId, String partnerId, Boolean updateOutOwner) {
            return new StandardChangePartnerAction.Arg(Lists.newArrayList(dataId), partnerId, updateOutOwner);
        }

        public static Arg of(Map<String, Object> callBackData, String dataId) {
            Boolean updateOutOwner = false;
            if (callBackData.get("update_out_owner") != null) {
                updateOutOwner = (Boolean) callBackData.get("update_out_owner");
            }

            return new StandardChangePartnerAction.Arg(Lists.newArrayList(dataId), (String) callBackData.get("partnerId"), updateOutOwner);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class OutUser {
        private Integer outTenantId;
        private Long outUserId;
        private String partnerId;
    }
}

