package com.facishare.paas.appframework.core.predef.action;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.facishare.paas.appframework.metadata.SceneLogicService.IS_OUTER;

public class StandardDeleteOuterSceneAction extends AbstractStandardAction<StandardDeleteOuterSceneAction.Arg, StandardDeleteOuterSceneAction.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        actionContext.setAttribute(IS_OUTER, true);
    }

    @Override
    protected Result doAct(Arg arg) {
        infraServiceFacade.deleteTenantScene(arg.getDescribeApiName(), arg.getSceneApiName(), arg.getExtendAttribute(), actionContext.getUser());
        return new Result(true);
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Arg {
        private String describeApiName;
        private String sceneApiName;
        private String extendAttribute;
        private String appId;
    }


    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class Result {
        private boolean success;
    }
}
