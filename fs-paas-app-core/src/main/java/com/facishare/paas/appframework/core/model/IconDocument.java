package com.facishare.paas.appframework.core.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.IconExt;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IconDocument {
    @J<PERSON>NField(name = "icon_index")
    @JsonProperty(value = "icon_index")
    @SerializedName(value = "icon_index")
    private Integer iconIndex;

    @JSONField(name = "icon_path")
    @JsonProperty(value = "icon_path")
    @SerializedName(value = "icon_path")
    private String iconPath;

    public static List<IconDocument> ofList(List<IconExt> list) {
        return list.stream().map(a->IconDocument.builder()
                .iconIndex(a.getIconIndex())
                .iconPath(a.getIconPath())
                .build())
                .collect(Collectors.toList());
    }
}
