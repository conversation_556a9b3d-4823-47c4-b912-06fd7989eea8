package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.PreDefineAction;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public class StandardBulkEditByRecordAction extends PreDefineAction<StandardBulkEditByRecordAction.Arg, StandardBulkEditByRecordAction.Result>{

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.BulkEditByRecord.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected Result doAct(Arg arg) {
        return null;
    }

    @Data
    public static class Arg {

    }

    @Data
    @NoArgsConstructor
    public static class Result {

    }
}
