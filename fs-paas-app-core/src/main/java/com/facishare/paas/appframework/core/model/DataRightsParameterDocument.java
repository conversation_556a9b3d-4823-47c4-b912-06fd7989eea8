package com.facishare.paas.appframework.core.model;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.impl.search.DataRightsParameter;

import java.util.Map;

public class DataRightsParameterDocument extends DocumentBaseEntity {

    public DataRightsParameterDocument() {
    }

    public DataRightsParameterDocument(Map<String, Object> map) {
        super(map);
    }

    public static DataRightsParameterDocument of(Map<String, Object> data) {
        return new DataRightsParameterDocument(data);
    }

    public IDataRightsParameter toDataRightsParameter() {
        return JSON.parseObject(JSON.toJSONString(data), DataRightsParameter.class);
    }

}
