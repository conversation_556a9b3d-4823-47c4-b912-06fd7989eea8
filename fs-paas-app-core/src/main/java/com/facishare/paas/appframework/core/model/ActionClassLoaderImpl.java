package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ActionClassLoadException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.util.Types;
import com.google.common.base.Strings;
import org.springframework.stereotype.Service;

/**
 * Action class 加载器
 * <p>
 * Created by liyiguang on 2017/6/21.
 */
@Service
public class ActionClassLoaderImpl implements ActionClassLoader {

    @Override
    public <T extends AbstractAction> Class<T> loadActionClass(ActionClassInfo classInfo) {
        if (classInfo.isPreDefine()) {
            return (Class<T>) loadPreDefineActionClass(classInfo);
        } else {
            return (Class<T>) loadCustomerAction(classInfo);
        }
    }

    Class<? extends PreDefineAction> loadPreDefineActionClass(ActionClassInfo classInfo) {
        StopWatch stopWatch = StopWatch.create("loadPreDefineActionClass");
        try {
            Class<?> clazz = Types.forName(classInfo.getClassName());
            stopWatch.lap("Class.forName");
            if (clazz == null) {
                throw new ActionClassLoadException(SystemErrorCode.ACTION_FOUND_ERROR);
            }
            if (PreDefineAction.class.isAssignableFrom(clazz)) {
                stopWatch.lap("isAssignableFrom");
                return (Class<PreDefineAction>) clazz;
            } else {
                throw new ActionClassLoadException(SystemErrorCode.ACTION_LOAD_ERROR);
            }
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    @Override
    public boolean check(ActionClassInfo classInfo) {
        if (classInfo == null || Strings.isNullOrEmpty(classInfo.getClassName())) {
            return false;
        }
        StopWatch stopWatch = StopWatch.create("checkAction");
        try {
            Class clazz = Types.forName(classInfo.getClassName());
            stopWatch.lap("Class.forName");
            return clazz != null;
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    Class<?> loadCustomerAction(ActionClassInfo classInfo) {
        //TODO:
        return null;
    }
}
