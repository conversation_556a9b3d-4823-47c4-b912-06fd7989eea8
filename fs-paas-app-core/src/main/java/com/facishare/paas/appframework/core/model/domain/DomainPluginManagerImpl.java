package com.facishare.paas.appframework.core.model.domain;

import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by zhouweirong on 2021/10/18.
 */
@Component
@Slf4j
public class DomainPluginManagerImpl implements DomainPluginManager {

    private static final String REMOTE_PREFIX = "_remote";
    private static final Joiner KEY_JOINER = Joiner.on("_").skipNulls();

    private final Map<String, ActionDomainPlugin> actionPluginMap = Maps.newConcurrentMap();
    private final Map<String, ActionDomainPlugin> remoteActionPluginMap = Maps.newConcurrentMap();
    private final Map<String, ControllerDomainPlugin> controllerPluginMap = Maps.newConcurrentMap();
    private final Map<String, ControllerDomainPlugin> remoteControllerPluginMap = Maps.newConcurrentMap();

    @Override
    public void register(Object bean) {
        Class<?> clazz = AopUtils.getTargetClass(bean);
        DomainProvider provider = clazz.getAnnotation(DomainProvider.class);
        if (provider == null || Strings.isNullOrEmpty(provider.name())) {
            return;
        }
        String name = provider.name();
        if (bean instanceof ActionDomainPlugin) {
            if (name.startsWith(REMOTE_PREFIX)) {
                remoteActionPluginMap.putIfAbsent(name, (ActionDomainPlugin) bean);
            } else {
                actionPluginMap.putIfAbsent(name, (ActionDomainPlugin) bean);
            }
            log.info("register action domain plugin:{}-{}", name, clazz);
        } else if (bean instanceof ControllerDomainPlugin) {
            if (name.startsWith(REMOTE_PREFIX)) {
                remoteControllerPluginMap.putIfAbsent(name, (ControllerDomainPlugin) bean);
            } else {
                controllerPluginMap.putIfAbsent(name, (ControllerDomainPlugin) bean);
            }
            log.info("register controller domain plugin:{}-{}", name, clazz);
        }
    }

    @Override
    public <T extends ActionDomainPlugin> T getActionPlugin(String pluginApiName, String actionCode) {
        ActionDomainPlugin plugin = actionPluginMap.get(buildKey(pluginApiName, actionCode));
        if (plugin == null) {
            plugin = remoteActionPluginMap.get(buildKey(REMOTE_PREFIX, actionCode));
        }
        return (T) plugin;
    }

    @Override
    public <T extends ControllerDomainPlugin> T getControllerPlugin(String pluginApiName, String controllerCode) {
        ControllerDomainPlugin plugin = controllerPluginMap.get(buildKey(pluginApiName, controllerCode));
        if (plugin == null) {
            plugin = remoteControllerPluginMap.get(buildKey(REMOTE_PREFIX, controllerCode));
        }
        return (T) plugin;
    }

    private String buildKey(String pluginApiName, String code) {
        return KEY_JOINER.join(pluginApiName, code);
    }
}
