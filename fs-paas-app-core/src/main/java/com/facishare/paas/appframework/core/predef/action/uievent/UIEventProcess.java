package com.facishare.paas.appframework.core.predef.action.uievent;

import com.facishare.ocr.api.model.OcrData;
import com.facishare.paas.appframework.core.predef.action.StandardTriggerEventAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.experimental.Delegate;

import java.util.List;
import java.util.Map;

/**
 * UI事件处理管道，至少包含一个Basic处理器，
 * Basic处理器在UI事件业务中为函数计算，
 * 第一个执行函数处理器。可添加处理器，
 * 如计算处理器，Bisic之外的处理器加入到
 * List中，按添加顺序执行
 *
 * <AUTHOR>
 * @date 2019-08-05 18:02
 */
public interface UIEventProcess {
    /**
     * 设置基本处理器： 执行函数
     */
    void setBasic(Processor basic);

    /**
     * 根据业务需求添加处理器，如计算
     */
    void addProcessor(Processor processor);

    /**
     * 依次调用Processor处理数据
     */
    void invoke(ProcessRequest processRequest);

    /**
     * 按照顺序批量添加处理器,执行顺序与传入顺序相同
     * 至少应该传入一个Processor
     *
     * @param processors 处理器
     */
    void addOrderedProcessors(Processor... processors);

    Processor getBasic();

    /**
     * 处理结果
     */
    @Data
    @Builder
    class ProcessRequest {
        IObjectData masterData;                                             // 主对象数据
        IObjectData masterWithOnlyChangedFields;

        Map<String, List<IObjectData>> detailDataMap;                       // 从对象数据
        Map<String, List<IObjectData>> detailWithOnlyChangedFields;

        List<String> triggerFieldAPIName;                                   //触发的字段

        Map<String, Object> remind;                                          // 提醒

        Map<String, Map<String, Boolean>> fieldAttribute;                     //字段隐藏/只读配置

        boolean remindEvent;

        Map<String, Map<String, Object>> objectAttribute;                   //从对象隐藏/显示属性
        /**
         * 790 UI事件能力提升
         */
        Map<String, List<Map<String, Object>>> deletedDetails;                    //删除明细事件被删除数据
        Map<String, Map<String, Object>> detailFieldAttribute;              //从对象字段隐藏只读必填
        Map<String, Map<String, Object>> detailRowFieldAttribute;              //从对象单条数据字段隐藏只读必填
        Map<String, Map<String,Object>> detailRecordType;                  //从对象业务类型的隐藏
        Map<String, Map<String, Object>> optionAttribute;                   //主从对象隐藏选项值
        Map<String, List<Map<String, Object>>> detailButton;                   //从对象按钮隐藏

        /**
         * ocr 识别结果
         */
        private List<StandardTriggerEventAction.OcrResult> ocrDataResult;
        /**
         * 业务信息参数
         */
        Map<String, Object> bizInfo;

        //是否计算从对象的默认值和计算字段
        boolean doCalculate;

        String triggerPage;
    }

}
