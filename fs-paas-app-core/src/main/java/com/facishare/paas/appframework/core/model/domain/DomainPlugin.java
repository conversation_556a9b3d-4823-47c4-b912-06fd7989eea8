package com.facishare.paas.appframework.core.model.domain;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ValidationResult;
import com.facishare.paas.appframework.metadata.domain.SimpleDomainPluginDescribe;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouweirong on 2021/10/18.
 */
public interface DomainPlugin {

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private SimpleDomainPluginDescribe pluginDescribe;
        private String objectApiName;
        //主流程中的doAct方法是否执行成功
        private boolean doActComplete;
        //主流程是否处理完成
        private boolean processComplete;
        //自定义的上下文数据
        private Map<String, String> contextData;
        //各个接口的额外参数
        private Map<String, Object> extraData;
        //是否handler模式
        private boolean actByHandler;
        //需要跳过的校验逻辑
        private List<String> skippedStepKeys;

        public String getContextData(String key) {
            if (CollectionUtils.empty(contextData)) {
                return null;
            }
            return contextData.get(key);
        }

        public void putContextData(String key, String value) {
            if (Objects.isNull(contextData)) {
                contextData = Maps.newHashMap();
            }
            contextData.put(key, value);
        }
    }

    @Data
    class Result {
        //自定义的上下文数据
        private Map<String, String> contextData;
        //校验结果（当需要返回非阻断提示信息时使用）
        private ValidationResult validationResult;

        public String getContextData(String key) {
            if (CollectionUtils.empty(contextData)) {
                return null;
            }
            return contextData.get(key);
        }

        public void putContextData(String key, String value) {
            if (Objects.isNull(contextData)) {
                contextData = Maps.newHashMap();
            }
            contextData.put(key, value);
        }
    }

}
