package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.metadata.api.describe.DynamicDescribe;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.bson.Document;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2017/10/11
 */
public class ObjectDescribeDocument extends DocumentBaseEntity {

    public ObjectDescribeDocument() {
    }

    public ObjectDescribeDocument(Map<String, Object> data) {
        super(data);
    }

    public IObjectDescribe toObjectDescribe() {
        return new ObjectDescribe(data);
    }

    public static ObjectDescribeDocument of(DynamicDescribe dynamicDescribe, List<String> describeSelects, List<String> fieldSelects) {
        ObjectDescribeDocument document = new ObjectDescribeDocument();
        document.put(IObjectDescribe.API_NAME, dynamicDescribe.getApiName());
        CollectionUtils.nullToEmpty(describeSelects).forEach(k -> document.put(k, dynamicDescribe.get(k)));
        Map<String, ObjectFieldDescribeDocument> fields = Maps.newLinkedHashMap();
        dynamicDescribe.getFields().forEach(f -> {
            ObjectFieldDescribeDocument field = new ObjectFieldDescribeDocument();
            field.put(IFieldDescribe.API_NAME, f.getApiName());
            CollectionUtils.nullToEmpty(fieldSelects).forEach(k -> field.put(k, f.get(k)));
            fields.put(f.getApiName(), field);
        });
        document.put(IObjectDescribe.FIELDS, fields);

        return document;
    }

    public static ObjectDescribeDocument of(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        return new ObjectDescribeDocument(data);
    }

    public static ObjectDescribeDocument of(IObjectDescribe objectDescribe) {
        if (objectDescribe == null) {
            return null;
        }
        return new ObjectDescribeDocument(ObjectDescribeExt.of(objectDescribe).toMap());
    }

    public static ObjectDescribeDocument of(ObjectDescribeExt objectDescribeExt) {
        if (objectDescribeExt == null) {
            return null;
        }
        return new ObjectDescribeDocument(objectDescribeExt.toMap());
    }

    public static ObjectDescribeDocument of(DescribeExtra describeExtra) {
        return Optional.ofNullable(describeExtra)
                .map(it -> ObjectDescribeDocument.of(it.getDescribeExpansion()))
                .orElse(null);
    }

    public static List<ObjectDescribeDocument> ofList(List<IObjectDescribe> describeList) {
        if (CollectionUtils.empty(describeList)) {
            return Lists.newArrayList();
        }
        return describeList.stream().map(ObjectDescribeDocument::of).collect(Collectors.toList());
    }

    public static Map<String, ObjectDescribeDocument> ofMap(Map<String, IObjectDescribe> describeMap) {
        if (CollectionUtils.empty(describeMap)) {
            return Maps.newHashMap();
        }
        Map<String, ObjectDescribeDocument> resultMap = Maps.newHashMap();
        describeMap.forEach((apiName, describe) -> resultMap.put(apiName, of(describe)));
        return resultMap;
    }

    public static ObjectDescribeDocument handleDescribeCache(Map<String, Integer> versionMap, IObjectDescribe describe) {
        if (CollectionUtils.empty(versionMap) || !versionMap.containsKey(describe.getApiName())) {
            return ObjectDescribeDocument.of(describe);
        }
        //预设对象只有在配置中心配置了才支持终端缓存
        if (!ObjectDescribeExt.of(describe).isCustomObject() && !AppFrameworkConfig.isPackageObjectSupportDescribeCache(describe.getTenantId(), describe.getApiName())) {
            return ObjectDescribeDocument.of(describe);
        }

        Integer version = versionMap.get(describe.getApiName());
        // 版本号相同，不需要下发描述
        return Objects.equals(describe.getVersion(), version) ? null : ObjectDescribeDocument.of(describe);
    }

    public static void projectField(List<ObjectDescribeDocument> describeList, List<String> attributes) {
        if (CollectionUtils.empty(describeList)) {
            return;
        }
        describeList.forEach(a -> a.projectField(attributes));
    }

    public void filterFieldsAttributes() {
        Object o = get(IObjectDescribe.FIELDS);
        if (o instanceof Map) {
            Map<String, Map> fields = (Map<String, Map>) o;
            for (Map<String, Object> field : fields.values()) {
                Lists.newArrayList(field.keySet()).forEach(key -> {
                    if (!AppFrameworkConfig.isSimpleDescribeFieldAttrWhite(key)) {
                        field.remove(key);
                    }
                });
            }
        }
    }
}
