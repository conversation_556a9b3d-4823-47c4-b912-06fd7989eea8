package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.metadata.config.IUdefConfig;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/06/09
 */
public class StandardConfigDocument extends DocumentBaseEntity {
    private static final long serialVersionUID = -3207852466693455616L;

    public StandardConfigDocument() {
    }

    public StandardConfigDocument(Map<String, Object> data) {
        super(data);
    }

    public static StandardConfigDocument of(IUdefConfig buttonConfig) {
        if (Objects.isNull(buttonConfig)) {
            return null;
        }
        return new StandardConfigDocument(buttonConfig.toMap());
    }

    public static List<StandardConfigDocument> ofList(List<IUdefConfig> configs) {
        return configs.stream()
                .map(StandardConfigDocument::of)
                .collect(Collectors.toList());
    }
}
