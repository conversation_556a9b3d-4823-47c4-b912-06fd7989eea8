package com.facishare.paas.appframework.core.predef.action;


import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.GetPrintTemplate;
import com.facishare.paas.appframework.common.util.ImportConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import org.apache.commons.validator.ValidatorException;

import java.util.List;
import java.util.Objects;


/**
 * 导出excel模板验证
 *
 * <AUTHOR>
 * @date 2021/08/08
 */
public class StandardExportExcelTemplateVerifyAction extends BaseExportVerifyAction {

    // TODO 增加校验打印模板id的逻辑

    /**
     * 默认最大允许导出数量。
     */
    public final int EXPORT_PRINT_TEMPLATE_EXCEL_THROTTLE;
    private static final int PRINT_EXCEL = 2;
    public StandardExportExcelTemplateVerifyAction() {
        EXPORT_PRINT_TEMPLATE_EXCEL_THROTTLE = ImportConfig.getExportPrintTemplateExcelThrottle();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.ExportExcelTemplateVerify.getFunPrivilegeCodes();
    }

    @Override
    protected int getExportRowsThrottle() {
        return EXPORT_PRINT_TEMPLATE_EXCEL_THROTTLE;
    }

    @Override
    protected void validateCount() {
        GetPrintTemplate.Arg getPrintTemplateArg = GetPrintTemplate.Arg.builder()
                .templateId(this.arg.getPrintTemplateId())
                .build();
        GetPrintTemplate.Result printTemplateResult = infraServiceFacade.findPrintTemplate(actionContext.getUser(), getPrintTemplateArg);
        if (Objects.isNull(printTemplateResult) || Objects.isNull(printTemplateResult.getPrintTemplateVO())
                || PRINT_EXCEL != printTemplateResult.getPrintTemplateVO().getIsToWord()) {
            log.warn("Export excel template validate Error, TenantId:{}, UserId:{}, printTemplateResult:{}.", actionContext.getTenantId(),
                    actionContext.getUser().getUserId(), printTemplateResult);
            throw new ValidateException(I18N.text(I18NKey.EXCEL_PRINT_TEMPLATE_NOT_EXIST));
        }
        super.validateCount();
    }
}

