package com.facishare.paas.appframework.core.model.handler;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 * 业务处理器的Spring bean过滤器，用于将Handler类型的Spring bean注册到管理器中
 * Created by zhouwr on 2023/1/6.
 */
@Component
public class HandlerBeanPostProcessor implements BeanPostProcessor {

    @Autowired
    private HandlerManager handlerManager;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof Handler) {
            handlerManager.register((Handler) bean);
        }
        return bean;
    }
}
