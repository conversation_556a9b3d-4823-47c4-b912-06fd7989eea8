package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.Pair;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.TeamMemberInfoPoJo;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.predef.action.StandardEditTeamMemberAction.Arg;
import static com.facishare.paas.appframework.core.predef.action.StandardEditTeamMemberAction.Result;
import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.DEPT_TEAM_MEMBER_REMIND_GRAY;

@Slf4j
public class StandardEditTeamMemberAction extends BaseTeamMemberAction<Arg, Result> {

    private List<IObjectData> orgDataList = new ArrayList<>();
    private List<String> failReasons = Lists.newArrayList();

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.EditTeamMember.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataID());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.EDIT_TEAM_MEMBER.getButtonApiName();
    }

    @Override
    protected boolean isBatchAction() {
        return false;
    }

    @Override
    protected Result doAct(Arg arg) {
        List<IObjectData> objectDataListToUpdate = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),
                Lists.newArrayList(arg.getDataID()), actionContext.getObjectApiName());
        objectDataListToUpdate.forEach(f -> orgDataList.add(ObjectDataExt.of(f).copy()));

        for (IObjectData objectData : objectDataListToUpdate) {
            checkOwnerChangeInTeamMember(actionContext.getUser(), objectData, arg.getTeamMemberInfos());
            try {
                serviceFacade.checkActionByLockStatusAndLifeStatus(objectData, ObjectAction.EDIT_TEAM_MEMBER,
                        actionContext.getUser(), actionContext.getObjectApiName(), false);
            } catch (Exception e) {
                failReasons.add(I18NExt.getOrDefault(I18NKey.OPERATION_FAILED, "操作{0}失败,原因:{1}",// ignoreI18n
                        objectData.getName(), e.getMessage()));
                continue;
            }

            updateObjectDataByEditMember(arg.getTeamMemberInfos(), objectData);
        }
        bulkOpResult = serviceFacade.batchUpdateRelevantTeam(actionContext.getUser(), objectDataListToUpdate, false);
        return Result.builder().build();
    }

    private void checkOwnerChangeInTeamMember(User user, IObjectData objectData, List<TeamMemberInfoPoJo> teamMemberInfos) {
        if (!arg.needCheckOwnerChangeInTeamMember()) {
            return;
        }
        String ownerId = ObjectDataExt.of(objectData).getOwnerId().orElse(null);
        if (Strings.isNullOrEmpty(ownerId)) {
            return;
        }
        boolean match = false;
        for (TeamMemberInfoPoJo teamMemberInfo : teamMemberInfos) {
            if (TeamMember.MemberType.EMPLOYEE.getValue().equals(teamMemberInfo.getTeamMemberType())
                    && teamMemberInfo.getTeamMemberEmployee().contains(ownerId)) {
                match = true;
                break;
            }
        }
        if (!match) {
            throw new ValidateException(I18NExt.text(I18NKey.CANNOT_CHANGE_OWNER_WHEN_EDIT_TEAM_MEMBER));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        serviceFacade.logByActionType(actionContext.getUser(), EventType.MODIFY, ActionType.ModifySale, orgDataList, bulkOpResult.getSuccessObjectDataList(), objectDescribe);
        serviceFacade.sendActionMq(actionContext.getUser(), bulkOpResult.getSuccessObjectDataList(), ObjectAction.EDIT_TEAM_MEMBER);
        // 发送CRM提醒
        sendReminder(Lists.newArrayList(arg.getDataID()), orgDataList, bulkOpResult.getSuccessObjectDataList());
        if (CollectionUtils.notEmpty(bulkOpResult.getFailObjectDataList())) {
            failReasons.add(bulkOpResult.getFailReason());
        }
        if (CollectionUtils.notEmpty(failReasons)) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.FAILED_DATA, "以下部分数据执行失败:{0}",// ignoreI18n
                    StringUtils.join(failReasons, ',')));
        }
        return super.after(arg, result);
    }

    @Override
    protected Map<String, Object> getActionParams(Arg arg) {
        return arg.toButtonArg();
    }

    @Deprecated
    @Override
    protected void diffTeamMemberInfoEmployee(List<String> dataIds,
                                              Table<String, String, TeamMemberInfoPoJo> teamMemberInfoTable,
                                              Table<String, String, TeamMemberInfoPoJo> dbTeamMemberInfoTable) {
        Map<String, Set<Integer>> toAdd = Maps.newHashMap();
        Map<String, Set<Integer>> toDelete = Maps.newHashMap();

        Map<String, Set<String>> toAddRole = Maps.newHashMap();
        Map<String, Set<String>> toDeleteRole = Maps.newHashMap();
        dataIds.forEach(dataId -> {
            Map<String, TeamMemberInfoPoJo> teamMemberInfoMap = Optional.ofNullable(teamMemberInfoTable.row(dataId))
                    .orElseGet(Collections::emptyMap);
            Map<String, TeamMemberInfoPoJo> dbTeamMemberInfoMap = Optional.ofNullable(dbTeamMemberInfoTable.row(dataId))
                    .orElseGet(Collections::emptyMap);
            MapDifference<String, TeamMemberInfoPoJo> difference = Maps.difference(dbTeamMemberInfoMap, teamMemberInfoMap);
            // 被删除的
            Map<String, TeamMemberInfoPoJo> toDeleteTeamMemberInfoPoJoMap = difference.entriesOnlyOnLeft();
            // 新增加的
            Map<String, TeamMemberInfoPoJo> toAddTeamMemberInfoPoJoMap = difference.entriesOnlyOnRight();
            toDelete.putAll(getEmployeeMap(dataId, toDeleteTeamMemberInfoPoJoMap));
            toDeleteRole.putAll(getTeamMemberRoleMap(dataId, toDeleteTeamMemberInfoPoJoMap));

            toAdd.putAll(getEmployeeMap(dataId, toAddTeamMemberInfoPoJoMap));
            toAddRole.putAll(getTeamMemberRoleMap(dataId, toAddTeamMemberInfoPoJoMap));
        });
        if (BooleanUtils.isNotTrue(arg.getIgnoreSendingRemind())) {
            sendNotification(toDelete, null, I18NKey.CANCELLED_ROLE_IDENTITY, toDeleteRole, teamRoleInfos);
            sendNotification(toAdd, null, I18NKey.ADD_ROLE, toAddRole, teamRoleInfos);
        }
    }

    @Override
    protected void diffTeamMemberInfo(List<String> dataIds,
                                      List<DataTeamMember> teamMemberInfoList,
                                      List<DataTeamMember> dbTeamMemberInfoList) {
        //根据成员类型分组，不同类型有不同的发通知方法
        Map<String, List<DataTeamMember>> teamMemberMap = getMemberMapByType(teamMemberInfoList);
        Map<String, List<DataTeamMember>> dbTeamMemberMap = getMemberMapByType(dbTeamMemberInfoList);

        // 发送人员的通知
        sendEmployeeRemind(dataIds, teamMemberMap, dbTeamMemberMap);

        if (UdobjGrayConfig.isAllow(DEPT_TEAM_MEMBER_REMIND_GRAY, actionContext.getTenantId())) {
            // 发送部门的通知
            sendDeptRemind(dataIds, teamMemberMap, dbTeamMemberMap);
        }

    }

    private void sendDeptRemind(List<String> dataIds, Map<String, List<DataTeamMember>> teamMemberMap, Map<String, List<DataTeamMember>> dbTeamMemberMap) {
        List<DataTeamMember> deptTeamList = getMemberListByType(teamMemberMap, TeamMember.MemberType.DEPARTMENT);
        List<DataTeamMember> deptDBTeamList = getMemberListByType(dbTeamMemberMap, TeamMember.MemberType.DEPARTMENT);

        Map<String, List<Pair<Integer, Boolean>>> toAddDept = Maps.newHashMap();
        Map<String, List<Pair<Integer, Boolean>>> toDeleteDept = Maps.newHashMap();

        Map<String, Set<String>> toAddRoleDept = Maps.newHashMap();
        Map<String, Set<String>> toDeleteRoleDept = Maps.newHashMap();
        dataIds.forEach(dataId -> {
            Map<String, TeamMemberInfoPoJo> teamMemberInfoMap = Optional.ofNullable(DataTeamMember.parseValueMapByData(deptTeamList, dataId))
                    .orElseGet(Collections::emptyMap);
            Map<String, TeamMemberInfoPoJo> dbTeamMemberInfoMap = Optional.ofNullable(DataTeamMember.parseValueMapByData(deptDBTeamList, dataId))
                    .orElseGet(Collections::emptyMap);
            MapDifference<String, TeamMemberInfoPoJo> difference = Maps.difference(dbTeamMemberInfoMap, teamMemberInfoMap);
            // 被删除的
            Map<String, TeamMemberInfoPoJo> toDeleteTeamMemberInfoPoJoMap = difference.entriesOnlyOnLeft();
            // 新增加的
            Map<String, TeamMemberInfoPoJo> toAddTeamMemberInfoPoJoMap = difference.entriesOnlyOnRight();
            toDeleteDept.putAll(getDeptMap(dataId, toDeleteTeamMemberInfoPoJoMap));
            toDeleteRoleDept.putAll(getTeamMemberRoleMap(dataId, toDeleteTeamMemberInfoPoJoMap));

            toAddDept.putAll(getDeptMap(dataId, toAddTeamMemberInfoPoJoMap));
            toAddRoleDept.putAll(getTeamMemberRoleMap(dataId, toAddTeamMemberInfoPoJoMap));

        });

        if (BooleanUtils.isNotTrue(arg.getIgnoreSendingRemind())) {
            sendNotification(null, toDeleteDept, I18NKey.CANCELLED_ROLE_IDENTITY, toDeleteRoleDept, teamRoleInfos);
            sendNotification(null, toAddDept, I18NKey.ADD_ROLE, toAddRoleDept, teamRoleInfos);
        }
    }

    private void sendEmployeeRemind(List<String> dataIds, Map<String, List<DataTeamMember>> teamMemberMap, Map<String, List<DataTeamMember>> dbTeamMemberMap) {
        List<DataTeamMember> employeeTeamList = getMemberListByType(teamMemberMap, TeamMember.MemberType.EMPLOYEE);
        List<DataTeamMember> employeeDBTeamList = getMemberListByType(dbTeamMemberMap, TeamMember.MemberType.EMPLOYEE);

        Map<String, Set<Integer>> toAdd = Maps.newHashMap();
        Map<String, Set<Integer>> toDelete = Maps.newHashMap();

        Map<String, Set<String>> toAddRole = Maps.newHashMap();
        Map<String, Set<String>> toDeleteRole = Maps.newHashMap();
        dataIds.forEach(dataId -> {
            Map<String, TeamMemberInfoPoJo> teamMemberInfoMap = Optional.ofNullable(DataTeamMember.parseValueMapByData(employeeTeamList, dataId))
                    .orElseGet(Collections::emptyMap);
            Map<String, TeamMemberInfoPoJo> dbTeamMemberInfoMap = Optional.ofNullable(DataTeamMember.parseValueMapByData(employeeDBTeamList, dataId))
                    .orElseGet(Collections::emptyMap);
            MapDifference<String, TeamMemberInfoPoJo> difference = Maps.difference(dbTeamMemberInfoMap, teamMemberInfoMap);
            // 被删除的
            Map<String, TeamMemberInfoPoJo> toDeleteTeamMemberInfoPoJoMap = difference.entriesOnlyOnLeft();
            // 新增加的
            Map<String, TeamMemberInfoPoJo> toAddTeamMemberInfoPoJoMap = difference.entriesOnlyOnRight();
            toDelete.putAll(getEmployeeMap(dataId, toDeleteTeamMemberInfoPoJoMap));
            toDeleteRole.putAll(getTeamMemberRoleMap(dataId, toDeleteTeamMemberInfoPoJoMap));

            toAdd.putAll(getEmployeeMap(dataId, toAddTeamMemberInfoPoJoMap));
            toAddRole.putAll(getTeamMemberRoleMap(dataId, toAddTeamMemberInfoPoJoMap));

        });

        if (BooleanUtils.isNotTrue(arg.getIgnoreSendingRemind())) {
            sendNotification(toDelete, null, I18NKey.CANCELLED_ROLE_IDENTITY, toDeleteRole, teamRoleInfos);
            sendNotification(toAdd, null, I18NKey.ADD_ROLE, toAddRole, teamRoleInfos);
        }
    }

    @Override
    protected void validateArg(Arg arg) {
        List<TeamMemberInfoPoJo> teamMemberInfos = arg.getTeamMemberInfos();
        if (CollectionUtils.empty(teamMemberInfos)) {
            return;
        }
        for (TeamMemberInfoPoJo teamMemberInfo : teamMemberInfos) {
            if (!teamMemberInfo.validate()) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
        }
    }

    private void updateObjectDataByEditMember(List<TeamMemberInfoPoJo> teamMemberInfoPoJoList, IObjectData objectData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = new ArrayList<>();
        Optional<String> ownerId = objectDataExt.getOwnerId();
        Optional<String> outOwnerId = objectDataExt.getOutOwnerId();
        boolean hasOwner = false;
        boolean hasOutOwner = false;
        for (TeamMemberInfoPoJo teamMemberInfo : teamMemberInfoPoJoList) {
            List<String> teamMemberRoleList = Lists.newArrayList();
            if (CollectionUtils.empty(teamMemberInfo.getTeamMemberRoleList())) {
                teamMemberRoleList.add(teamMemberInfo.getTeamMemberRole());
            } else {
                teamMemberRoleList.addAll(teamMemberInfo.getTeamMemberRoleList());
            }
            for (String userId : teamMemberInfo.getTeamMemberEmployee()) {
                for (String roleCode : teamMemberRoleList) {
                    TeamMember.Permission permission = TeamMember.Permission.of(teamMemberInfo.getTeamMemberPermissionType());
                    //负责人不允许更新
                    if (ownerId.isPresent()
                            && TeamMember.MemberType.EMPLOYEE.getValue().equals(teamMemberInfo.getTeamMemberType())
                            && Objects.equals(userId, ownerId.get())) {
                        hasOwner = true;
                        permission = TeamMember.Permission.READANDWRITE;
                    }
                    // 外部负责人不允许更新
                    if (outOwnerId.isPresent()
                            && TeamMember.MemberType.EMPLOYEE.getValue().equals(teamMemberInfo.getTeamMemberType())
                            && Objects.equals(userId, outOwnerId.get())) {
                        hasOutOwner = true;
                        permission = TeamMember.Permission.READANDWRITE;
                    }
                    if (TeamMember.Role.OWNER.getValue().equals(roleCode)) {
                        continue;
                    }
                    TeamMember.Role role = null;
                    if (!customTeamRoleGray) {
                        role = TeamMember.Role.of(roleCode);
                    }
                    TeamMember teamMember = new TeamMember(userId, roleCode, role,
                            permission,
                            teamMemberInfo.getOutTenantId(),
                            TeamMember.MemberType.of(teamMemberInfo.getTeamMemberType()),
                            teamMemberInfo.getTeamMemberDeptCascade());
                    teamMembers.add(teamMember);
                }
            }
        }
        //添加负责人 (负责人存在时才需要增加负责人（客户和线索有负责人为空的情况））
        if (ownerId.isPresent()) {
            if (!hasOwner) {
                List<TeamMember> ownerTeamMember = objectDataExt.getTeamMembers().stream()
                        .filter(x -> Objects.equals(x.getEmployee(), ownerId.get())
                                && TeamMember.MemberType.EMPLOYEE.equals(x.getMemberType()))
                        .collect(Collectors.toList());
                teamMembers.addAll(ownerTeamMember);
            } else {
                teamMembers.add(new TeamMember(ownerId.get(), TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE));
            }
        }

        // 添加外部负责人,外部负责人和外部企业必须都存在
        if (outOwnerId.isPresent() && StringUtils.isNotBlank(objectDataExt.getOutTenantId())) {
            if (!hasOutOwner) {
                List<TeamMember> ownerTeamMember = objectDataExt.getTeamMembers().stream()
                        .filter(x -> Objects.equals(x.getEmployee(), outOwnerId.get())
                                && x.isOutMember()
                                && TeamMember.MemberType.EMPLOYEE.equals(x.getMemberType()))
                        .collect(Collectors.toList());
                teamMembers.addAll(ownerTeamMember);
            } else {
                teamMembers.add(new TeamMember(outOwnerId.get(), TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, objectDataExt.getOutTenantId()));
            }
        }
        // 移除互联部门类型的相关团队数据，不允许更新该类型
        teamMembers.removeIf(member -> TeamMember.MemberType.INTERCONNECT_DEPARTMENT.equals(member.getMemberType()));
        // 将原有的互联部门类型的相关团队数据添加回来
        List<TeamMember> interconnectDeptMembers = objectDataExt.getTeamMembers().stream()
                .filter(member -> TeamMember.MemberType.INTERCONNECT_DEPARTMENT.equals(member.getMemberType()))
                .collect(Collectors.toList());
        teamMembers.addAll(interconnectDeptMembers);
        
        if (arg.getMode() == 0) {
            // 只处理内部相关团队，补充外部人员
            teamMembers.removeIf(TeamMember::isOutMember);  // 防止用户混合传递导致重复添加外部人员
            List<TeamMember> outTeamMembers = objectDataExt.getTeamMembers().stream().filter(TeamMember::isOutMember).collect(Collectors.toList());
            teamMembers.addAll(outTeamMembers);
        } else if (arg.getMode() == 1) {
            // 只处理外部相关团队，补充内部人员
            teamMembers.removeIf(t -> !t.isOutMember());    // 防止用户混合传递导致重复添加内部人员
            List<TeamMember> innerTeamMembers = objectDataExt.getTeamMembers().stream().filter(t -> !t.isOutMember()).collect(Collectors.toList());
            teamMembers.addAll(innerTeamMembers);
        }

        // 下游人员只能编辑自己企业的相关团队人员，防止数据误操作
        if (actionContext.getUser().isOutUser()) {
            teamMembers.removeIf(it -> it.isOutMember()
                    && !Objects.equals(it.getOutTenantId(), actionContext.getUser().getOutTenantId()));
            List<TeamMember> members = objectDataExt.getTeamMembers().stream().filter(it -> it.isOutMember()
                    && !Objects.equals(it.getOutTenantId(), actionContext.getUser().getOutTenantId())).collect(Collectors.toList());
            teamMembers.addAll(members);
        }
        objectDataExt.setTeamMembers(teamMembers);
    }

    @Data
    private static class EditTeamMemberArg {
        private EditTeamMemberArg() {
        }

        //数据ID列表
        private String dataID;
        //数据团队成员信息
        private List<TeamMemberInfoPoJo> teamMemberInfos;
    }

    @Data
    public static class Arg {
        //添加无参构造函数，否则内部类转 json报 No suitable constructor found for type
        public Arg() {
        }

        //数据ID列表
        private String dataID;
        //数据团队成员信息
        private List<TeamMemberInfoPoJo> teamMemberInfos;
        // 编辑方式： 0: 只处理内部相关团队   1: 只处理外部相关团队
        private int mode = 0;
        //是否不发crm提醒
        @JsonProperty("ignoreSendingRemind")
        @JSONField(name = "ignoreSendingRemind")
        private Boolean ignoreSendingRemind;

        private Boolean checkOwnerChange;

        public Map<String, Object> toButtonArg() {
            Map<String, Object> result = Maps.newHashMap();
            result.put("teamMemberInfos", teamMemberInfos.stream().map(TeamMemberInfoPoJo::toMap).collect(Collectors.toList()));
            return result;
        }

        public boolean needCheckOwnerChangeInTeamMember() {
            return BooleanUtils.isTrue(checkOwnerChange);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    public static class Result {
    }
}
