package com.facishare.paas.appframework.core.model;


import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FAccountAuthorizationService {
    @Autowired
    private ServiceFacade serviceFacade;

    public IObjectData getFAccountAuthorizationData(User user, String objectApiName, String authorizedType) {
        List<IFilter> filterList = Lists.newArrayList();

        IFilter objectFilter = new Filter();
        objectFilter.setFieldName("authorized_object_api_name");
        objectFilter.setFieldValues(Lists.newArrayList(objectApiName));
        objectFilter.setOperator(Operator.EQ);
        filterList.add(objectFilter);

        IFilter authorizedTypeFilter = new Filter();
        authorizedTypeFilter.setFieldName("authorized_type");
        authorizedTypeFilter.setFieldValues(Lists.newArrayList(authorizedType));
        authorizedTypeFilter.setOperator(Operator.EQ);
        filterList.add(authorizedTypeFilter);

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName("life_status");
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));
        lifeStatusFilter.setOperator(Operator.EQ);
        filterList.add(lifeStatusFilter);

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        IFilter tenantIdFilter = new Filter();
        tenantIdFilter.setFieldName(IObjectData.TENANT_ID);
        tenantIdFilter.setFieldValues(Lists.newArrayList(user.getTenantId()));
        tenantIdFilter.setOperator(Operator.EQ);
        filterList.add(tenantIdFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1);

        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, "FAccountAuthorizationObj", query);
        if (CollectionUtils.empty(queryResult.getData())) {
            throw new ValidateException(I18N.text(FAccountAuthorizationService.Const.FACCOUNT_AUTH_NOT_EXIST, objectApiName));
        }
        return queryResult.getData().get(0);
    }

    public static class Const {
        /**
         * 找不到对象{0}对应的账户授权记录
         */
        final static public String FACCOUNT_AUTH_NOT_EXIST = "faccountauthorizationobj.action.validate.faccountauthorizationobj_data_not_exist";
        /**
         * 对象{0}对应的账户授权未初始化
         */
        final static public String FACCOUNT_AUTH_NOT_INIT = "faccountauthorizationobj.action.validate.faccountauthorizationobj_data_not_init";
        /**
         * 账户{0}不在账户授权的授权明细里面
         */
        final static public String FUND_ACCOUNT_NOT_IN_AUTH_DETAIL = "faccountauthorizationobj.action.validate.fundaccount_not_in_auth_detail";
    }
}