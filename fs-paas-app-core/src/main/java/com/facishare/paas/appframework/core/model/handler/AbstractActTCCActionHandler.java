package com.facishare.paas.appframework.core.model.handler;

import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * Created by zhouwr on 2023/7/10.
 */
public abstract class AbstractActTCCActionHandler<A extends Handler.Arg, R extends Handler.Result> implements TCCActionHandler<A, R> {

    @Autowired
    private RollbackHandlerManager rollbackHandlerManager;

    @Override
    public boolean commit(BranchTransactionalContext branchTransactionalContext, HandlerContext context, A arg) {
        return true;
    }

    @Override
    public boolean rollback(BranchTransactionalContext branchTransactionalContext, HandlerContext context, A arg) {
        RollbackHandler<A> rollbackHandler = rollbackHandlerManager.getRollbackHandler(context.getInterfaceCode(), arg.getObjectApiName());
        if (Objects.isNull(rollbackHandler)) {
            return true;
        }
        return RequestContextManager.runWithContext(context.getRequestContext(),
                () -> rollbackHandler.rollback(branchTransactionalContext, context, arg));
    }

}
