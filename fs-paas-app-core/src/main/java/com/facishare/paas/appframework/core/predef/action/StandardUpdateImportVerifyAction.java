package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.UniqueRuleExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.predef.action.BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE;

public class StandardUpdateImportVerifyAction extends BaseImportVerifyAction {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        List<String> funPrivilegeCodes = Lists.newArrayList();
        funPrivilegeCodes.addAll(StandardAction.UpdateImportData.getFunPrivilegeCodes());
        if (BooleanUtils.isTrue(arg.getUpdateOwner())) {
            funPrivilegeCodes.addAll(StandardAction.ChangeOwner.getFunPrivilegeCodes());
        }
        return funPrivilegeCodes;
    }

    @Override
    protected String verifyChangeOrderObject() {
        if (ObjectDescribeExt.of(objectDescribe).enabledChangeOrder()) {
            return I18NExt.text(I18NKey.OPEN_CHANGE_ORDER_NOT_SUPPORT_UPDATE_IMPORT);
        }
        return super.verifyChangeOrderObject();
    }

    @Override
    protected void recordLog() {
        serviceFacade.log(actionContext.getUser(), EventType.ADD, ActionType.UpdateImport, objectDescribe.getApiName(), objectDescribe.getDisplayName());
    }

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> validImportFields = infraServiceFacade.getUpdateImportTemplateField(actionContext.getUser(), objectDescribe);
        ObjectDataDocument objectDataDocument = arg.getRows().get(0);
        if (isSupportFieldMapping()) {
            IObjectData data = convertColIndexToApiName(objectDataDocument);
            validImportFields.removeIf(x -> !ObjectDataExt.of(data).containsField(x.getApiName()));
        } else {
            Set<Entry<String, Object>> entrySet = objectDataDocument.entrySet();
            List<String> validTitles = getValidTitles(entrySet, validImportFields);
            validImportFields.removeIf(x -> !validTitles.contains(x.getLabel()));
        }
        return validImportFields;
    }

    @Override
    protected List<IFieldDescribe> handelUniqueRuleFields(List<IFieldDescribe> sortedFieldList) {
        if (!Objects.equals(arg.getMatchingType(), BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE) || Objects.isNull(uniqueRule)) {
            return sortedFieldList;
        }
        Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(actionContext.getUser(), objectDescribe.getApiName());
        return UniqueRuleExt.of(uniqueRule).handelHeaderField(objectDescribe, sortedFieldList, unauthorizedFields);
    }

    @Override
    protected String validateMatchTypeField(String matchTypeField, Set<Entry<String, Object>> entrySet) {
        if (StringUtils.isBlank(matchTypeField)) {
            return I18NExt.getOrDefault(I18NKey.UPDATE_IMPORT_NOT_EXIST_FIELD, "请检查您当前指定的字段是否存在！");// ignoreI18n
        }
        Optional<IFieldDescribe> dbFieldDescribe;
        if (importReferenceFieldMappingSwitch) {
            dbFieldDescribe = ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribeSilently(matchTypeField);
        } else {
            dbFieldDescribe = ObjectDescribeExt.of(objectDescribe).getFieldDescribeSilently(matchTypeField);
        }
        if (!dbFieldDescribe.isPresent()) {
            return I18NExt.getOrDefault(I18NKey.UPDATE_IMPORT_NOT_EXIST_SPECIFIED_FIELD, "更新导入指定的字段[{0}]禁用或删除,请重新选择或者配置！", matchTypeField);// ignoreI18n
        }
        String label;
        if (arg.getMatchingType() == BaseImportDataAction.MATCHING_TYPE_ID) {
            label = I18NExt.getOrDefault(I18NKey.DATAID_LABEL, "唯一性ID") + I18NExt.getOrDefault(I18NKey.MUST_FILL_IN, "（必填）");// ignoreI18n
        } else {
            label = dbFieldDescribe.get().getLabel();
            if (dbFieldDescribe.get().isRequired()) {
                label = label + I18NExt.getOrDefault(I18NKey.MUST_FILL_IN, "（必填）");// ignoreI18n
            }
        }
        List<String> labels = entrySet.stream().map(Entry::getKey).collect(Collectors.toList());
        if (!labels.contains(label)) {
            return I18NExt.getOrDefault(I18NKey.UPDATE_IMPORT_MATCH_FIELD_NOT_IN_TEMPLATE, "导入标识字段[{0}]不在模板中，请检查模板并补充该字段。", label);// ignoreI18n
        }
        return null;
    }

    @Override
    protected String validateMatchTypeField(String matchTypeField, IObjectData data) {
        if (StringUtils.isBlank(matchTypeField)) {
            return I18NExt.getOrDefault(I18NKey.UPDATE_IMPORT_NOT_EXIST_FIELD, "请检查您当前指定的字段是否存在！");// ignoreI18n
        }
        Optional<IFieldDescribe> dbFieldDescribe;
        if (importReferenceFieldMappingSwitch) {
            dbFieldDescribe = ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribeSilently(matchTypeField);
        } else {
            dbFieldDescribe = ObjectDescribeExt.of(objectDescribe).getFieldDescribeSilently(matchTypeField);
        }
        if (!dbFieldDescribe.isPresent()) {
            return I18NExt.getOrDefault(I18NKey.UPDATE_IMPORT_NOT_EXIST_SPECIFIED_FIELD, "更新导入指定的字段[{0}]禁用或删除,请重新选择或者配置！", matchTypeField);// ignoreI18n
        }
        if (!data.containsField(matchTypeField)) {
            return I18NExt.getOrDefault(I18NKey.UPDATE_IMPORT_MATCH_FIELD_NOT_IN_TEMPLATE, "导入标识字段[{0}]不在模板中，请检查模板并补充该字段。", dbFieldDescribe.get().getLabel());// ignoreI18n
        }
        return null;
    }

    @Override
    protected String customVerify() {
        return null;
    }

    @Override
    protected String validateUniqueRule(List<IFieldDescribe> validFieldList, Set<Entry<String, Object>> entrySet) {
        if (MATCHING_TYPE_UNIQUE_RULE == arg.getMatchingType()) {
            return super.validateUniqueRule(validFieldList, entrySet);
        }
        return null;
    }

    @Override
    protected String verifyUniqueRule(List<IFieldDescribe> validFieldList, IObjectData data) {
        if (MATCHING_TYPE_UNIQUE_RULE == arg.getMatchingType()) {
            return super.verifyUniqueRule(validFieldList, data);
        }
        return null;
    }
}
