package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.ocr.api.model.OcrData;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.UIEventBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.uievent.*;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.ocr.OcrDataDto;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUIEvent;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.functions.exception.FunctionAPIException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 触发UI事件
 *
 * <AUTHOR>
 * @date 2019-08-01 16:42
 */
@Slf4j
//@Idempotent(serializer = Serializer.Type.java)
public class StandardTriggerEventAction extends PreDefineAction<StandardTriggerEventAction.Arg,
        StandardTriggerEventAction.Result> implements ActionContainer {
    /**
     * 从对象执行结果类型： 更新
     */
    private static final String UPDATE = "u";
    /**
     * 从对象执行结果类型： 添加
     */
    private static final String ADD = "a";
    /**
     * 提醒内容key
     */
    public static final String REMIND_CONTENT = "content";
    /**
     * 提醒内容支持多字段红字提醒
     */
    public static final String REMIND_CONTENT_MAP = "contentMap";
    /**
     * 提醒类型Key
     */
    public static final String REMIND_TYPE = "type";
    /**
     * 提醒类型
     */
    private static final int REMIND_TYPE_RED = 1;
    private static final int REMIND_TYPE_TOAST = 2;
    private static final int REMIND_TYPE_POP = 3;
    /**
     * 数据处理器
     */
    private UIEventProcess process;
    /**
     * 主对象数据
     */
    private IObjectData masterData;
    /**
     * 从对象数据
     */
    private Map<String, List<IObjectData>> detailData = Maps.newHashMap();
    /**
     * 从对象描述
     */
    private Map<String, IObjectDescribe> detailObjectDescribeMap = Maps.newHashMap();
    private MaskFieldLogicService maskFieldLogicService;
    private RLock seriesLock;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void finallyDo() {
        infraServiceFacade.unlock(seriesLock);
        super.finallyDo();
    }

    @Override
    protected void before(Arg arg) {
        if (StringUtils.isNotBlank(arg.getSeriesId())
                && UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.LOCK_BY_SERIES_ID_IN_CALCULATE_AND_UI_EVENT_AND_ADD_EDIT_REQUEST, actionContext.getTenantId(), actionContext.getObjectApiName())) {
            String lockKey = RequestUtil.buildSeriesKey(arg.getSeriesId(), actionContext.getTenantId());
            seriesLock = infraServiceFacade.tryLockWithErrorMsg(0, 30, TimeUnit.SECONDS, lockKey,
                    I18NExt.text(I18NKey.PREVIOUS_REQUEST_PROCESSING_ALERT), AppFrameworkErrorCode.PREVIOUS_REQUEST_PROCESSING_ALERT.getCode());
        }

        super.before(arg);
        IUIEvent event = infraServiceFacade.findEventById(arg.getEventId(), actionContext.getTenantId());
        if (Objects.isNull(event)) {
            log.warn("findEventById result id null, eventId:{}, ei:{}", arg.getEventId(), actionContext.getTenantId());
            throw new ValidateException(I18NExt.text(I18NKey.EVENT_NOT_EXIST));
        }
        stopWatch.lap("findEventById");
        // 初始化数据
        initData(event);
        stopWatch.lap("initData");
        // 初始化处理器
        initProcessor(event);
        stopWatch.lap("initProcessor");
    }

    /**
     * 初始化主从对象数据，将Map转为IObjectData
     */
    private void initData(IUIEvent event) {
        Map<String, Object> masterDataMap = arg.getMasterData();
        if (CollectionUtils.notEmpty(masterDataMap)) {
            masterData = ObjectDataExt.of(masterDataMap).getObjectData();
            masterData.setTenantId(actionContext.getTenantId());
            masterData.setDescribeApiName(objectDescribe.getApiName());
        }
        Map<String, Map<String, Map<String, Object>>> detailDataMap = arg.getDetailData();
        String editingMark = arg.getEditingMark();
        List<String> batchEditingMark = arg.getBatchEditingMark();
        List<String> newDetails = CollectionUtils.nullToEmpty(arg.getNewDetails());
        if (CollectionUtils.notEmpty(detailDataMap)) {
            detailDataMap.forEach((apiName, dataMap) -> {
                // 从对象数据
                List<IObjectData> dataList = Lists.newArrayList();
                dataMap.forEach((mark, data) -> {

                    if (event.getTriggerDescribeApiName() != null && event.getTriggerDescribeApiName().equals(apiName)) {
                        if (StringUtils.isNotBlank(editingMark) && editingMark.equals(mark)) {
                            data.put(ObjectDataExt.IS_CURRENT_EDITING, true);
                        }
                        if (CollectionUtils.notEmpty(batchEditingMark) && batchEditingMark.contains(mark)) {
                            data.put(ObjectDataExt.IS_BATCH_CURRENT_EDITING, true);
                        }
                        // 新增事件中，新增的detail
                        if (newDetails.contains(mark)) {
                            data.put(ObjectDataExt.IS_NEW_DATA, true);
                        }
                    }

                    data.put(ObjectDataExt.MARK_API_NAME, mark);
                    IObjectData detail = ObjectDataExt.of(data).getObjectData();
                    detail.setDescribeApiName(apiName);
                    detail.setTenantId(actionContext.getTenantId());
                    dataList.add(detail);
                });
                detailData.put(apiName, dataList);
            });
        } else {
            detailData = Maps.newHashMap();
        }
        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(actionContext.getTenantId(), objectDescribe.getApiName());
        detailObjectDescribeMap = detailDescribes.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));
        // 对参数中的掩码字段解码
        decodeMaskFieldEncryptValue(objectDescribe, Lists.newArrayList(masterData));
        detailData.forEach((objectApiName, detailDataList) -> decodeMaskFieldEncryptValue(detailObjectDescribeMap.get(objectApiName), detailDataList));
        mergeData(masterData, detailData);

    }

    protected void decodeMaskFieldEncryptValue(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        if (Objects.isNull(objectDescribe) || !AppFrameworkConfig.maskFieldEncryptGray(actionContext.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        if (Objects.isNull(maskFieldLogicService)) {
            maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
        }
        maskFieldLogicService.decodeMaskFieldEncryptValue(actionContext.getUser(), dataList, objectDescribe);
    }

    @Override
    protected Result doAct(Arg arg) {

        try {
            // 构造请求
            Result result = new Result();
            // copy 数据
            IObjectData masterDataCopy = ObjectDataExt.of(masterData).copy();
            Map<String, List<IObjectData>> detailDataCopy = Maps.newHashMap();
            detailData.forEach((apiName, data) -> {
                List<IObjectData> copyList = ObjectDataExt.copyList(data);
                detailDataCopy.put(apiName, copyList);
            });

            UIEventProcess.ProcessRequest request = UIEventProcess.ProcessRequest.builder()
                    .masterData(masterDataCopy)
                    .detailDataMap(detailDataCopy)
                    .deletedDetails(arg.getDeletedDetails())
                    .triggerFieldAPIName(arg.getTriggerFieldAPIName())
                    .bizInfo(arg.getBizInfo())
                    .ocrDataResult(getOcrDataResult(arg, masterDataCopy, result))
                    .triggerPage(getTriggerPage())
                    .build();
            // 逐一顺序调用处理器，修改request内容
            // merge处理时，当前masterData和detailData会被修改
            // 以用作之后的Diff
            process.invoke(request);
            stopWatch.lap("process.invoke");
            // 业务方定制化地处理特殊字段
            customHandleFieldsAfterProcess(request.getMasterWithOnlyChangedFields(), request.getDetailWithOnlyChangedFields());
            // 返回结果， 校验事件不反悔data字段
            result.setObjectAttribute(request.getObjectAttribute());
            result.setFieldAttribute(request.getFieldAttribute());
            result.setDetailFieldAttribute(request.getDetailFieldAttribute());
            result.setDetailRecordType(request.getDetailRecordType());
            result.setOptionAttribute(request.getOptionAttribute());
            result.setDetailButton(request.getDetailButton());
            result.setDetailRowFieldAttribute(request.getDetailRowFieldAttribute());
            result.setRemind(request.getRemind());
            if (!isRemindEvent(request)) {
                IObjectData objectData = request.getMasterWithOnlyChangedFields();
                Map<String, List<IObjectData>> detailWithOnlyChangedFields = request.getDetailWithOnlyChangedFields();
                fillMaskFieldValue(objectData, detailWithOnlyChangedFields);
                stopWatch.lap("fillMaskFieldValue");
                result.setMasterData(objectDescribe.getApiName(), objectData);
                result.batchDealDetailData(detailWithOnlyChangedFields);
            } else {
                result.setData(new HashMap<>());
            }
            return result;
        } catch (FunctionAPIException e) {
            // 函数执行错误
            log.warn("Executing function error, please check function,tenantId:{}, event:{}, describeApiName:{}, arg:{}",
                    actionContext.getTenantId(), arg.getEventId(), actionContext.getObjectApiName(), arg, e);
            throw new ValidateException(e.getMessage());
        } catch (UIEventBusinessException e) {
            log.error("UI event fail.", e);
            throw new ValidateException(e.getMessage());
        }

    }

    private List<OcrResult> getOcrDataResult(Arg arg, IObjectData masterDataCopy, Result result) {
        List<OcrResult> ocrResults = Lists.newArrayList();
        if (Objects.nonNull(arg.getOcrDataResult())) {
            log.info("ui event not trigger ocr,tenantId:{}", actionContext.getTenantId());
            List<OcrDataDto> ocrDataResult = arg.getOcrDataResult();
            for (OcrDataDto ocrDataDto : ocrDataResult) {
                OcrResult ocrResult = OcrResult.builder().fieldName(ocrDataDto.getFieldName())
                        .path(ocrDataDto.getPath())
                        .ocrData(ocrDataDto.getOcrData())
                        .build();
                ocrResults.add(ocrResult);
            }
        } else {
            log.info("ui event trigger ocr,tenantId:{}", actionContext.getTenantId());
            ocrResults = ocrImages(masterDataCopy, objectDescribe, arg.getTriggerFieldAPIName());
        }
        Map<String, List<OcrResult>> ocrErrorResultMap = ocrResults.stream()
                .filter(it -> !it.isSuccess())
                .collect(Collectors.groupingBy(OcrResult::getFieldName, Collectors.mapping(it -> it, Collectors.toList())));
        result.setOcrErrorResultMap(ocrErrorResultMap);
        stopWatch.lap("ocrImages");
        return ocrResults;
    }

    private void fillMaskFieldValue(IObjectData masterWithOnlyChangedFields,
                                    Map<String, List<IObjectData>> detailWithOnlyChangedFields) {
        fillMaskFieldValue(objectDescribe, Lists.newArrayList(masterWithOnlyChangedFields));
        detailWithOnlyChangedFields.forEach((objectApiName, detailDataList) -> {
            fillMaskFieldValue(detailObjectDescribeMap.get(objectApiName), detailDataList);
        });
    }

    private void fillMaskFieldValue(IObjectDescribe describe, List<IObjectData> dataList) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(actionContext.getTenantId(), actionContext.getObjectApiName()) || Objects.isNull(describe)) {
            return;
        }
        List<IFieldDescribe> maskFields = getMaskEncryptFields(describe);

        if (Objects.isNull(maskFieldLogicService)) {
            maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
        }
        maskFieldLogicService.fillMaskFieldValue(actionContext.getUser(), describe, dataList, maskFields, MaskFieldLogicService.MaskFieldConfig.defaultMaskFieldConfig());
    }

    private List<IFieldDescribe> getMaskEncryptFields(IObjectDescribe describe) {
        Map<String, List<String>> maskFieldApiNames = arg.getMaskFieldApiNames();
        if (CollectionUtils.empty(maskFieldApiNames)) {
            return Lists.newArrayList();
        }
        return ObjectDescribeExt.of(describe).getFieldByApiNames(maskFieldApiNames.get(describe.getApiName()));
    }

    private List<OcrResult> ocrImages(IObjectData objectData, IObjectDescribe objectDescribe, List<String> fieldApiNames) {
        Map<String, Map<String, OcrData>> ocrResult = infraServiceFacade.ocrImages(actionContext.getUser(), objectData, objectDescribe, fieldApiNames);
        if (CollectionUtils.empty(ocrResult)) {
            return Lists.newArrayList();
        }
        return ocrResult.entrySet().stream()
                .flatMap(entry -> entry.getValue().entrySet().stream().map(it -> {
                    OcrData ocrData = it.getValue();
                    return OcrResult.builder().fieldName(entry.getKey()).path(it.getKey()).ocrData(ocrData).build();
                }))
                .collect(Collectors.toList());
    }

    /*
     * 是否为校验事件
     */
    private boolean isRemindEvent(UIEventProcess.ProcessRequest request) {
        return request.isRemindEvent();
    }

    /**
     * 定制化的处理最终变化的字段
     * <p>
     * {@link StandardTriggerEventAction#masterData} 以及
     * {@link StandardTriggerEventAction#detailData}
     * 是前端传过来的原始数据，可作为处理特殊字段的底板。此外有
     * {@link StandardTriggerEventAction#detailObjectDescribeMap}
     * 为所有从对象的描述信息
     *
     * @param masterWithOnlyChangedFields 经过UI事件之后，主对象(只包含变化字段)数据
     * @param detailWithOnlyChangedFields 经过UI事件之后，从对象(只包含变化字段)数据
     */
    public void customHandleFieldsAfterProcess(IObjectData masterWithOnlyChangedFields,
                                               Map<String, List<IObjectData>> detailWithOnlyChangedFields) {
        // 默认什么都不做
    }

    /**
     * 业务方定制化处理diff之后的主对象字段
     * <p>
     * 执行完函数和计算完成之后会分别进行一次主对象字段的Diff，
     * 此方法在进行完成通用Diff之后执行。通用Diff根据前端传的
     * {@link StandardTriggerEventAction#masterData}
     * 和函数(或计算)之后的数据做Diff，结果由参数
     * masterWithOnlyChangedFields引用。如果业务方有特殊字段
     * 不想被修改，请从masterWithOnlyChangedFields中删除。
     *
     * @param masterWithOnlyChangedFields 主对象(只包含变化字段)数据
     */
    public void customHandleDiffedMaster(IObjectData masterWithOnlyChangedFields) {
        // 默认什么都不做
    }

    /**
     * 业务方定制化处理diff之后的从对象字段
     * <p>
     * 执行完函数之后会分别进行一次主对象字段的Diff(计算不更改从)，
     * 此方法在进行完成通用Diff之后执行。通用Diff根据前端传的
     * {@link StandardTriggerEventAction#detailData}
     * 和函数之后的从数据做Diff，结果由参数
     * detailWithOnlyChangedFields。如果业务方有特殊字段
     * 不想被修改，请从detailWithOnlyChangedFields中删除。
     *
     * @param detailWithOnlyChangedFields 从对象(只包含变化字段)数据
     */
    public void customHandleDiffedDetail(Map<String, List<IObjectData>> detailWithOnlyChangedFields) {
        // 默认什么都不做
    }

    @Override
    public ServiceFacade getServiceFacade() {
        return serviceFacade;
    }

    @Override
    public InfraServiceFacade getInfraServiceFacade() {
        return infraServiceFacade;
    }

    @Override
    public RequestContext getContext() {
        return actionContext.getRequestContext();
    }

    @Override
    public IObjectData getMasterData() {
        return masterData;
    }

    @Override
    public Map<String, List<IObjectData>> getDetailData() {
        return detailData;
    }

    @Override
    public IObjectDescribe getObjectDescribe() {
        return objectDescribe;
    }

    @Override
    public Map<String, IObjectDescribe> getDetailDescribe() {
        return detailObjectDescribeMap;
    }


    // ---------------------------------------- Private methods

    private void mergeData(IObjectData masterData, Map<String, List<IObjectData>> detailMap) {
        // 不存在ID说明当前是由新建触发的UI事件，不进行merge
        if (StringUtils.isNotBlank(masterData.getId())) {
            // merge主对象
            serviceFacade.mergeWithDbData(actionContext.getTenantId(), objectDescribe.getApiName(), Lists.newArrayList(masterData));
            // merge从对象
            if (CollectionUtils.notEmpty(detailMap)) {
                ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
                detailMap.forEach((apiName, dataList) ->
                        task.submit(() -> serviceFacade.mergeWithDbData(actionContext.getTenantId(), apiName, dataList)));
                try {
                    task.await(10000, TimeUnit.MILLISECONDS);
                } catch (TimeoutException e) {
                    log.error("Merge data time out, tenantId:{}, arg:{}", actionContext.getTenantId(), arg);
                    throw new UIEventBusinessException("Merge data timeout");
                }
            }

        }
    }

    /**
     * 初始化处理器
     */
    private void initProcessor(IUIEvent event) {
        process = new SimpleUIEventProcess();
        // 初始化 functionProcessor
        Processor functionProcessor = new FunctionProcessor(this);
        functionProcessor.setEvent(event);
        // 构造Diff处理器
        DiffProcessor diffAfterFunction = new DiffProcessor.Builder(this)
                .setDiffMaster(true)
                .setDiffDetail(true)
                .setShieldFieldsAfterFunction(true).build();
        //如果灰度了支持计算从对象，那么计算之后也需要diff从对象
        DiffProcessor diffAfterCompute = new DiffProcessor.Builder(this)
                .setDiffMaster(true)
                .setDiffDetail(false)
                .build();
        // 按照顺序添加处理器
        process.addOrderedProcessors(
                functionProcessor,                      // 函数处理
                diffAfterFunction,       // 主从都diff,为了计算准备
                new ComputeProcessor(this),    // 计算
                diffAfterCompute, // 只diff主(计算只改变主z)
                new SupplementProcessor(this));
    }


    private String getTriggerPage() {
        return Optional.ofNullable(arg.getTriggerInfo())
                .map(TriggerInfo::getTriggerPage)
                .orElse(null);
    }


    @Data
    public static class Arg {
        @JSONField(name = "event_id")
        @JsonProperty(value = "event_id")
        @SerializedName(value = "event_id")
        String eventId;

        @JSONField(name = "layout_api_name")
        @JsonProperty(value = "layout_api_name")
        @SerializedName(value = "layout_api_name")
        String layoutApiName;

        @JSONField(name = "object_data")
        @JsonProperty(value = "object_data")
        @SerializedName(value = "object_data")
        Map<String, Object> masterData;

        @JSONField(name = "trigger_field_api_name")
        @JsonProperty(value = "trigger_field_api_name")
        @SerializedName(value = "trigger_field_api_name")
        List<String> triggerFieldAPIName;

        @JSONField(name = "detail_object_data")
        @JsonProperty(value = "detail_object_data")
        @SerializedName(value = "detail_object_data")
        Map<String, Map<String, Map<String, Object>>> detailData;

        @JSONField(name = "editing_object_data")
        @JsonProperty(value = "editing_object_data")
        @SerializedName(value = "editing_object_data")
        String editingMark;

        @JSONField(name = "new_details")
        @JsonProperty(value = "new_details")
        @SerializedName(value = "new_details")
        List<String> newDetails;
        @JSONField(name = "deleted_details")
        @JsonProperty(value = "deleted_details")
        @SerializedName(value = "deleted_details")
        Map<String, List<Map<String, Object>>> deletedDetails;
        /**
         * ocr 识别结果
         */
        private List<OcrDataDto> ocrDataResult;
        /**
         * 需要掩码加密的字段
         */
        private Map<String, List<String>> maskFieldApiNames;

        //页面id，对于同一个新建/编辑页调用的请求，都用同一个id
        private String seriesId;
        // 业务信息
        private Map<String, Object> bizInfo;
        @JSONField(name = "batch_editing_object_data")
        @JsonProperty(value = "batch_editing_object_data")
        @SerializedName(value = "batch_editing_object_data")
        List<String> batchEditingMark;

        @JSONField(name = "trigger_info")
        @JsonProperty("trigger_info")
        @SerializedName("trigger_info")
        private TriggerInfo triggerInfo;
    }

    @Data
    public static class TriggerInfo {
        /**
         * Add 新建页面
         * Edit 编辑页面
         */
        @JSONField(name = "trigger_page")
        @JsonProperty("trigger_page")
        @SerializedName("trigger_page")
        private String triggerPage;
    }

    @Setter
    @Builder
    @ToString
    @EqualsAndHashCode
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OcrResult {
        @Getter
        private String fieldName;
        @Getter
        private String path;
        @Delegate
        @JsonIgnore
        private transient OcrData ocrData;
    }

    @Data
    public static class Result {
        public static final String UPDATE_OR_DELETE = "u";
        public static final String NEW_OBJECTS = "a";

        @JSONField(name = "data")
        @JsonProperty(value = "data")
        @SerializedName(value = "data")
        Map<String, Map<String, Object>> data;

        @JSONField(name = "remind")
        @JsonProperty(value = "remind")
        @SerializedName(value = "remind")
        Map<String, Object> remind;

        @JSONField(name = "objectAttribute")
        @JsonProperty(value = "objectAttribute")
        @SerializedName(value = "objectAttribute")
        Map<String, Map<String, Object>> objectAttribute;

        @JSONField(name = "fieldAttribute")
        @JsonProperty(value = "fieldAttribute")
        @SerializedName(value = "fieldAttribute")
        Map<String, Map<String, Boolean>> fieldAttribute;                     //字段隐藏/只读配置

        @JSONField(name = "detailFieldAttribute")
        @JsonProperty(value = "detailFieldAttribute")
        @SerializedName(value = "detailFieldAttribute")
        Map<String, Map<String, Object>> detailFieldAttribute;               //从对象字段隐藏只读

        @JSONField(name = "detailRecordType")
        @JsonProperty(value = "detailRecordType")
        @SerializedName(value = "detailRecordType")
        Map<String, Map<String, Object>> detailRecordType;               //从对象业务类型的隐藏

        @JSONField(name = "optionAttribute")
        @JsonProperty(value = "optionAttribute")
        @SerializedName(value = "optionAttribute")
        Map<String, Map<String, Object>> optionAttribute;               //从对象字段隐藏只读

        @JSONField(name = "detailButton")
        @JsonProperty(value = "detailButton")
        @SerializedName(value = "detailButton")
        Map<String, List<Map<String, Object>>> detailButton;                 //从对象按钮隐藏

        Map<String, Map<String, Object>> detailRowFieldAttribute;


        /**
         * 返回 OCR 识别失败的结果
         */
        private Map<String, List<OcrResult>> ocrErrorResultMap;

        private Result() {
        }

        /**
         * 处理主对象数据
         */
        void setMasterData(String apiName, IObjectData master) {
            if (Objects.isNull(data)) {
                data = Maps.newLinkedHashMap();
            }
            data.put(apiName, ObjectDataExt.of(master).toMap());
        }

        /**
         * 处理从对象数据
         */
        void dealDetailData(String apiName, IObjectData detailData) {
            if (Objects.isNull(data)) {
                data = Maps.newLinkedHashMap();
            }
            Objects.requireNonNull(apiName);
            Map<String, Object> detail = ObjectDataExt.of(detailData).toMap();
            if (CollectionUtils.empty(detail)) {
                return;
            }
            // 删除detailData中的"__current__" 字段
            detail.remove(ObjectDataExt.IS_CURRENT_EDITING);
            detail.remove(ObjectDataExt.IS_BATCH_CURRENT_EDITING);
            // 删除detailData中的"__new_data__" 字段
            detail.remove(ObjectDataExt.IS_NEW_DATA);
            Map<String, Object> detailMap = data.computeIfAbsent(apiName, k -> Maps.newHashMap());
            // 获取从数据的标识（前端传来的）
            String mark = Objects.isNull(detail.get(ObjectDataExt.MARK_API_NAME)) ? null :
                    (String) detail.get(ObjectDataExt.MARK_API_NAME);
            // update: mark非空,   add: mark为null;
            if (StringUtils.isBlank(mark)) {
                // 根据业务数据结构，从对象返回的数据结构为:   "a":[]，这里强转是安全的
                @SuppressWarnings("unchecked") List<Object> addedList =
                        (List<Object>) detailMap.computeIfAbsent(NEW_OBJECTS, k -> Lists.newArrayList());
                dealAddedMap(addedList, detail);
            } else {
                // 根据业务数据结构，从对象返回的数据结构为:   "u":{}
                @SuppressWarnings("unchecked") Map<String, Object> updateMap =
                        (Map<String, Object>) detailMap.computeIfAbsent(UPDATE_OR_DELETE, k -> Maps.newLinkedHashMap());
                // 删除detail中"__mark__"
                detail.remove(ObjectDataExt.MARK_API_NAME);
                dealUpdatedMap(updateMap, detail, mark);
            }

        }

        /**
         * 批量处理从对象数据
         */
        void batchDealDetailData(String apiName, List<IObjectData> details) {
            data.put(apiName, detailEmptyMap());
            for (IObjectData detail : details) {
                dealDetailData(apiName, detail);
            }
        }

        /**
         * 保证返回数据结构中，从对象的"u":{}, "a":[]存在
         */
        private Map<String, Object> detailEmptyMap() {
            Map<String, Object> detailMap = Maps.newHashMap();
            detailMap.put(UPDATE_OR_DELETE, Maps.newHashMap());
            detailMap.put(NEW_OBJECTS, Lists.newArrayList());
            return detailMap;
        }

        /**
         * 批量处理从对象数据
         */
        void batchDealDetailData(Map<String, List<IObjectData>> detailsMap) {
            for (Map.Entry<String, List<IObjectData>> entry : detailsMap.entrySet()) {
                String apiName = entry.getKey();
                List<IObjectData> details = entry.getValue();
                batchDealDetailData(apiName, details);
            }
        }

        void dealRemind(String content, Integer type) {
            remind.put(REMIND_CONTENT, content);
            remind.put(REMIND_TYPE, type);
        }

        private void dealUpdatedMap(Map<String, Object> updateMap, Map<String, Object> detail, String mark) {
            updateMap.put(mark, detail);
        }

        private void dealAddedMap(List<Object> addedList, Map<String, Object> detail) {
            addedList.add(detail);
        }


    }
}
