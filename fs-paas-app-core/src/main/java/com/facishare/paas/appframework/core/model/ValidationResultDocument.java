package com.facishare.paas.appframework.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhouwr on 2024/1/5.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ValidationResultDocument implements Serializable {
    //是否阻断（否的话可以继续提交）
    private boolean block;
    //提示信息
    private String message;
    private MultiMessage multiMessage;
    //可跳过的校验器列表（从第一次提交到本次提交的期间产生非阻断提示信息的所有校验器，按照提交先后顺序正序排列）
    private List<ValidatorInfo> skippedValidatorList;
}
