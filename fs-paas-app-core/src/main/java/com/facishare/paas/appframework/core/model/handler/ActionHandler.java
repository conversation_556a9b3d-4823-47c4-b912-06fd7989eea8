package com.facishare.paas.appframework.core.model.handler;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2023/2/15.
 */
public interface ActionHandler<A extends Handler.Arg, R extends Handler.Result> extends Handler<A, R> {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(exclude = {"dataListForDataPrivilegeCheck"}, callSuper = true)
    class Arg<T> extends Handler.Arg<T> {
        //是否是异步审批回调执行的
        private boolean executeByFlowStartCallback;
        //是否是审批流通过以后执行的
        private boolean executeByFlowCompleted;
        //自定义的审批流回调参数
        @JsonIgnore
        private Map<String, Object> extraCallbackData;
        //审批流触发类型
        private String approvalFlowTriggerType;
        //是否成功触发审批流
        private boolean triggerApprovalFlowSuccess;
        //是否异步触发审批流
        private boolean triggerApprovalFlowAsync;
        //供数据权限校验用的数据列表
        @JsonIgnore
        private List<ObjectDataDocument> dataListForDataPrivilegeCheck;

        public List<IObjectData> dataListForDataPrivilegeCheck() {
            return ObjectDataDocument.ofDataList(dataListForDataPrivilegeCheck);
        }

        public boolean triggerUpdateApprovalFlowSuccess() {
            return triggerApprovalFlowSuccess && ApprovalFlowTriggerType.UPDATE.getId().equals(approvalFlowTriggerType);
        }

        public boolean triggerCreateApprovalFlowSuccess() {
            return triggerApprovalFlowSuccess && ApprovalFlowTriggerType.CREATE.getId().equals(approvalFlowTriggerType);
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Result<T> extends Handler.Result<T> {
        //自定义的审批流回调参数
        private Map<String, Object> extraCallbackData;
        //是否跳过数据权限校验
        private Boolean skipDataPrivilegeCheck;
        //是否跳过审批流
        private Boolean skipApprovalFlow;
        //是否跳过修改记录
        private Boolean skipRecordLog;
    }
}
