package com.facishare.paas.appframework.core.predef.action;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.service.dto.PrintTemplate;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.predef.action.StandardExportByPrintTemplateAction.Arg;
import com.facishare.paas.appframework.core.predef.action.StandardExportByPrintTemplateAction.Result;
import com.facishare.paas.appframework.core.util.ObjectUtils;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.util.XmlUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.token.model.TokenInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.concurrent.atomic.AtomicInteger;

public class StandardExportByPrintTemplateAction extends AbstractExportAction<Arg, Result> {


    public static final int DATA_BATCH_SIZE = 100;

    private static final int REPORT_INTERVAL = 10;

    private String path = "";

    /**
     * 返回完成状态必须path不能为空。
     * 假地址，用于返回完成状态
     */
    public static final String FAKER_PATH = "FAKER_PATH";

    private AtomicInteger counter;

    /**
     * 失败的 idList
     */
    List<String> failedIds = Lists.newArrayList();


    public boolean isComplete() {
        return counter.get() == totalCount && StringUtils.isNotBlank(path);
    }

    @Override
    protected void before(Arg arg) {
        //初始0
        counter = new AtomicInteger(0);
        super.before(arg);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected String getToken(Arg arg) {
        return arg.getToken();
    }

    @Override
    protected void customInit() {
    }

    @Override
    protected boolean hasToken() {
        return StringUtils.isNotBlank(arg.getToken());
    }

    @Override
    protected String getSearchTemplateId() {
        return null;
    }

    @Override
    protected String getSearchQueryInfo() {
        ObjectUtils.requireNotEmpty(arg.getSearchQueryInfo(), I18NExt.text(I18NKey.PARAM_ERROR));
        return arg.getSearchQueryInfo();
    }

    @Override
    protected List<String> getDataIdList() {
        return arg.getDataIdList();
    }

    @Override
    protected String getSearchTemplateType() {
        return null;
    }

    @Override
    protected boolean isIgnoreSceneFilter() {
        return false;
    }

    @Override
    protected boolean isIgnoreSceneRecordType() {
        return false;
    }

    @Override
    protected boolean isNoExportRelevantTeam() {
        return false;
    }

    private void auditProgress() {
        if (!isComplete() && counter.incrementAndGet() % REPORT_INTERVAL == 0) {
            TokenInfo tokenInfo = TokenInfo.builder().id(token).progress(counter.get()).build();
            tokenService.update(tokenInfo, tokenExpireSeconds);
        }
    }

    private void export(StopWatch stopWatch, QueryResult<IObjectData> queryResult, Map<String, String> pathAndName) {
        stopWatch.lap("export");
        for (IObjectData data : queryResult.getData()) {
            auditProgress();
            String dataId = data.getId();
            PrintTemplate.Arg body = PrintTemplate.Arg.builder()
                    .objectAPIName(objectDescribe.getApiName())
                    .objectId(dataId)
                    .templateId(arg.getPrintTemplateId())
                    .build();
            try {
                PrintTemplate.Result result = infraServiceFacade.print(actionContext.getUser(), body);
                if (Objects.nonNull(result) && StringUtils.isNotEmpty(result.getPath())) {
                    pathAndName.put(result.getPath(), String.join(".", result.getName(), result.getFileType()));
                } else {
                    failedIds.add(dataId);
                }
            } catch (Exception e) {
                log.warn("print data failed! tenantId:{}, dataId:{}.", actionContext.getTenantId(), dataId, e);
                failedIds.add(dataId);
            }
        }
    }

    @Override
    protected void doExport() {
        StopWatch stopWatch = StopWatch.create("doExport");
        Map<String,String> pathAndName = new HashMap<>();
        int totalPage = SearchTemplateQueryExt.calculateTotalPage(getExportRowsThrottle(), getDataBatchSize());
        serviceFacade.queryDataAndHandle(actionContext.getUser(), searchQuery, objectDescribe, getDataBatchSize(),
                totalPage, isNoExportRelevantTeam(), queryResult -> export(stopWatch, queryResult, pathAndName));

        if (CollectionUtils.notEmpty(failedIds)) {
            log.warn("print data failed count! tenantId:{}, dataIds:{},", actionContext.getTenantId(), failedIds);
        }

        String xml = XmlUtil.createExportFile(pathAndName, getFileName());

        //将文件描述发给文件服务，由文件服务 提交 complete 给 async-job-center
        infraServiceFacade.packedFile(actionContext.getUser(), xml, arg.getJobId(), totalCount-failedIds.size());

        path = FAKER_PATH;
        Long fileSize = 0L;
        Long expiredTime = 0L;
        String fileType = "zip";
        String successResult = buildTokenResult(path, fileSize, expiredTime, fileType);

        TokenInfo completeToken = TokenInfo.buildSuccess(token, successResult);
        tokenService.complete(completeToken, tokenExpireSeconds);
    }

    @Override
    protected void consumerDataList(List<IObjectData> dataList) {
    }

    @Override
    protected int getDataBatchSize() {
        return DATA_BATCH_SIZE;
    }

    protected String buildTokenResult(Object... items) {
        if (items.length == 0) {
            return "";
        }
        StringJoiner joiner = new StringJoiner("|");
        for (Object item : items) {
            joiner.add(String.valueOf(item));
        }
        return joiner.toString();
    }

    @Override
    protected Result generateResult(String token) {
        TokenInfo tokenInfo = tokenService.query(token);
        if (tokenInfo == null) {
            throw new MetaDataException("token not exist!");
        }
        int currentCount = Optional.ofNullable(tokenInfo.getProgress()).orElse(0);
        if (tokenInfo.isOngoing()) {
            return Result.builder()
                    .token(token)
                    .totalCount(totalCount)
                    .currentCount(currentCount)
                    .build();
        }

        if (tokenInfo.isSuccess()) {
            // nPath|fileSize|expiredTime|fileType
            String successResult = tokenInfo.getResult();
            String[] items = successResult.split("\\|");

            return Result.builder()
                    .path(items[0])
                    .size(Long.parseLong(items[1]))
                    .fileExpiredTime(Long.parseLong(items[2]))
                    .ext(items[3])
                    .fileName(getFileName())
                    .token(token)
                    .totalCount(totalCount)
                    .currentCount(currentCount)
                    .build();
        }

        if (tokenInfo.isError()) {
            String errorMsg = tokenInfo.getMessage();
            if (StringUtils.isNotBlank(errorMsg) && errorMsg.contains(ERROR_SPLIT)) {
                String message = StringUtils.substringAfter(errorMsg, ERROR_SPLIT);
                throw new MetaDataBusinessException(message);
            }
            throw new MetaDataException("bulk print data failed! " + errorMsg);
        }
        throw new MetaDataException("bulk print data failed!");
    }

    @Override
    protected String getFileName() {
        return String.join("_", objectDescribe.getDisplayName(), DateUtil.today());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getDataIdList();
    }

    @Data
    public static class Arg {
        @JsonProperty("job_id")
        @SerializedName("job_id")
        private String jobId;
        @JsonProperty("dataIdList")
        @SerializedName("dataIdList")
        private List<String> dataIdList;
        @JSONField(name = "search_query_info")
        @JsonProperty(value = "search_query_info")
        @SerializedName(value = "search_query_info")
        String searchQueryInfo;
        @JsonProperty("object_describe_api_name")
        @SerializedName("object_describe_api_name")
        private String objectDescribeApiName;
        @JsonProperty("print_template_id")
        @SerializedName("print_template_id")
        private String printTemplateId;
        @JsonProperty("bulk_export_type")
        @SerializedName("bulk_export_type")
        private String bulkExportType;
        @JsonProperty("result_processor")
        @SerializedName("result_processor")
        private String resultProcessor;

        /**
         * 任务 token 用于异步导出
         */
        @JsonProperty("token")
        @SerializedName("token")
        private String token;
    }

    @Data
    @Builder
    public static class Result {
        @JSONField(name = "M1")
        String ext;

        @JSONField(name = "file_name")
        @JsonProperty("file_name")
        @SerializedName("file_name")
        String fileName;

        /**
         * 返回给 dataloader 的结果中 path 不为空认为导出成功
         */
        @JSONField(name = "path")
        @JsonProperty("path")
        @SerializedName("path")
        String path;

        @JSONField(name = "M4")
        String token;

        long size;

        @JSONField(name = "total_count")
        @JsonProperty("total_count")
        @SerializedName("total_count")
        int totalCount;

        @JSONField(name = "current_count")
        @JsonProperty("current_count")
        @SerializedName("current_count")
        int currentCount;


        Long fileExpiredTime;
    }
}
