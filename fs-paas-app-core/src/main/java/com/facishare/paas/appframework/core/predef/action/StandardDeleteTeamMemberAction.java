package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.Pair;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.TeamMemberInfoPoJo;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.predef.action.StandardDeleteTeamMemberAction.Arg;
import static com.facishare.paas.appframework.core.predef.action.StandardDeleteTeamMemberAction.Result;
import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.DEPT_TEAM_MEMBER_REMIND_GRAY;

@Slf4j
//@Idempotent(serializer = Serializer.Type.java)
public class StandardDeleteTeamMemberAction extends BaseTeamMemberAction<Arg, Result> {

    private List<IObjectData> orgDataList = new ArrayList<>();
    private List<String> failReasons = Lists.newArrayList();

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.DeleteTeamMember.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getDataIDs();
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.DELETE_TEAM_MEMBER.getButtonApiName();
    }

    @Override
    protected boolean isBatchAction() {
        return arg.getDataIDs().size() > 1;
    }


    @Override
    protected Result doAct(Arg arg) {
        List<IObjectData> objectDataListToUpdate = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),
                arg.dataIDs, actionContext.getObjectApiName());
        if (CollectionUtils.empty(objectDataListToUpdate)) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.DATA_NOT_FIND_OR_DELETE, "数据不存在或已删除"));// ignoreI18n
        }

        objectDataListToUpdate.forEach(f -> orgDataList.add(ObjectDataExt.of(f).copy()));

        for (IObjectData objectData : objectDataListToUpdate) {
            try {
                //根据objectData的生命状态和锁定状态来判断数据是否有权限进行该操作
                serviceFacade.checkActionByLockStatusAndLifeStatus(objectData, ObjectAction.DELETE_TEAM_MEMBER,
                        actionContext.getUser(), actionContext.getObjectApiName(), false);
            } catch (Exception e) {
                failReasons.add(I18NExt.getOrDefault(I18NKey.OPERATION_FAILED, "操作{0}失败,原因:{1}",// ignoreI18n
                        objectData.getName(), e.getMessage()));
                continue;
            }

            if (CollectionUtils.empty(arg.getTeamMemberInfos())) {
                updateObjectDataByDeleteMember(arg.getTeamMemberEmployee(), objectData);
                removeOuterTeamMemberEmployee(arg.getOutTeamMemberEmployee(), objectData);
            } else {
                updateObjectDataWithDeleteMember(arg.getTeamMemberInfos(), objectData);
                removeOuterTeamMember(arg.getTeamMemberInfos(), objectData);
            }
        }
        // 更新
        bulkOpResult = serviceFacade.batchUpdateRelevantTeam(actionContext.getUser(), objectDataListToUpdate, false);

        return Result.builder().build();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        serviceFacade.logByActionType(actionContext.getUser(), EventType.DELETE, ActionType.RemoveEmployee, orgDataList, bulkOpResult.getSuccessObjectDataList(), objectDescribe);
        serviceFacade.sendActionMq(actionContext.getUser(), bulkOpResult.getSuccessObjectDataList(), ObjectAction.DELETE_TEAM_MEMBER);
        // 发送CRM提醒.批量删除时，删除成功的数据还是需要发送CRM提醒的
        sendReminder(arg.getDataIDs(), orgDataList, bulkOpResult.getSuccessObjectDataList());

        if (CollectionUtils.notEmpty(bulkOpResult.getFailObjectDataList())) {
            failReasons.add(bulkOpResult.getFailReason());
        }
        if (CollectionUtils.notEmpty(failReasons)) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.FAILED_DATA, "以下部分数据执行失败:{0}",// ignoreI18n
                    StringUtils.join(failReasons, ',')));
        }
        return super.after(arg, result);
    }

    @Override
    protected Map<String, Object> getActionParams(Arg arg) {
        return arg.toButtonArg();
    }

    @Deprecated
    @Override
    protected void diffTeamMemberInfoEmployee(List<String> dataIds,
                                              Table<String, String, TeamMemberInfoPoJo> teamMemberInfoTable,
                                              Table<String, String, TeamMemberInfoPoJo> dbTeamMemberInfoTable) {
        Map<String, Set<Integer>> employeeResultMap = Maps.newHashMap();
        Map<String, Set<String>> roleResultMap = Maps.newHashMap();
        dataIds.forEach(dataId -> {
            Map<String, TeamMemberInfoPoJo> teamMemberInfoMap = Optional.ofNullable(teamMemberInfoTable.row(dataId))
                    .orElseGet(Collections::emptyMap);
            Map<String, TeamMemberInfoPoJo> dbTeamMemberInfoMap = Optional.ofNullable(dbTeamMemberInfoTable.row(dataId))
                    .orElseGet(Collections::emptyMap);
            dbTeamMemberInfoMap.keySet().removeIf(teamMemberInfoMap::containsKey);
            employeeResultMap.putAll(getEmployeeMap(dataId, dbTeamMemberInfoMap));
            roleResultMap.putAll(getTeamMemberRoleMap(dataId, dbTeamMemberInfoMap));
        });
        if (BooleanUtils.isNotTrue(arg.getIgnoreSendingRemind())) {
            sendNotification(employeeResultMap, null, I18NKey.CANCELLED_ROLE_IDENTITY, roleResultMap, teamRoleInfos);
        }
    }

    @Override
    protected void diffTeamMemberInfo(List<String> dataIds,
                                      List<DataTeamMember> teamMemberInfoList,
                                      List<DataTeamMember> dbTeamMemberInfoList) {
        //根据成员类型分组，不同类型有不同的发通知方法
        Map<String, List<DataTeamMember>> teamMemberMap = getMemberMapByType(teamMemberInfoList);
        Map<String, List<DataTeamMember>> dbTeamMemberMap = getMemberMapByType(dbTeamMemberInfoList);

        // 发送人员的通知
        sendEmployeeRemind(dataIds, teamMemberMap, dbTeamMemberMap);

        if (UdobjGrayConfig.isAllow(DEPT_TEAM_MEMBER_REMIND_GRAY, actionContext.getTenantId())) {
            // 发送部门的通知
            sendDeptRemind(dataIds, teamMemberMap, dbTeamMemberMap);
        }
    }

    private void sendDeptRemind(List<String> dataIds, Map<String, List<DataTeamMember>> teamMemberMap, Map<String, List<DataTeamMember>> dbTeamMemberMap) {
        List<DataTeamMember> deptTeamList = getMemberListByType(teamMemberMap, TeamMember.MemberType.DEPARTMENT);
        List<DataTeamMember> deptDBTeamList = getMemberListByType(dbTeamMemberMap, TeamMember.MemberType.DEPARTMENT);

        Map<String, List<Pair<Integer, Boolean>>> deptResultMap = Maps.newHashMap();
        Map<String, Set<String>> roleResultMapDept = Maps.newHashMap();
        dataIds.forEach(dataId -> {
            Map<String, TeamMemberInfoPoJo> teamMemberInfoMap = Optional.ofNullable(DataTeamMember.parseValueMapByData(deptTeamList, dataId))
                    .orElseGet(Collections::emptyMap);
            Map<String, TeamMemberInfoPoJo> dbTeamMemberInfoMap = Optional.ofNullable(DataTeamMember.parseValueMapByData(deptDBTeamList, dataId))
                    .orElseGet(Collections::emptyMap);
            dbTeamMemberInfoMap.keySet().removeIf(teamMemberInfoMap::containsKey);
            deptResultMap.putAll(getDeptMap(dataId, dbTeamMemberInfoMap));
            roleResultMapDept.putAll(getTeamMemberRoleMap(dataId, dbTeamMemberInfoMap));
        });

        if (BooleanUtils.isNotTrue(arg.getIgnoreSendingRemind())) {
            sendNotification(null, deptResultMap, I18NKey.CANCELLED_ROLE_IDENTITY, roleResultMapDept, teamRoleInfos);
        }
    }

    private void sendEmployeeRemind(List<String> dataIds, Map<String, List<DataTeamMember>> teamMemberMap, Map<String, List<DataTeamMember>> dbTeamMemberMap) {
        List<DataTeamMember> employeeTeamList = getMemberListByType(teamMemberMap, TeamMember.MemberType.EMPLOYEE);
        List<DataTeamMember> employeeDBTeamList = getMemberListByType(dbTeamMemberMap, TeamMember.MemberType.EMPLOYEE);

        Map<String, Set<Integer>> employeeResultMap = Maps.newHashMap();
        Map<String, Set<String>> roleResultMap = Maps.newHashMap();
        dataIds.forEach(dataId -> {
            Map<String, TeamMemberInfoPoJo> teamMemberInfoMap = Optional.ofNullable(DataTeamMember.parseValueMapByData(employeeTeamList, dataId))
                    .orElseGet(Collections::emptyMap);
            Map<String, TeamMemberInfoPoJo> dbTeamMemberInfoMap = Optional.ofNullable(DataTeamMember.parseValueMapByData(employeeDBTeamList, dataId))
                    .orElseGet(Collections::emptyMap);
            dbTeamMemberInfoMap.keySet().removeIf(teamMemberInfoMap::containsKey);
            employeeResultMap.putAll(getEmployeeMap(dataId, dbTeamMemberInfoMap));
            roleResultMap.putAll(getTeamMemberRoleMap(dataId, dbTeamMemberInfoMap));
        });

        if (BooleanUtils.isNotTrue(arg.getIgnoreSendingRemind())) {
            sendNotification(employeeResultMap, null, I18NKey.CANCELLED_ROLE_IDENTITY, roleResultMap, teamRoleInfos);
        }
    }

    /**
     * 760新版相关团队，包括上游详情页操作相关团队和上游批量移除相关团队按钮操作
     *
     * @param teamMemberInfos 相关团队信息
     * @param objectData      数据
     */
    private void updateObjectDataWithDeleteMember(List<TeamMemberInfoPoJo> teamMemberInfos, IObjectData objectData) {
        // 批量移除相关团队按钮操作，目前只支持上下游各自独立，只能选到上下游各自的相关团队
        if (actionContext.getUser().isOutUser() || CollectionUtils.empty(teamMemberInfos)) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        //数据库查出来的相关团队信息
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        //遍历接口传过来的数据
        for (TeamMemberInfoPoJo teamMemberInfoPoJo : teamMemberInfos) {
            for (String emp : teamMemberInfoPoJo.getTeamMemberEmployee()) {
                if (TeamMember.MemberType.EMPLOYEE.getValue().equals(TeamMember.MemberType.of(teamMemberInfoPoJo.getTeamMemberType()).getValue())) {
                    //负责人不允许删除
                    //从原相关团队中获取原负责人
                    Optional<TeamMember> oldOwnerTeamMemberOpt = teamMembers.stream()
                            .filter(f -> !f.isOutMember()
                                    && f.getRole() == TeamMember.Role.OWNER
                                    && f.getMemberType() == TeamMember.MemberType.EMPLOYEE)
                            .findFirst();
                    Optional<String> ownerId = objectDataExt.getOwnerId();
                    if (ownerId.isPresent()
                            && oldOwnerTeamMemberOpt.isPresent()
                            && ownerId.get().equals(oldOwnerTeamMemberOpt.get().getEmployee())
                            && Objects.equals(emp, ownerId.get())
                            && TeamMember.MemberType.EMPLOYEE.equals(oldOwnerTeamMemberOpt.get().getMemberType())) {
                        failReasons.add(I18NExt.getOrDefault(I18NKey.OWNER_CAN_NOT_DELETE, "操作\\{0}\\失败原因负责人不能删除", objectData.getName()));// ignoreI18n
                        continue;
                    }
                }
                //类型（人员、部门、组织、角色）->需要删除的人员、部门、组织等信息
                List<TeamMember> deleteMember = teamMembers.stream()
                        .filter(it -> !it.isOutMember())
                        .filter(f -> f.getEmployee().equals(emp)
                                && Objects.equals(f.getMemberType().getValue(), teamMemberInfoPoJo.getTeamMemberType()))
                        .collect(Collectors.toList());
                teamMembers.removeAll(deleteMember);
            }
        }
        objectDataExt.setTeamMembers(teamMembers);
    }

    /**
     * 760下游批量移除相关团队按钮操作，目前只支持上下游各自独立，只能选到上下游各自的相关团队
     *
     * @param teamMemberInfos 相关团队信息
     * @param objectData      数据
     */
    private void removeOuterTeamMember(List<TeamMemberInfoPoJo> teamMemberInfos, IObjectData objectData) {
        RequestContext context = RequestContextManager.getContext();
        if (ObjectUtils.isNotEmpty(context) && context.isFromFunction()) {
            removeOuterTeamMemberByFunction(teamMemberInfos, objectData);
            return;
        }
        if (CollectionUtils.empty(teamMemberInfos)) {
            return;
        }
        // 移除外部相关团队，暂时只对下游生效
        if (!actionContext.getUser().isOutUser()) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        for (TeamMemberInfoPoJo teamMemberInfoPoJo : teamMemberInfos) {
            if (CollectionUtils.empty(teamMemberInfoPoJo.getOutTeamMemberEmployee())) {
                continue;
            }
            List<String> teamMemberIds = teamMemberInfoPoJo.getOutTeamMemberEmployee().stream()
                    .filter(it -> Objects.equals(actionContext.getUser().getOutTenantId(), it.getOutTenantId()))
                    .map(TeamMemberInfoPoJo.TeamMemberDTO::getUserId)
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(teamMemberIds)) {
                continue;
            }
            List<TeamMember> deleteMember = teamMembers.stream()
                    // 只处理外部相关团队
                    .filter(TeamMember::isOutMember)
                    // 只处理同一个下游的数据
                    .filter(it -> Objects.equals(actionContext.getUser().getOutTenantId(), it.getOutTenantId()))
                    // 负责人不允许被移除
                    .filter(it -> TeamMember.Role.OWNER != it.getRole() && TeamMember.MemberType.EMPLOYEE == it.getMemberType())
                    // 互联部门不允许移除
                    .filter(it -> TeamMember.MemberType.INTERCONNECT_DEPARTMENT != it.getMemberType())
                    .filter(it -> teamMemberIds.contains(it.getEmployee()))
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(deleteMember)) {
                continue;
            }
            teamMembers.removeAll(deleteMember);
        }
        objectDataExt.setTeamMembers(teamMembers);
    }

    /**
     * 760通过函数删除外部相关团队
     *
     * @param teamMemberInfos 相关团队信息
     * @param objectData      数据
     */
    private void removeOuterTeamMemberByFunction(List<TeamMemberInfoPoJo> teamMemberInfos, IObjectData objectData) {
        if (CollectionUtils.empty(teamMemberInfos)) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        for (TeamMemberInfoPoJo teamMemberInfoPoJo : teamMemberInfos) {
            if (CollectionUtils.empty(teamMemberInfoPoJo.getOutTeamMemberEmployee())) {
                continue;
            }
            List<String> teamMemberIds = teamMemberInfoPoJo.getOutTeamMemberEmployee().stream()
                    .filter(it -> Objects.equals(actionContext.getUser().getOutTenantId(), it.getOutTenantId()))
                    .map(TeamMemberInfoPoJo.TeamMemberDTO::getUserId)
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(teamMemberIds)) {
                continue;
            }
            List<TeamMember> deleteMember = teamMembers.stream()
                    // 只处理外部相关团队
                    .filter(TeamMember::isOutMember)
                    // 负责人不允许被移除
                    .filter(it -> TeamMember.Role.OWNER != it.getRole() && TeamMember.MemberType.EMPLOYEE == it.getMemberType())
                    .filter(it -> teamMemberIds.contains(it.getEmployee()))
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(deleteMember)) {
                continue;
            }
            teamMembers.removeAll(deleteMember);
        }
        objectDataExt.setTeamMembers(teamMembers);
    }


    /**
     * 删除外部相关团队
     *
     * @param outTeamMemberEmployee
     * @param objectData
     */
    private void removeOuterTeamMemberEmployee(List<TeamMemberEmployeeInfo> outTeamMemberEmployee, IObjectData objectData) {
        RequestContext context = RequestContextManager.getContext();
        if (ObjectUtils.isNotEmpty(context) && context.isFromFunction()) {
            removeOuterTeamMemberEmployeeByFunction(outTeamMemberEmployee, objectData);
            return;
        }

        // 批量移除相关团队按钮操作，目前只支持上下游各自独立，只能选到上下游各自的相关团队
        if (!actionContext.getUser().isOutUser()) {
            return;
        }
        if (CollectionUtils.empty(outTeamMemberEmployee)) {
            return;
        }

        List<String> employeeIDs = outTeamMemberEmployee.stream()
                .filter(it -> Objects.equals(actionContext.getUser().getOutTenantId(), it.getOutTenantId()))
                .map(TeamMemberEmployeeInfo::getUserId)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(employeeIDs)) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        List<TeamMember> deleteMember = teamMembers.stream()
                // 只处理外部相关团队
                .filter(TeamMember::isOutMember)
                // 只处理同一个下游的数据
                .filter(it -> Objects.equals(actionContext.getUser().getOutTenantId(), it.getOutTenantId()))
                // 负责人不允许被移除
                .filter(it -> TeamMember.Role.OWNER != it.getRole())
                // 互联部门不允许移除
                .filter(it -> TeamMember.MemberType.INTERCONNECT_DEPARTMENT != it.getMemberType())
                .filter(it -> employeeIDs.contains(it.getEmployee()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(deleteMember)) {
            return;
        }
        teamMembers.removeAll(deleteMember);
        objectDataExt.setTeamMembers(teamMembers);
    }

    private void removeOuterTeamMemberEmployeeByFunction(List<TeamMemberEmployeeInfo> outTeamMemberEmployee, IObjectData objectData) {
        if (ObjectUtils.isEmpty(outTeamMemberEmployee)) {
            return;
        }

        List<String> employeeIDs = outTeamMemberEmployee.stream()
                .map(TeamMemberEmployeeInfo::getUserId)
                .collect(Collectors.toList());
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);

        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        List<TeamMember> deleteMember = teamMembers.stream()
                .filter(TeamMember::isOutMember)
                .filter(it -> TeamMember.Role.OWNER != it.getRole())
                .filter(it -> employeeIDs.contains(it.getEmployee()))
                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(deleteMember)) {
            return;
        }
        teamMembers.removeAll(deleteMember);
        objectDataExt.setTeamMembers(teamMembers);
    }

    private void updateObjectDataByDeleteMember(List<String> employeeIDs, IObjectData objectData) {
        // 批量移除相关团队按钮操作，目前只支持上下游各自独立操作相关团队，下游请求不能处理上游相关团队
        if (actionContext.getUser().isOutUser()) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        for (String emp : employeeIDs) {
            //负责人不允许删除
            //从原相关团队中获取原负责人
            Optional<TeamMember> oldOwnerTeamMemberOpt = teamMembers.stream()
                    .filter(f -> !f.isOutMember() && f.getRole() == TeamMember.Role.OWNER)
                    .findFirst();
            Optional<String> ownerId = objectDataExt.getOwnerId();
            if (ownerId.isPresent()
                    && oldOwnerTeamMemberOpt.isPresent()
                    && ownerId.get().equals(oldOwnerTeamMemberOpt.get().getEmployee())
                    && Objects.equals(emp, ownerId.get())) {
                failReasons.add(I18NExt.getOrDefault(I18NKey.OWNER_CAN_NOT_DELETE, "操作\\{0}\\失败原因负责人不能删除", objectData.getName()));// ignoreI18n
                continue;
            }
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(it -> !it.isOutMember())
                    .filter(f -> f.getEmployee().equals(emp)).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
        }
        objectDataExt.setTeamMembers(teamMembers);
    }

    @Data
    public static class Arg {
        //添加无参构造函数，否则内部类转 json报 No suitable constructor found for type
        public Arg() {
        }

        //数据ID列表
        private List<String> dataIDs;
        //团队成员ID列表
        private List<String> teamMemberEmployee;
        // 外部相关团队信息
        private List<TeamMemberEmployeeInfo> outTeamMemberEmployee;
        //新相关团队数据权限
        private List<TeamMemberInfoPoJo> teamMemberInfos;
        @JsonProperty("ignoreSendingRemind")
        @JSONField(name = "ignoreSendingRemind")
        private Boolean ignoreSendingRemind;

        public static Arg of(String id, List<String> teamMemberEmployee, List<TeamMemberEmployeeInfo> outTeamMemberEmployee) {
            Arg arg = new Arg();
            arg.setDataIDs(Lists.newArrayList(id));
            arg.setTeamMemberEmployee(teamMemberEmployee);
            arg.setOutTeamMemberEmployee(outTeamMemberEmployee);
            return arg;
        }

        public static Arg ofArg(String id, List<TeamMemberInfoPoJo> teamMemberInfos) {
            Arg arg = new Arg();
            arg.setDataIDs(Lists.newArrayList(id));
            arg.setTeamMemberInfos(teamMemberInfos);
            return arg;
        }

        public Map<String, Object> toButtonArg() {
            Map<String, Object> result = Maps.newHashMap();
            //存在outTeamMemberEmployee和teamMemberEmployee同时不为空的情况，以外部为准
            if (CollectionUtils.empty(teamMemberInfos)) {
                List<TeamMemberInfoPoJo> teamMemberInfoPoJoList = Lists.newArrayList();
                TeamMemberInfoPoJo teamMemberInfoPoJo = new TeamMemberInfoPoJo();
                if (CollectionUtils.empty(outTeamMemberEmployee)) {
                    if (CollectionUtils.empty(teamMemberEmployee)) {
                        return result;
                    }
                    teamMemberInfoPoJo.setTeamMemberEmployee(teamMemberEmployee);
                } else {
                    List<TeamMemberInfoPoJo.TeamMemberDTO> teamMemberDTOS = outTeamMemberEmployee.stream()
                            .map(StandardDeleteTeamMemberAction.TeamMemberEmployeeInfo::convertTo)
                            .collect(Collectors.toList());
                    teamMemberInfoPoJo.setOutTeamMemberEmployee(teamMemberDTOS);
                    teamMemberInfoPoJo.setSourceType("2");
                }
                teamMemberInfoPoJo.setTeamMemberType(TeamMember.MemberType.EMPLOYEE.getValue());
                teamMemberInfoPoJoList.add(teamMemberInfoPoJo);
                result.put("teamMemberInfos", teamMemberInfoPoJoList.stream().map(TeamMemberInfoPoJo::toMap).collect(Collectors.toList()));
            } else {
                result.put("teamMemberInfos", teamMemberInfos.stream().map(TeamMemberInfoPoJo::toMap).collect(Collectors.toList()));
            }
            return result;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    public static class Result implements Serializable {

        private static final long serialVersionUID = -7439910442812147481L;
    }

    @Data
    public static class TeamMemberEmployeeInfo {
        private String userId;
        private String outTenantId;

        public TeamMemberInfoPoJo.TeamMemberDTO convertTo() {
            TeamMemberInfoPoJo.TeamMemberDTO teamMemberDTO = new TeamMemberInfoPoJo.TeamMemberDTO();
            teamMemberDTO.setUserId(userId);
            teamMemberDTO.setOutTenantId(outTenantId);
            return teamMemberDTO;
        }
    }

}
