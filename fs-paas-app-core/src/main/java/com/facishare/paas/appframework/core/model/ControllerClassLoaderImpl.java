package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ControllerClassLoadException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.util.Types;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Controller class loader implements
 * <p>
 * Created by liyiguang on 2017/7/4.
 */
@Service
@Slf4j
public class ControllerClassLoaderImpl implements ControllerClassLoader {
    @Override
    public <T extends AbstractController> Class<T> loadControllerClass(ControllerClassInfo classInfo) {
        StopWatch stopWatch = StopWatch.create("loadControllerClass");
        try {
            if (classInfo.isPreDefine()) {
                Class<?> clazz = Types.forName(classInfo.getClassName());
                stopWatch.lap("Class.forName");
                if (clazz == null) {
                    throw new ControllerClassLoadException(SystemErrorCode.CONTROLLER_FOUND_ERROR);
                }
                if (clazz != null && PreDefineController.class.isAssignableFrom(clazz)) {
                    stopWatch.lap("isAssignableFrom");
                    return (Class<T>) clazz;
                } else {
                    throw new ControllerClassLoadException(SystemErrorCode.CONTROLLER_LOAD_ERROR);
                }
            }
            //TODO: 目前暂时不支持自定义Controller
            throw new UnsupportedOperationException("Unsupported custom controller:" + classInfo);
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    @Override
    public boolean check(ControllerClassInfo classInfo) {
        if (classInfo == null || Strings.isNullOrEmpty(classInfo.getClassName())) {
            return false;
        }
        StopWatch stopWatch = StopWatch.create("checkController");
        try {
            Class clazz = Types.forName(classInfo.getClassName());
            stopWatch.lap("Class.forName");
            return clazz != null;
        } finally {
            stopWatch.logSlow(1000);
        }
    }
}
