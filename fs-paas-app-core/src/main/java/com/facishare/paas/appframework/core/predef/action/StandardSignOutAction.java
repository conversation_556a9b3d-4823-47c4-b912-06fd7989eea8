package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.SignInException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.SignInFieldDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.SignIn;
import com.facishare.paas.metadata.impl.describe.SignInFieldDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 签退
 */
public class StandardSignOutAction extends BaseObjectSignAction {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.SignOut.getFunPrivilegeCodes();
    }

    @Override
    public String getCurrentLocation(IObjectData data, SignInFieldDescribe signInFieldDescribe) {
        return data.get(signInFieldDescribe.getSignOutLocationFieldApiName(), String.class);
    }

    @Override
    public List<String> process(SignInFieldDescribe signInFieldDescribe, IObjectData stored, IObjectData data) {
        if (!Boolean.TRUE.equals(signInFieldDescribe.getIsEnableSignOut())) {
            //未启用签退
            throw new SignInException(I18N.text(I18NKey.NOT_SIGN_OUT));
        }

        String location = compensateAddress((String) data.get(signInFieldDescribe.getSignOutLocationFieldApiName()));
        data.set(signInFieldDescribe.getSignOutLocationFieldApiName(), location);

        if (StringUtils.isNotBlank(signInFieldDescribe.getQuoteField())) {
            //启用了目标地址和允差范围
            long distance = getDistance(signInFieldDescribe, data);
            if (distance > Long.parseLong(SignInFieldDescribeExt.of(signInFieldDescribe).getRadiusRange())) {
                throw new SignInException(I18N.text(I18NKey.BEYOND_SCOPE_SIGN_OUT));
            }
        }

        if (Objects.nonNull(data.getVersion())) {
            // version为null元数据会报错
            stored.setVersion(data.getVersion());
        }
        long signOutTime = System.currentTimeMillis();
        stored.set(signInFieldDescribe.getSignOutTimeFieldApiName(), signOutTime);
        long signInTime = 0;
        Object o = stored.get(signInFieldDescribe.getSignInTimeFieldApiName());
        if (o instanceof Long) {
            signInTime = Long.parseLong(String.valueOf(o));
        }

        long interval = signOutTime - signInTime;
        //换算成小时,若小于6分钟，按6分钟算
        String value = interval <= 1000 * 60 * 6 ? "0.1" : new BigDecimal(interval).divide(new BigDecimal(1000 * 60 * 60), 1, BigDecimal.ROUND_HALF_UP).toPlainString();
        stored.set(signInFieldDescribe.getIntervalFieldApiName(), value);
        stored.set(signInFieldDescribe.getSignOutStatusFieldApiName(), SignIn.SIGN_STATUS_COMPLETE);
        stored.set(signInFieldDescribe.getVisitStatusFieldApiName(), SignIn.SIGN_STATUS_COMPLETE);
        stored.set(signInFieldDescribe.getSignOutLocationFieldApiName(), data.get(signInFieldDescribe.getSignOutLocationFieldApiName()));
        handleNewSignInfo(data, stored, signInFieldDescribe, "sign_out", "sign_out_complete");
        List<String> fields = Lists.newArrayList();
        fields.add(signInFieldDescribe.getSignOutTimeFieldApiName());
        fields.add(signInFieldDescribe.getIntervalFieldApiName());
        fields.add(signInFieldDescribe.getSignOutStatusFieldApiName());
        fields.add(signInFieldDescribe.getVisitStatusFieldApiName());
        fields.add(signInFieldDescribe.getSignOutLocationFieldApiName());
        return fields;
    }
}
