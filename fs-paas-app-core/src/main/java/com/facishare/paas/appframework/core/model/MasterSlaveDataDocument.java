package com.facishare.paas.appframework.core.model;


import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.dto.DetailResource;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MasterSlaveDataDocument extends DocumentBaseEntity {
    private static final String FROM_MASTER_IDS = "fromMasterIds";
    private static final String FROM_DETAILS = "fromDetails";
    private static final String OBJECT_DATA = "objectData";
    private static final String DETAILS = "details";

    public MasterSlaveDataDocument() {
    }

    public MasterSlaveDataDocument(Map<String, Object> map) {
        super(map);
    }

    public static MasterSlaveDataDocument of(MasterSlaveDataModel masterSlaveDataModel) {
        MasterSlaveDataDocument masterSlaveDataDocument = new MasterSlaveDataDocument();
        masterSlaveDataDocument.put(FROM_MASTER_IDS, masterSlaveDataModel.getFromMasterIds());
        masterSlaveDataDocument.put(FROM_DETAILS, masterSlaveDataModel.getFromDetails());
        masterSlaveDataDocument.put(OBJECT_DATA, ObjectDataDocument.of(masterSlaveDataModel.getObjectData()));
        masterSlaveDataDocument.put(DETAILS, ObjectDataDocument.ofMap(masterSlaveDataModel.getDetails()));
        return masterSlaveDataDocument;
    }

    public static List<MasterSlaveDataDocument> ofList(List<MasterSlaveDataModel> masterSlaveDataModels) {
        return CollectionUtils.nullToEmpty(masterSlaveDataModels).stream()
                .map(MasterSlaveDataDocument::of).collect(Collectors.toList());
    }

    public static List<MasterSlaveDataModel> ofDataModelList(List<MasterSlaveDataDocument> masterSlaveDataDocuments) {
        return CollectionUtils.nullToEmpty(masterSlaveDataDocuments).stream()
                .map(data -> MasterSlaveDataModel.builder()
                        .fromMasterIds(CollectionUtils.nullToEmpty((List<String>) data.get(FROM_MASTER_IDS)))
                        .fromDetails(CollectionUtils.nullToEmpty((List<DetailResource>) data.get(FROM_DETAILS)))
                        .objectData(ObjectDataExt.of((Map<String, Object>) data.get(OBJECT_DATA)))
                        .details(ObjectDataDocument.ofDataMap((Map<String, List<ObjectDataDocument>>) data.get(DETAILS)))
                        .build())
                .collect(Collectors.toList());
    }
}
