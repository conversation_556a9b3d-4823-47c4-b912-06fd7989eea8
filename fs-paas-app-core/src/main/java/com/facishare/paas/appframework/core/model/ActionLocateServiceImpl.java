package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.service.ActionProxy;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ActionDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ActionNewInstanceException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.predef.action.CustomButtonAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.util.HandlerGrayConfig;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.google.common.base.Strings;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * Action locate service
 * <p>
 * <p>
 * Created by liyiguang on 2017/6/17.
 */
@Slf4j
@Setter
@Service
public class ActionLocateServiceImpl implements ActionLocateService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private SerializerManager serializerManager;
    @Autowired
    private ActionClassLoader actionClassLoader;
    @Autowired
    private ActionProxy actionProxy;

    private ActionClassInfo customButtonActionClassInfo;

    @PostConstruct
    public void init() {
        customButtonActionClassInfo = new ActionClassInfo(CustomButtonAction.class.getName());
    }

    @Override
    public Action locateAction(ActionContext context, String payload) {
        StopWatch stopWatch = StopWatch.create("locateAction");
        try {
            ActionClassInfo classInfo = findActionClassName(context, true);
            stopWatch.lap("findActionClassName");
            if (classInfo.isPreDefine()) {
                Class<PreDefineAction> preDefineActionClass = actionClassLoader.loadActionClass(classInfo);
                stopWatch.lap("loadActionClass");
                Action action = newPreDefineActionInstance(context, preDefineActionClass, payload);
                stopWatch.lap("newPreDefineActionInstance");
                return action;
            } else {
                //TODO:
                return null;
            }
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    @Override
    public Action locateAction(ActionContext context, Object arg) {
        return locateAction(context, arg, null);
    }

    @Override
    public Action locateAction(ActionContext context, Object arg, Class resultType) {
        ActionClassInfo classInfo = findActionClassName(context, resultType == null);
        //不是自定义对象且resultType不为空，本地没有自定义Action则走rest接口调用
        if (classInfo == null && resultType != null) {
            return locateRemoteAction(context, arg, resultType);
        }
        if (Objects.nonNull(classInfo) && classInfo.isPreDefine()) {
            Class<PreDefineAction> preDefineActionClass = actionClassLoader.loadActionClass(classInfo);
            return newPreDefineActionInstanceWithArg(context, preDefineActionClass, arg);
        } else {
            //TODO:
            return null;
        }
    }

    @Override
    public Action locateRemoteAction(ActionContext context, Object arg, Class resultType) {
        if (Objects.isNull(resultType)) {
            log.warn("locate remote action fail, resultType isNull, context:{}, arg:{}", context, arg);
            throw new ActionNewInstanceException(SystemErrorCode.ACTION_INSTANTIATION_ERROR);
        }
        return RemoteAction.builder()
                .context(context)
                .arg(arg)
                .resultType(resultType)
                .proxy(actionProxy)
                .build();
    }

    PreDefineAction newPreDefineActionInstanceWithArg(
            ActionContext context, Class<PreDefineAction> actionClass, Object arg) {
        try {
            PreDefineAction action = actionClass.newInstance();
            action.setServiceFacade(serviceFacade);
            action.setInfraServiceFacade(infraServiceFacade);
            action.setActionContext(context);
            List<Class> actionListenerClassList = action.getActionListenerClassList();
            actionListenerClassList.forEach(actionListenerClass -> {
                ActionListener listener = (ActionListener) serviceFacade.getBean(actionListenerClass);
                if (listener != null) {
                    action.addActionListener(listener);
                }
            });
            action.setArg(arg);
            return action;
        } catch (InstantiationException | IllegalAccessException e) {
            throw new ActionNewInstanceException(SystemErrorCode.ACTION_INSTANTIATION_ERROR, e);
        }
    }

    PreDefineAction newPreDefineActionInstance(
            ActionContext context, Class<PreDefineAction> actionClass, String payload) {
        StopWatch stopWatch = StopWatch.create("newPreDefineActionInstance");
        try {
            PreDefineAction action = actionClass.newInstance();
            stopWatch.lap("newInstance");
            action.setServiceFacade(serviceFacade);
            action.setInfraServiceFacade(infraServiceFacade);
            action.setActionContext(context);
            List<Class> actionListenerClassList = action.getActionListenerClassList();
            actionListenerClassList.forEach(actionListenerClass -> {
                ActionListener listener = (ActionListener) serviceFacade.getBean(actionListenerClass);
                if (listener != null) {
                    action.addActionListener(listener);
                }
            });
            stopWatch.lap("getListeners");
            JSONSerializer serializer = serializerManager.getSerializer(context.getRequestContext().getContentType());
            Object arg = serializer.decode(action.getArgClass(), payload);
            action.setArg(arg);
            stopWatch.lap("decodeBody");

            return action;
        } catch (InstantiationException | IllegalAccessException e) {
            throw new ActionNewInstanceException(SystemErrorCode.ACTION_INSTANTIATION_ERROR, e);
        } finally {
            stopWatch.logSlow(1000);
        }
    }


    /**
     * 1. 根据APIName查找对应的Action元数据信息
     * 2. 如果有有对应的Action描述直接获取ActionClassInfo
     * 3. 如果是预定义对象直接获取预定对应的ActionClassInfo
     * 4. 如果是标准Action直接获取标准ActionClassInfo
     * 5. 找不到抛出 @ActionDefNotFoundError
     *
     * @param context
     * @param isLocal
     * @return
     */
    ActionClassInfo findActionClassName(ActionContext context, boolean isLocal) {
        if (ObjectAction.isCustomAction(context.getActionCode())) {
            return customButtonActionClassInfo;
        }

        StopWatch stopWatch = StopWatch.create("findControllerClassInfo");

        try {
            //灰度了Handler的Action优先使用标准实现类
            if (HandlerGrayConfig.supportHandler(context.getTenantId(), context.getActionCode(), context.getObjectApiName())) {
                StandardAction action = StandardAction.valueOfAction(context.getActionCode());
                if (Objects.nonNull(action)) {
                    return action.getDefaultActionClassInfo();
                }
            }

            ActionClassInfo actionClassInfo = null;

            PreDefineObject preDefineObject = Strings.isNullOrEmpty(context.getAppId()) ?
                    PreDefineObjectRegistry.getPreDefineObject(context.getObjectApiName()) :
                    PreDefineObjectRegistry.getPreDefineObject(context.getAppId(), context.getObjectApiName());
            stopWatch.lap("getPreDefineObject");

            if (Objects.nonNull(preDefineObject)) {
                actionClassInfo = preDefineObject.getDefaultActionClassInfo(context.getActionCode());
                stopWatch.lap("preDefineObject.getDefaultActionClassInfo");
                boolean success = actionClassLoader.check(actionClassInfo);
                stopWatch.lap("preDefineObject.check");
                if (success) {
                    return actionClassInfo;
                }
            }

            ModuleClassLocator moduleClassLocator = PreDefineObjectRegistry.getModuleClassLocator(context.getAppId());
            stopWatch.lap("getModuleClassLocator");
            if (Objects.nonNull(moduleClassLocator)) {
                actionClassInfo = moduleClassLocator.findActionClassInfo(context.getActionCode());
                stopWatch.lap("moduleClassLocator.getDefaultActionClassInfo");
                boolean success = actionClassInfo != null && actionClassLoader.check(actionClassInfo);
                stopWatch.lap("moduleClassLocator.check");
                if (success) {
                    return actionClassInfo;
                }
            }

            //没有在本地注册，不是自定义对象，并且支持远程调用
            if (actionClassInfo == null && !ObjectDescribeExt.isCustomObject(context.getObjectApiName()) && !isLocal) {
                return null;
            }

            StandardAction action = StandardAction.valueOfAction(context.getActionCode());
            if (Objects.nonNull(action)) {
                return action.getDefaultActionClassInfo();
            }

            throw new ActionDefNotFoundError(SystemErrorCode.ACTION_FOUND_ERROR);
        } finally {
            stopWatch.logSlow(1000);
        }
    }
}
