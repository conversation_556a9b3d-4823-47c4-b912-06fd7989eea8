package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.service.ControllerProxy;
import com.facishare.paas.appframework.core.rest.APIResult;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.collect.Maps;
import lombok.Builder;

import java.util.Map;

@Builder
public class RemoteController<A, R> implements Controller<A, R> {

    private Class<R> resultType;
    private ControllerContext context;
    private A arg;
    private ControllerProxy proxy;

    @Override
    public R service(A arg) {
        Map<String, String> headers = RestUtils.buildHeaders(context.getUser());
        Map<String, String> pathParams = Maps.newHashMap();
        pathParams.put("apiName", context.getObjectApiName());
        pathParams.put("methodName", context.getMethodName());
        Map<String, String> queryParams = RestUtils.buildQueryParams();
        String restResult = proxy.executeController(arg, headers, pathParams, queryParams);
        return parseResult(restResult);
    }

    @Override
    public A getArg() {
        return arg;
    }

    private R parseResult(String restResult) {
        APIResult<R> result = JacksonUtils.fromJson(restResult, APIResult.class, resultType);
        if (!result.isSuccess()) {
            throw new ValidateException(result.getMessage());
        }
        return result.getData();
    }
}
