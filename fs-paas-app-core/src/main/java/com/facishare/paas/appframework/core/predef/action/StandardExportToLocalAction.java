package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.metadata.util.XmlUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.QuoteFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.DocumentException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2018/12/3 3:50 PM
 */
@Slf4j
public class StandardExportToLocalAction extends PreDefineAction<StandardExportToLocalAction.Arg, StandardExportToLocalAction.Result> {
    /**
     * 线程池
     */
    private ThreadPoolTaskExecutor threadExecutor = SpringUtil.getContext().getBean(ThreadPoolTaskExecutor.class);
    /**
     * 缓存，保存导出到网盘的状态
     */
    protected RedisDao redisDao = SpringUtil.getContext().getBean(RedisDao.class);
    /**
     * 缓存有效期
     */
    private static final int TOKEN_EXPIRE_SECONDS = 3600;
    /**
     * 每次查询数据最大允许数量
     */
    public static final int DATA_BATCH_SIZE = 100;
    /**
     * 导出到网盘方式一： 单级目录
     */
    private static final int EXPORT_METHOD_ONE_LAYER = 1;
    /**
     * 导出到网盘方式二： 三级目录
     */
    private static final int EXPORT_METHOD_THREE_LAYER = 2;
    /**
     * 个人文件根目录ID
     */
    private static final String ROOT_PATH = "00000000000000000000000000000000";
    /**
     * 查询数据模板
     */
    protected SearchTemplateQuery searchTemplateQuery;
    /**
     * 导出到网盘状态
     */
    private int exportStatus = NOT_START;
    private final static int NOT_START = 0;
    private final static int IN_PROCESS = 100;
    private final static int FINISHED = 200;
    /**
     * 导出方式
     */
    private int method;
    protected List<IObjectData> dataToExport = Lists.newArrayList();
    /**
     * 导出字段
     */
    protected List<IFieldDescribe> fieldDescribeToExport = Lists.newArrayList();
    /**
     * 文件系统服务
     */
    private FileStoreService fileStoreService;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.ExportToLocal.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        fileStoreService = serviceFacade.getBean(FileStoreService.class);
        method = arg.getMethod();
        //需要导出的字段apiName
        List<String> fieldApiNameList = arg.getFieldApiNames();
        for (IFieldDescribe field : objectDescribe.getFieldDescribes()) {
            if (fieldApiNameList.contains(field.getApiName())) {
                if (IFieldType.QUOTE.equals(field.getType())) {
                    String type = ((QuoteFieldDescribe) field).getQuoteFieldType();
                    Map<String, Object> map = FieldDescribeExt.of(field).toMap();
                    map.put(IFieldDescribe.TYPE, type);
                    IFieldDescribe CustomField = FieldDescribeFactory.newInstance(map);
                    fieldDescribeToExport.add(CustomField);
                } else {
                    fieldDescribeToExport.add(field);
                }
            }
        }
        if (CollectionUtils.notEmpty(fieldApiNameList)) {
            //处理gdpr相关逻辑
            List<String> needFilterGdprFields = infraServiceFacade.needFilterGdprFields(actionContext.getUser(), objectDescribe.getApiName(), ObjectAction.BATCH_EXPORT.getActionCode());
            fieldApiNameList.removeIf(needFilterGdprFields::contains);
            if (CollectionUtils.empty(fieldApiNameList)) {
                throw new MetaDataBusinessException(I18NExt.getOrDefault(I18NKey.GDPR_EXPORT_NOT_SUPPORT, "GDPR不支持导出该字段"));// ignoreI18n
            }
        }
        if (CollectionUtils.notEmpty(arg.getDataIdList())) {
            dataToExport.addAll(serviceFacade.findObjectDataByIds(actionContext.getTenantId(),
                    arg.getDataIdList(), objectDescribe.getApiName()));
        } else {
            searchTemplateQuery = getSearchQuery();
        }
    }

    private SearchTemplateQuery getSearchQuery() {
        SearchTemplateQuery searchTemplateQuery;
        if (serviceFacade.isSupportOrFilter(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            searchTemplateQuery = (SearchTemplateQuery) defineQuery(actionContext.getUser(), arg.getSearchTemplateId(),
                    arg.getSearchQueryInfo(), arg.getDataIdList()).toSearchTemplateQuery();
        } else {
            searchTemplateQuery = generateSearchQuery(actionContext.getUser(), arg.getSearchTemplateId(),
                    arg.getSearchQueryInfo(), arg.getDataIdList());
        }
        if (AppFrameworkConfig.isExportUseDbTenantId(actionContext.getTenantId())) {
            searchTemplateQuery.setSearchSource("db");
        }
        return customSearchTemplate(searchTemplateQuery);
    }

    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchTemplateQuery) {
        return searchTemplateQuery;
    }

    protected Query defineQuery(User user, String searchTemplateId,
                                String searchQueryInfo, List<String> dataIdList) {
        SearchQueryContext queryContext = SearchQueryContext.builder()
                .templateId(searchTemplateId)
                .isRelatedPage(false)
                .build();
        Query query = serviceFacade.findSearchQuery(user, objectDescribe, searchQueryInfo, queryContext);
        if (CollectionUtils.notEmpty(dataIdList)) {
            SearchQuery searchQuery = query.getSearchQuery()
                    .map(it -> it.and(FilterExt.of(Operator.IN, IObjectData.ID, dataIdList).getFilter()))
                    .orElse(SearchQueryImpl.filter(FilterExt.of(Operator.IN, IObjectData.ID, dataIdList).getFilter()));
            query.setSearchQuery(searchQuery);
        }
        // 忽略掉前端传过来的offset
        query.setOffset(0);
        return query;
    }

    protected SearchTemplateQuery generateSearchQuery(User user, String searchTemplateId, String searchQueryInfo,
                                                      List<String> dataIdList) {
        SearchTemplateQuery query = serviceFacade
                .getSearchTemplateQuery(user, ObjectDescribeExt.of(objectDescribe), searchTemplateId,
                        searchQueryInfo);
        if (CollectionUtils.notEmpty(dataIdList)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.IN, IObjectData.ID, dataIdList);
        }
        return query;
    }

    @Override
    protected Result doAct(Arg arg) {
        String token = queryAndExport();
        return generateResult(token);
    }

    /**
     * 查询数据并导出图片/附件到文件中心
     */
    private String queryAndExport() {
        if (CollectionUtils.notEmpty(dataToExport)) {
            return createFolderAndExport(consumerDataList(dataToExport));
        } else {
            List<IObjectData> dataList = Lists.newArrayList();
            int limit = searchTemplateQuery.getLimit();
            if (limit > 100) {
                searchTemplateQuery.setLimit(DATA_BATCH_SIZE);
            }
//            int offset = 0;
//            searchTemplateQuery.setOffset(offset);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), objectDescribe,
                    objectDescribe.getApiName(), searchTemplateQuery);
            dataList.addAll(queryResult.getData());
            return createFolderAndExport(consumerDataList(dataList));
        }
    }

    /**
     * 业务可以自己加工数据
     *
     * @param dataList
     * @return
     */
    protected List<IObjectData> consumerDataList(List<IObjectData> dataList) {
        return dataList;
    }

    /**
     * 创建目录并导出图片/附件到网盘
     */
    private String createFolderAndExport(List<IObjectData> dataList) {
        convertImageAndAttachFile(dataList);
        String xml = null;
        if (method == EXPORT_METHOD_ONE_LAYER) {
            xml = XmlUtil.create1LayerXml(objectDescribe, fieldDescribeToExport, dataList);
        } else if (method == EXPORT_METHOD_THREE_LAYER) {
            xml = XmlUtil.create3LayerXml(objectDescribe, fieldDescribeToExport, dataList);
        }
        log.info("tenantId:{}, userId:{}, xml:{}", actionContext.getTenantId(), actionContext.getUser().getUserId(), xml
        );
        try {
            if (!XmlUtil.hasFile(xml)) {
                return "0";
            }
        } catch (DocumentException e) {
            log.error("Parsing xml error, tenantId:{}, userId:{}, xml:{}", actionContext.getTenantId(),
                    actionContext.getUser().getUserId(), xml);
            throw new ValidateException("dealing files error!");
        }
        String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
        return infraServiceFacade.exportFilesWithXml(xml, actionContext.getTenantId(), ea, actionContext.getUser(),
                XmlUtil.filterName(objectDescribe.getDisplayName()));
    }

    private void convertImageAndAttachFile(List<IObjectData> dataList) {

        for (IFieldDescribe fieldDescribe : fieldDescribeToExport) {
            for (IObjectData data : dataList) {
                Object value = infraServiceFacade.convertData(data, fieldDescribe, actionContext.getUser());
                data.set(fieldDescribe.getApiName(), value);
            }
        }
        // 元数据不会返回图片字段的filename，先调用文件中心接口获取filename
        // fillImageFileName(dataList);
    }

    /**
     * 补充图片文件名称filename
     */
    private void fillImageFileName(List<IObjectData> dataList) {
        for (IFieldDescribe field : fieldDescribeToExport) {
            if (IFieldType.IMAGE.equals(field.getType())) {
                // 批量获取图片NPath
                List<String> nPaths = dataList.stream().map(d -> d.get(field.getApiName()))
                        .flatMap(ps -> Stream.of(ps.toString().split("\\|")))
                        .distinct()
                        .collect(Collectors.toList());
                if (CollectionUtils.empty(nPaths)) {
                    continue;
                }
                // 调用文件中心接口批量获取图片名称
                Map<String, String> pathNameMap = fileStoreService.getFileNameList(nPaths, actionContext.getUser());
                // 把name和nPath组合起来 filename#nPath
                for (IObjectData data : dataList) {
                    Object value = data.get(field.getApiName());
                    if (Objects.nonNull(value)) {
                        String path = (String) value;
                        String newValue = Stream.of(path.split("\\|"))
                                .map(s -> pathNameMap.getOrDefault(s, s) + "#" + s)
                                .collect(Collectors.joining("|"));
                        data.set(field.getApiName(), newValue);
                    }
                }

            }
        }

    }

    /**
     * 生成响应
     */
    private Result generateResult(String token) {
        return Result.builder().token(token).build();
    }

    @Data
    public static class Arg {
        @JSONField(name = "object_describe_api_name")
        @JsonProperty(value = "object_describe_api_name")
        @SerializedName(value = "object_describe_api_name")
        String describeApiName;

        @JSONField(name = "dataIdList")
        @JsonProperty(value = "dataIdList")
        @SerializedName(value = "dataIdList")
        List<String> dataIdList;

        @JSONField(name = "search_template_id")
        @JsonProperty(value = "search_template_id")
        @SerializedName(value = "search_template_id")
        String searchTemplateId;

        @JSONField(name = "search_query_info")
        @JsonProperty(value = "search_query_info")
        @SerializedName(value = "search_query_info")
        String searchQueryInfo;

        @JSONField(name = "method")
        @JsonProperty(value = "method")
        @SerializedName(value = "method")
        int method;

        @JSONField(name = "field_api_name_list")
        @JsonProperty(value = "field_api_name_list")
        @SerializedName(value = "field_api_name_list")
        List<String> fieldApiNames;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "token")
        String token;
    }
}
