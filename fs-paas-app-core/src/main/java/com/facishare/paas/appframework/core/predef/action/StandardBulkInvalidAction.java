package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction.Arg;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction.Result;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.dto.FieldMapping;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
//@Idempotent(serializer = Serializer.Type.java)
public class StandardBulkInvalidAction extends BaseObjectInvalidAction<Arg, Result> {

    private static final String FIELD_LIST = "dataList";
    private static final String FIELD_DATA_ID = "_id";
    private List<String> dataIds = null;

    @Override
    protected String getIndustryCode(Arg arg) {
        return arg.getIndustryCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.BulkInvalid.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return getDataIds(arg);
    }

    @Override
    protected void initDataList() {
        if (isContainIdFieldMapping()) {
            MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                    .user(actionContext.getUser())
                    .skipRelevantTeam(skipQueryRelateTeam())
                    .isSimple(needSimple())
                    .includeInvalid(needInvalidData())
                    .build();
            dataList = infraServiceFacade.queryDataBySpecifiedField(queryContext, objectDescribe, Lists.newArrayList(arg.getFieldMapping()), Lists.newArrayList(arg.getObjectData().toObjectData()));
            if (CollectionUtils.empty(dataList)) {
                throw new ObjectDataNotFoundException(I18N.text(I18NKey.DATA_NOT_USED));
            }
        } else {
            super.initDataList();
        }
    }

    private List<String> getDataIds(Arg arg) {
        if (Objects.nonNull(this.dataIds)) {
            return this.dataIds;
        }
        if (Objects.isNull(arg) || StringUtils.isBlank(arg.getJson())) {
            throw new ValidateException(I18N.text(I18NKey.DATA_TO_INVALID_EMPTY));
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = JSON.parseObject(arg.getJson());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ValidateException(I18N.text(I18NKey.DATA_TO_INVALID_FORMAT_ERROR));
        }
        JSONArray jsonArray = jsonObject.getJSONArray(FIELD_LIST);
        if (Objects.isNull(jsonArray)) {
            throw new ValidateException(I18N.text(I18NKey.DATA_TO_INVALID_EMPTY));
        }
        this.dataIds = jsonArray.stream().map(x -> ((JSONObject) x).getString(FIELD_DATA_ID)).collect(Collectors.toList());
        return this.dataIds;
    }

    @Override
    protected void initObjectDataList() {
        if (isContainIdFieldMapping()) {
            if (CollectionUtils.notEmpty(this.dataList)) {
                this.objectDataList = this.dataList;
            } else {
                throw new MetaDataBusinessException(I18N.text(I18NKey.DATA_NOT_USED));
            }
            return;
        }
        List<String> ids = getDataIds(arg);
        if (CollectionUtils.notEmpty(this.dataList)) {
            this.objectDataList = this.dataList;
        } else {
            IActionContext context = ActionContextExt.of(actionContext.getUser()).getContext();
            this.objectDataList = serviceFacade.findObjectDataByIdsExcludeInvalid(context, ids, objectDescribe.getApiName());
        }
        Set<String> dbIds = objectDataList.stream().map(IObjectData::getId).collect(Collectors.toSet());
        ids.forEach(id -> {
            if (!dbIds.contains(id)) {
                throw new MetaDataBusinessException(I18N.text(I18NKey.OBJECT_DATA_NOT_FOUND, id));
            }
        });
    }

    private boolean isContainIdFieldMapping() {
        return Objects.nonNull(arg.getFieldMapping()) && infraServiceFacade.containIdFieldMapping(actionContext.getUser(), objectDescribe, Lists.newArrayList(arg.getFieldMapping()));
    }

    @Override
    protected Result generateResult() {
        return Result.builder()
                .objectDataList(Objects.nonNull(bulkOpResult.getSuccessObjectDataList()) ?
                        ObjectDataDocument.ofList(bulkOpResult.getSuccessObjectDataList()) : null)
                .failureObjectDataList(Objects.nonNull(bulkOpResult.getFailObjectDataList()) ?
                        ObjectDataDocument.ofList(bulkOpResult.getFailObjectDataList()) : null)
                .failureNotice(bulkOpResult.getFailReason()).build();
    }

    @Override
    protected Map<String, Object> getArgs() {
        return CollectionUtils.nullToEmpty(arg.getArgs());
    }

    /**
     * 父类新接口要加参数
     * 本接口要加参数重新新接口
     * 参数包括传给流程暂存的（为了流程审批成功调用审批动作）和流程审批需要展示给用户看的
     *
     * @param objectDataListToTriggerApprovalFlow
     * @return
     */
    @Override
    protected Map<String, ApprovalFlowStartResult> tryToStartInvalidApprovalFlow(List<IObjectData> objectDataListToTriggerApprovalFlow,
                                                                                 Map<String, Map<String, Object>> dataMap,
                                                                                 Map<String, Map<String, Object>> callBackDataMap) {
        //InvalidAction的特殊逻辑：
        //一条以上数据异步处理
        //一条数据使用父类com.facishare.paas.appframework.core.predef.action.BaseObjectInvalidAction.tryToStartInvalidApprovalFlow
        if (objectDataListToTriggerApprovalFlow.size() > 1) {
            //未生效数据
            List<IObjectData> ineffectiveDataList = objectDataListToTriggerApprovalFlow.stream().filter(x -> ObjectDataExt.of(x).isIneffective()).collect(Collectors.toList());
            //不是未生效的数据
            List<IObjectData> effectiveDataList = objectDataListToTriggerApprovalFlow.stream().filter(x -> !ObjectDataExt.of(x).isIneffective()).collect(Collectors.toList());

            needTriggerInvalidAfterActionDataList.addAll(ineffectiveDataList);
            startApprovalFlowAsynchronous(effectiveDataList, ApprovalFlowTriggerType.INVALID, dataMap, callBackDataMap);

            return Maps.newHashMap();
        } else {
            return super.tryToStartInvalidApprovalFlow(objectDataListToTriggerApprovalFlow, dataMap, callBackDataMap);
        }
    }

    @Override
    protected boolean isBatchAction() {
        return CollectionUtils.size(objectDataList) > 1;
    }

    @Override
    protected final List<String> getRecordTypes() {
        return CollectionUtils.nullToEmpty(objectDataList).stream()
                .map(MultiRecordType::getRecordType)
                .filter(x -> !Strings.isNullOrEmpty(x))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    protected final BulkInvalidActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        List<IObjectData> pluginDataList = CollectionUtils.nullToEmpty(objectDataList).stream()
                .filter(x -> CollectionUtils.empty(recordTypeList) || recordTypeList.contains(x.getRecordType()))
                .collect(Collectors.toList());
        return BulkInvalidActionDomainPlugin.Arg.builder()
                .objectDataList(ObjectDataDocument.ofList(pluginDataList))
                .extraData(arg.getExtraData())
                .build();
    }

    @Override
    protected final String getInterfaceCode() {
        return StandardAction.Invalid.name();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Arg {

        @JSONField(name = "M1")
        private String json;

        @JsonProperty("field_mapping")
        @SerializedName("field_mapping")
        private FieldMapping fieldMapping;

        @JsonProperty("object_data")
        @SerializedName("object_data")
        private ObjectDataDocument objectData;

        /**
         * 存放按钮入参
         * 示例：
         * {
         * "name1": "value1",
         * "name2": "value2"
         * }
         */
        private Map<String, Object> args;

        private List<ArgHelper> dataList;

        private String industryCode;
        private Map<String, Object> extraData;

        public static Arg fromJson(String argJson) {
            if (Strings.isNullOrEmpty(argJson)) {
                return null;
            }
            return JSON.parseObject(argJson, Arg.class);
        }

        public static Arg fromArgHelpers(List<ArgHelper> dataList) {
            if (CollectionUtils.empty(dataList)) {
                return null;
            }
            Arg arg = new Arg();
            Map<String, List<ArgHelper>> jsonArg = Maps.newHashMap();
            jsonArg.put(FIELD_LIST, dataList);
            arg.setJson(JSON.toJSONString(jsonArg));
            arg.setDataList(dataList);
            return arg;
        }

        private List<ArgHelper> dataList() {
            if (Objects.nonNull(dataList)) {
                return dataList;
            }
            Arg arg = fromJson(json);
            if (Objects.isNull(arg)) {
                return dataList = Collections.emptyList();
            }
            return dataList = arg.getDataList();
        }

        @JsonIgnore
        public List<String> getDataIds() {
            return dataList().stream().map(ArgHelper::getId).collect(Collectors.toList());
        }

        public List<StandardInvalidAction.Arg> toStandardInvalidActionArgList() {
            return dataList().stream().map(x -> x.toStandardInvalidActionArg(this)).collect(Collectors.toList());
        }

        public void putExtraData(String key, Object value) {
            if (Objects.isNull(extraData)) {
                extraData = Maps.newHashMap();
            }
            extraData.put(key, value);
        }

        public Object getExtraData(String key) {
            if (Objects.isNull(extraData)) {
                return null;
            }
            return extraData.get(key);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ArgHelper {
        @JsonProperty("object_describe_id")
        @JSONField(name = "object_describe_id")
        private String objectDescribeId;
        @JsonProperty("object_describe_api_name")
        @JSONField(name = "object_describe_api_name")
        private String objectDescribeApiName;
        @JsonProperty("_id")
        @JSONField(name = "_id")
        private String id;

        public StandardInvalidAction.Arg toStandardInvalidActionArg(Arg arg) {
            return StandardInvalidAction.Arg.builder()
                    .objectDataId(id)
                    .objectDescribeApiName(objectDescribeApiName)
                    .objectDescribeId(objectDescribeId)
                    .args(arg.getArgs())
                    .industryCode(arg.getIndustryCode())
                    .build();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result implements Serializable {

        private static final long serialVersionUID = -6745180389462400615L;
        @JSONField(name = "M13")
        private List<ObjectDataDocument> objectDataList;

        @JSONField(name = "M14")
        private List<ObjectDataDocument> failureObjectDataList;

        @JSONField(name = "M15")
        private String failureNotice;
    }
}
