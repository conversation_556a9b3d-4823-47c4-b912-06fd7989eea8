package com.facishare.paas.appframework.core.predef.action.uievent;

import com.facishare.paas.appframework.core.exception.UIEventBusinessException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUIEvent;

import java.util.List;
import java.util.Map;

/**
 * UI事件处理器, 实例化处理器对象后，通过
 * setEvent和setActionContext初始化处理器
 * <AUTHOR>
 * @date 2019-08-05 18:03
 */
public interface Processor {
    /**
     * 此API调用具体处理器实例的invoke方法，处理请求request，将结果回填到request中
     * invoke方法中可调用ProcessorContext的invokeNext实现链式调用
     * @param request 请求
     * @param context 处理器上下文
     */
    void invoke(UIEventProcess.ProcessRequest request, ProcessorContext context);

    void setEvent(IUIEvent event);
}
