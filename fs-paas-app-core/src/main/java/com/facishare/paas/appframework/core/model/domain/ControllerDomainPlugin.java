package com.facishare.paas.appframework.core.model.domain;

import com.facishare.paas.appframework.core.model.ControllerContext;

/**
 * Created by zhouwr on 2022/11/2.
 */
public interface ControllerDomainPlugin<A extends DomainPlugin.Arg, R extends DomainPlugin.Result> extends DomainPlugin {
    String BEFORE = "before";

    String AFTER = "after";

    R before(ControllerContext context, A arg);

    R after(ControllerContext context, A arg);
}
