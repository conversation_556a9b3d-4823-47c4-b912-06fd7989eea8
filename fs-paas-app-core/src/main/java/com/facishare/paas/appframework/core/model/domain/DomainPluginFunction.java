package com.facishare.paas.appframework.core.model.domain;

import java.util.List;

/**
 * Created by zhouwr on 2022/1/24.
 */
public interface DomainPluginFunction {
    @FunctionalInterface
    interface BuildArgFunction {
        DomainPlugin.Arg apply(String method, List<String> recordTypeList);
    }

    @FunctionalInterface
    interface RunFunction {
        DomainPlugin.Result apply(DomainPlugin domainPlugin, DomainPlugin.Arg arg);
    }

    @FunctionalInterface
    interface ProcessResultFunction {
        void apply(String method, DomainPlugin.Arg arg, DomainPlugin.Result result);
    }
}
