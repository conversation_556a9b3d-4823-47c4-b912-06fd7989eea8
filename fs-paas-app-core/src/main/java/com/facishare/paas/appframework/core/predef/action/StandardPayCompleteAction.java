package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.common.utils.AesException;
import com.facishare.open.common.utils.MsgEncryptor;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.payment.PayMethod;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.describe.PaymentFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.timezone.DateTimeFormat;
import com.facishare.paas.timezone.DateTimeFormatUtils;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.facishare.paas.timezone.config.TimeZoneConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;

import static com.facishare.paas.metadata.api.describe.Payment.PAY_STATUS_COMPLETE;

public class StandardPayCompleteAction extends PreDefineAction<StandardPayCompleteAction.Arg, StandardPayCompleteAction.Result> {
    private static final String AES_KEY = "abcdefghijklmnopqrstuvwxyz0123456789ABCDEFG";
    private IObjectData updatedData;
    private PaymentFieldDescribe paymentFieldDescribe;
    private SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private CRMNotificationService crmNotificationService = (CRMNotificationService) SpringUtil.getContext()
            .getBean("crmNotificationService");

    @Override
    protected void init() {
        //do nothing
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        paymentFieldDescribe = ObjectDescribeExt.of(objectDescribe).getPaymentFieldDescribe()
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));

    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected Result doAct(Arg arg) {
        String decrypt = decryptContent();
        CallbackData callbackParam = JSONObject.parseObject(decrypt, CallbackData.class);
        IObjectData data = serviceFacade.findObjectData(actionContext.getUser(), callbackParam.getBusiNo(), callbackParam.getApiName());
        data.set(paymentFieldDescribe.getPayStatusFieldApiName(), PAY_STATUS_COMPLETE);
        data.set(paymentFieldDescribe.getPayTimeFieldApiName(), callbackParam.getFinishTime());
        data.set(paymentFieldDescribe.getPayTypeFieldApiName(), PayMethod.parseByName(callbackParam.getPayType()).getLabel());
        updatedData = serviceFacade.updateObjectData(actionContext.getUser(), data);
        return Result.builder().build();
    }

    private String decryptContent() {
        try {
            return MsgEncryptor.decrypt(arg.getContent(), AES_KEY);
        } catch (AesException e) {
            log.warn("Can not decrypt message content in pay callback, content:{}", arg.getContent(), e);
        }
        return null;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (Objects.isNull(updatedData)) {
            log.warn("Updated data is null, arg:{}", arg);
            return result;
        }

        serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, objectDescribe, updatedData);

        //发送crm提醒
//        CRMNotification notification = CRMNotification.builder()
//                .content(String.format(I18N.text(I18NKey.PAY_REMIND_CONTENT),
//                        objectDescribe.getDisplayName(), updatedData.getName(),
//                        updatedData.get(paymentFieldDescribe.getPayAmountFieldApiName()),
//                        getDateTimeText()))
//                .dataId(updatedData.getId())
//                .sender(actionContext.getUser().getUserId())
//                .remindRecordType(CRMNotification.PAY_CALLBACK_REMIND_TYPE)
//                .title(I18N.text(I18NKey.RECEIPT_MESSAGE_TITLE))
//                .receiverIds(Sets.newHashSet(ObjectDataExt.of(updatedData).getOwnerIdInt()))
//                .fixContent2ID(String.format("{\"apiname\":\"%s\"}", objectDescribe.getApiName()))
//                .build();
//        crmNotificationService.sendCRMNotification(actionContext.getUser(), notification);

        NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                .senderId(actionContext.getUser().getUserId())
                .type(NewCrmNotification.PAY_CALLBACK_REMIND_TYPE)
                .receiverIDs(Sets.newHashSet(ObjectDataExt.of(updatedData).getOwnerIdInt()))
                .title("收款成功通知")// ignoreI18n
                .remindSender(true)
                .objectId(updatedData.getId())
                .objectApiName(objectDescribe.getApiName())
                .titleInfo(InternationalItem.builder().internationalKey(I18NKey.RECEIPT_MESSAGE_TITLE).build())
                .fullContent(String.format("对象名称：%s，对象主属性：%s，金额：%s，支付时间：%s",// ignoreI18n
                        objectDescribe.getDisplayName(),
                        updatedData.getName(),
                        updatedData.get(paymentFieldDescribe.getPayAmountFieldApiName()),
                        getDateTimeText()))
                .fullContentInfo(InternationalItem.builder().
                        internationalKey(I18NKey.NEW_PAY_REMIND_CONTENT).
                        internationalParameters(Lists.newArrayList(
                                objectDescribe.getDisplayName(),
                                updatedData.getName(),
                                updatedData.get(paymentFieldDescribe.getPayAmountFieldApiName(), String.class),
                                getDateTimeText())).build())
                .urlType(1)
                .build()
                .addUrlParameter("objectApiName", objectDescribe.getApiName())
                .addUrlParameter("objectId", updatedData.getId());

        crmNotificationService.sendNewCrmNotification(actionContext.getUser(), newCrmNotification);
        return result;
    }

    private String getDateTimeText() {
        if (TimeZoneConfig.INSTANCE.isGray(actionContext.getTenantId())) {
            return DateTimeFormatUtils.formatWithTimezoneInfo(updatedData.get(paymentFieldDescribe.getPayTimeFieldApiName()),
                    TimeZoneContextHolder.getTenantTimeZone(), DateTimeFormat.DATE_TIME.getType());
        }
        return simpleDateFormat.format(updatedData.get(paymentFieldDescribe.getPayTimeFieldApiName()));
    }

    @Data
    @ToString
    public static class CallbackData {
        private String orderNo;
        private String busiNo;
        private String busiCode;
        private Integer status;
        private Long amount;
        private Long fee;
        private String payType;
        private Long finishTime;
        private Long transTime;
        private String enterpriseAccount;
        private String apiName;
    }

    @Data
    @ToString
    public static class Arg {
        private String nonce;
        private String timeStamp;
        private String content;
        private String sig;
    }

    @Data
    @Builder
    @NoArgsConstructor
    public static class Result {

    }
}
