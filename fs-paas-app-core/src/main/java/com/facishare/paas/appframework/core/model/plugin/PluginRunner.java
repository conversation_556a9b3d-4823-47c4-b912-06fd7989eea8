package com.facishare.paas.appframework.core.model.plugin;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.model.RequestType;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2022/4/14.
 */
@Slf4j
@Builder
public class PluginRunner {
    private PluginManager pluginManager;
    private boolean needProcessResult;
    private RequestType requestType;
    private String objectApiName;
    private String requestCode;
    private String method;
    private String tenantId;
    private String agentType;
    private PluginFunction.BuildArgFunction buildArgFunction;
    private PluginFunction.RunFunction runFunction;
    private PluginFunction.ProcessResultFunction processResultFunction;
    private MtFunctionPluginConf pluginConfig;

    private void runByDB() {
        StopWatch stopWatch = StopWatch.create("runPluginByDB_" + method);
        try {
            if (!(Objects.nonNull(pluginConfig)
                    && pluginConfig.agentTypeMatch(agentType)
                    && CollectionUtils.nullToEmpty(pluginConfig.getMethods()).contains(method))) {
                stopWatch.lap("noPlugin");
                return;
            }

            String pluginProvider = pluginConfig.getPluginProvider();
            Plugin plugin = pluginManager.getPlugin(requestType, requestCode, pluginProvider);
            stopWatch.lap("getPlugin" + "_" + pluginProvider);
            if (Objects.isNull(plugin)) {
                return;
            }

            // 构造插件执行参数
            Plugin.Arg pluginArg = buildArgFunction.apply(plugin, method);
            pluginArg.setObjectApiName(objectApiName);
            pluginArg.setFunctionPluginConf(pluginConfig);
            stopWatch.lap("buildArg");

            // 执行插件
            Plugin.Result pluginResult = runFunction.apply(plugin, pluginArg);
            stopWatch.lap("runPlugin");

            // 处理插件返回结果
            if (needProcessResult) {
                processResultFunction.apply(plugin, method, pluginArg, pluginResult);
                stopWatch.lap("processResult");
            }
        } catch (RuntimeException e) {
            if (e instanceof AppBusinessException) {
                log.warn("run plugin failed,method:{}", method, e);
            }
            else {
                log.error("run plugin error,method:{}", method, e);
            }
            if (!Objects.equals(method, ActionPlugin.FINALLY_DO)) {
                throw e;
            }
        } finally {
            stopWatch.log();
        }
    }

    private void runByConfig() {
        // 理论上这里的回参 size 至多为1
        List<String> pluginApiNames = PluginConfig.getPluginApiNames(requestType, objectApiName, requestCode, method, tenantId, agentType);
        if (CollectionUtils.empty(pluginApiNames)) {
            return;
        }
        pluginApiNames.forEach(pluginApiName -> {
            StopWatch stopWatch = StopWatch.create("runPlugin_" + pluginApiName + "_" + method);
            try {
                // 查找插件实现类
                Plugin plugin = pluginManager.getPlugin(requestType, requestCode, pluginApiName);
                stopWatch.lap("getPlugin");
                if (Objects.isNull(plugin)) {
                    return;
                }
                // 构造插件执行参数
                Plugin.Arg pluginArg = buildArgFunction.apply(plugin, method);
                pluginArg.setObjectApiName(objectApiName);
                stopWatch.lap("buildArg");
                // 执行插件
                Plugin.Result pluginResult = runFunction.apply(plugin, pluginArg);
                stopWatch.lap("runPlugin");
                // 处理插件返回结果
                if (needProcessResult) {
                    processResultFunction.apply(plugin, method, pluginArg, pluginResult);
                    stopWatch.lap("processResult");
                }
            } catch (RuntimeException e) {
                if (e instanceof AppBusinessException) {
                    log.warn("run plugin failed,pluginApiName:{},method:{}", pluginApiName, method, e);
                }
                else {
                    log.error("run plugin error,pluginApiName:{},method:{}", pluginApiName, method, e);
                }
                if (!Objects.equals(method, ActionPlugin.FINALLY_DO)) {
                    throw e;
                }
            } finally {
                stopWatch.logSlow(100);
            }
        });
    }

    public void run() {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OBJECT_EXTENSION, tenantId)) {
            runByDB();
        }
        else {
            runByConfig();
        }

    }
}
