package com.facishare.paas.appframework.core.model.handler;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 业务处理器的接口定义
 * Created by zhouwr on 2023/1/6.
 */
public interface Handler<A extends Handler.Arg, R extends Handler.Result> {

    String HANDLE = "handle";

    /**
     * 业务处理器的执行方法
     *
     * @param context 接口上下文
     * @param arg     接口参数
     * @return 业务处理结果
     */
    R handle(HandlerContext context, A arg);

    @Data
    @ToString(exclude = {"objectDescribe"})
    class Arg<T> {
        //自定义的上下文数据
        private Map<String, String> contextData;
        //Handler的描述信息
        private SimpleHandlerDescribe handlerDescribe;
        //接口请求的对象
        @JsonIgnore
        private IObjectDescribe objectDescribe;
        //对象apiName
        private String objectApiName;
        //源接口参数
        private T interfaceArg;
        //主流程中的doAct方法是否执行成功
        private boolean doActComplete;
        //主流程是否处理完成
        private boolean processComplete;
        //功能权限校验用的Code
        private List<String> functionPrivilegeCodes;

        public String getContextData(String key) {
            if (CollectionUtils.empty(contextData)) {
                return null;
            }
            return contextData.get(key);
        }

        public void putContextData(String key, String value) {
            if (Objects.isNull(contextData)) {
                contextData = Maps.newHashMap();
            }
            contextData.put(key, value);
        }
    }

    @Data
    class Result<T> {
        //自定义的上下文数据
        private Map<String, String> contextData;
        //接口返回值
        private T interfaceResult;
        //是否跳过功能权限校验
        private Boolean skipFunctionPrivilegeCheck;

        public String getContextData(String key) {
            if (CollectionUtils.empty(contextData)) {
                return null;
            }
            return contextData.get(key);
        }

        public void putContextData(String key, String value) {
            if (Objects.isNull(contextData)) {
                contextData = Maps.newHashMap();
            }
            contextData.put(key, value);
        }
    }

    @Data
    class APLResult<T extends Handler.Result> {
        private Boolean success;
        private String errorMessage;
        private T result;

        public boolean success() {
            return !Boolean.FALSE.equals(success);
        }
    }
}
