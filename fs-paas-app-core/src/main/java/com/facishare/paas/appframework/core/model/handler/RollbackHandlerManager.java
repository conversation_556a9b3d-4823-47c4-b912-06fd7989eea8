package com.facishare.paas.appframework.core.model.handler;

/**
 * Created by zhouwr on 2023/7/17.
 */
public interface RollbackHandlerManager {
    /**
     * 注册回滚业务处理器
     *
     * @param handler 回滚业务处理器
     */
    void register(RollbackHandler handler);

    /**
     * 根据接口编码和对象ApiName查找回滚业务处理器
     *
     * @param interfaceCode 接口编码
     * @param objectApiName 对象ApiName
     * @return name="RollbackHandler_${interfaceCode}_${objectApiName}"的回滚业务处理器
     */
    RollbackHandler getRollbackHandler(String interfaceCode, String objectApiName);
}
