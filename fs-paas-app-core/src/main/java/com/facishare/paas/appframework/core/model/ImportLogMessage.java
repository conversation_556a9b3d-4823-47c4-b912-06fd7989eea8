package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;

@Slf4j
public class ImportLogMessage {

    public static final String VALID_UNIQUENESS_RULE = "uniquenessRule";
    public static final String VALID_DUPLICATE_SEARCH = "duplicateSearch";
    public static final String CUSTOM_VALIDATE = "customValidate";
    public static final String CALL_VALIDATION_FUNCTION = "validationFunction";
    public static final String CONVERT_FIELDS = "convertFields";
    public static final String CUSTOM_INIT = "customInit";

    private static final String APP_NAME = ConfigHelper.getProcessInfo().getName();
    private static final String SERVER_IP = ConfigHelper.getProcessInfo().getIp();
    private static final String PROFILE = ConfigHelper.getProcessInfo().getProfile();

    private final String tenantId;
    private final String userId;
    private final String describeApiName;

    private final String importType;
    private final String matchingType;
    private final String jobId;
    private final String objectCode;
    private final Boolean preProcessingStatus;
    private final Boolean unionStatus;
    private final long totalCost;

    private final Map<String, Long> costMap;

    private ImportLogMessage(String tenantId, String userId, String describeApiName, String importType, String matchingType,
                             String jobId, String objectCode, Boolean preProcessingStatus, Boolean unionStatus, long totalCost, Map<String, Long> costMap) {
        this.tenantId = tenantId;
        this.userId = userId;
        this.describeApiName = describeApiName;
        this.importType = importType;
        this.matchingType = matchingType;
        this.jobId = jobId;
        this.objectCode = objectCode;
        this.preProcessingStatus = preProcessingStatus;
        this.unionStatus = unionStatus;
        this.totalCost = totalCost;
        this.costMap = Maps.newHashMap(costMap);
    }

    public static ImportMessageBuilder createAndStart(User user, BaseImportAction.Arg arg, String actionCode) {
        return new ImportMessageBuilder(user.getTenantId(), user.getUserId(), arg.getApiName(),
                actionCode, getMatchingType(arg.getMatchingType()), arg.getJobId(),
                arg.getObjectCode(), arg.getImportPreProcessing(), CollectionUtils.notEmpty(arg.getUnionApiNameList()),
                System.currentTimeMillis());
    }

    /**
     * 按ID匹配    1
     * 按主属性匹配   2
     * 按唯一性规则匹配 3
     * 按特定字段匹配  4
     */
    private static String getMatchingType(Integer matchingType) {
        if (Objects.equals(BaseImportDataAction.MATCHING_TYPE_ID, matchingType)) {
            return "id";
        }
        if (Objects.equals(BaseImportDataAction.MATCHING_TYPE_NAME, matchingType)) {
            return "name";
        }
        if (Objects.equals(BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE, matchingType)) {
            return "unique_rule";
        }
        if (Objects.equals(BaseImportDataAction.MATCHING_TYPE_SPECIFIED_FIELD, matchingType)) {
            return "specified_field";
        }
        return null;
    }

    /**
     * 新建导入 0
     * 更新导入 1
     */
    private static String getImportType(Integer importType) {
        if (Objects.equals(BaseImportTemplateAction.IMPORT_TYPE_ADD, importType)) {
            return "insertImport";
        }
        if (Objects.equals(BaseImportTemplateAction.IMPORT_TYPE_EDIT, importType)) {
            return "updateImport";
        }
        return null;
    }

    private Long getCostByKey(String key) {
        return costMap.get(key);
    }

    public void send() {
        try {
            AuditLogDTO dto = AuditLogDTO.builder()
                    .appName(APP_NAME)
                    .serverIp(SERVER_IP)
                    .profile(PROFILE)
                    .tenantId(tenantId)
                    .objectApiNames(describeApiName)
                    .eventId(jobId)
                    .cost(totalCost)
                    .userId(userId)
                    .action(importType)
                    .status(matchingType)
                    .extra(objectCode)
                    .traceId(TraceContext.get().getTraceId())
                    .isPreProcessing(preProcessingStatus)
                    .isUnion(unionStatus)
                    .step1Cost(getCostByKey(CUSTOM_INIT))
                    .step2Cost(getCostByKey(CONVERT_FIELDS))
                    .step3Cost(getCostByKey(CUSTOM_VALIDATE))
                    .step4Cost(getCostByKey(CALL_VALIDATION_FUNCTION))
                    .step5Cost(getCostByKey(VALID_UNIQUENESS_RULE))
                    .step6Cost(getCostByKey(VALID_DUPLICATE_SEARCH))
                    .build();
            BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
        } catch (Exception e) {
            log.info("sendPaaSCalculateLog ei:{},describe:{},jobId:{},error:", tenantId, describeApiName, jobId, e);
        }
    }

    @Override
    public String toString() {
        StringJoiner joiner = new StringJoiner(", ");
        joiner.add("tenantId: " + tenantId);
        joiner.add("userId: " + userId);
        joiner.add("describeApiName: " + describeApiName);
        joiner.add("importType: " + importType);
        joiner.add("matchingType: " + matchingType);
        joiner.add("jobId: " + jobId);
        joiner.add("matchingType: " + matchingType);
        joiner.add("objectCode: " + objectCode);
        joiner.add("preProcessingStatus: " + preProcessingStatus);
        joiner.add("unionStatus: " + unionStatus);
        joiner.add("totalCost: " + totalCost + " ms");
        costMap.forEach((key, value) -> joiner.add(key + ": " + value + " ms"));
        return joiner.toString();
    }

    public static class ImportMessageBuilder {
        private final Map<String, Long> START_MAP = Maps.newHashMap();
        private final Map<String, Long> COST_MAP = Maps.newHashMap();

        private final Long startTime;

        private final String tenantId;
        private final String userId;
        private final String describeApiName;

        private final String importType;
        private final String matchingType;
        private final String jobId;
        private final String objectCode;
        private final Boolean preProcessingStatus;
        private final Boolean unionStatus;

        private ImportMessageBuilder(String tenantId, String userId, String describeApiName, String importType,
                                     String matchingType, String jobId, String objectCode, Boolean preProcessingStatus,
                                     Boolean unionStatus, Long startTime) {
            this.tenantId = tenantId;
            this.userId = userId;
            this.describeApiName = describeApiName;
            this.importType = importType;
            this.matchingType = matchingType;
            this.jobId = jobId;
            this.objectCode = objectCode;
            this.preProcessingStatus = preProcessingStatus;
            this.unionStatus = unionStatus;
            this.startTime = startTime;
        }

        public ImportMessageBuilder start(String key) {
            START_MAP.putIfAbsent(key, System.currentTimeMillis());
            return this;
        }

        public ImportMessageBuilder end(String key) {
            Long startTime = START_MAP.get(key);
            if (Objects.nonNull(startTime)) {
                COST_MAP.putIfAbsent(key, System.currentTimeMillis() - startTime);
            }
            return this;
        }

        public ImportLogMessage build() {
            long currentTimeMillis = System.currentTimeMillis();
            return new ImportLogMessage(tenantId, userId, describeApiName, importType, matchingType, jobId, objectCode,
                    preProcessingStatus, unionStatus, currentTimeMillis - startTime, COST_MAP);
        }

    }
}
