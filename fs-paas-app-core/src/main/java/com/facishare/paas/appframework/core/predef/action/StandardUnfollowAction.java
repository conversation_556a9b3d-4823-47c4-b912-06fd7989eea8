package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.follow.FollowLogicService;
import com.facishare.paas.appframework.metadata.mongo.follow.bean.ObjectFollowInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

public class StandardUnfollowAction extends AbstractStandardAction<StandardUnfollowAction.Arg, StandardUnfollowAction.Result> {

    protected IObjectData objectData;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.UNFOLLOW.getButtonApiName();
    }

    @Override
    protected void init() {
        super.init();
        objectData = dataList.stream().filter(x -> Objects.equals(x.getId(), arg.getObjectDataId())).findFirst().orElse(null);
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }


    @Override
    protected Result doAct(Arg arg) {
        if (StringUtils.isBlank(arg.getObjectDataId())) {
            return new Result();
        }
        FollowLogicService followLogicService = serviceFacade.getBean(FollowLogicService.class);
        String apiName = objectDescribe.getApiName();
        followLogicService.deleteOneFollowDataByUser(actionContext.getUser(), ObjectFollowInfo.OBJECT_BIZ, apiName, arg.getObjectDataId());
        return new Result();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Arg {
        private String objectDataId;

        public static StandardUnfollowAction.Arg of(String id) {
            return new StandardUnfollowAction.Arg(id);
        }
    }

    @Data
    public static class Result {

    }
}
