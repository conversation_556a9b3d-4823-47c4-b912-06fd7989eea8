package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.metadata.config.IUdefButtonConfig;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * create by z<PERSON><PERSON> on 2019/09/18
 */
public class ButtonConfigDocument extends DocumentBaseEntity {
    public ButtonConfigDocument() {
    }

    public ButtonConfigDocument(Map<String, Object> data) {
        super(data);
    }

    public static ButtonConfigDocument of(IUdefButtonConfig buttonConfig) {
        if (Objects.isNull(buttonConfig)) {
            return null;
        }
        return new ButtonConfigDocument(buttonConfig.toMap());
    }

    public static List<ButtonConfigDocument> ofList(List<IUdefButtonConfig> buttonConfigs) {
        if (CollectionUtils.empty(buttonConfigs)) {
            return Collections.emptyList();
        }
        return buttonConfigs.stream().map(ButtonConfigDocument::of).collect(Collectors.toList());
    }
}
