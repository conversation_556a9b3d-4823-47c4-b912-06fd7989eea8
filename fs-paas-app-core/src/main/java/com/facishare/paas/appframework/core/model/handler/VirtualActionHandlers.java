package com.facishare.paas.appframework.core.model.handler;

/**
 * Created by zhouwr on 2023/3/16.
 */
public interface VirtualActionHandlers {
    String PROCESS_ARG = "defaultProcessArgActionHandler";
    String PLUGIN_INIT = "defaultPluginInitActionHandler";
    String PLUGIN_BEFORE = "defaultPluginBeforeActionHandler";
    String DOMAIN_PLUGIN_BEFORE = "defaultDomainPluginBeforeActionHandler";
    String PRE_ACTION = "defaultPreActionHandler";
    String DOMAIN_PLUGIN_PRE_ACT = "defaultDomainPluginPreActHandler";
    String DOMAIN_PLUGIN_POST_ACT = "defaultDomainPluginPostActHandler";
    String PLUGIN_AFTER = "defaultPluginAfterActionHandler";
    String DOMAIN_PLUGIN_AFTER = "defaultDomainPluginAfterActionHandler";
    String POST_ACTION = "defaultPostActionHandler";
    String PROCESS_RESULT = "defaultProcessResultActionHandler";
    String PLUGIN_FINALLY_DO = "defaultPluginFinallyActionHandler";
    String DOMAIN_PLUGIN_FINALLY_DO = "defaultDomainPluginFinallyActionHandler";
}
