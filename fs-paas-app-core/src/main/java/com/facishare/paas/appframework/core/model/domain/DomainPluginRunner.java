package com.facishare.paas.appframework.core.model.domain;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.model.RequestType;
import com.facishare.paas.appframework.core.model.ValidatorInfo;
import com.facishare.paas.appframework.core.model.ValidatorType;
import com.facishare.paas.appframework.metadata.domain.SimpleDomainPluginDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2022/1/24.
 */
@Slf4j
@Builder
public class DomainPluginRunner {
    private DomainPluginManager domainPluginManager;
    private List<SimpleDomainPluginDescribe> pluginDescribes;
    private String method;
    private RequestType requestType;
    private String requestCode;
    private List<ValidatorInfo> skippedValidatorList;
    private DomainPluginFunction.BuildArgFunction buildArgFunction;
    private DomainPluginFunction.RunFunction runFunction;
    private DomainPluginFunction.ProcessResultFunction processResultFunction;

    public void run() {
        if (CollectionUtils.empty(pluginDescribes)) {
            return;
        }
        pluginDescribes.forEach(pluginDescribe -> {
            StopWatch stopWatch = StopWatch.create("runDomainPlugin_" + pluginDescribe.getApiName() + "_" + method);
            try {
                //根据上次校验结果过滤plugin
                if (skipByLastValidationResult(pluginDescribe)) {
                    log.warn("skip DomainPlugin by last validation result:{},method:{}", pluginDescribe.getApiName(), method);
                    return;
                }
                //查找插件实现类
                DomainPlugin domainPlugin = getDomainPlugin(pluginDescribe.getApiName());
                stopWatch.lap("getPlugin");
                if (Objects.isNull(domainPlugin)) {
                    log.warn("DomainPlugin implementation not found:{}", pluginDescribe.getApiName());
                    return;
                }
                long startTime = System.currentTimeMillis();
                //构造插件执行参数
                DomainPlugin.Arg pluginArg = buildArgFunction.apply(method, pluginDescribe.getRecordTypeList());
                pluginArg.setPluginDescribe(pluginDescribe);
                pluginArg.setSkippedStepKeys(getSkippedStepKeys(pluginDescribe));
                stopWatch.lap("buildArg");
                //执行插件
                DomainPlugin.Result pluginResult = runFunction.apply(domainPlugin, pluginArg);
                stopWatch.lap("runPlugin");
                //处理插件返回结果
                if (Objects.nonNull(processResultFunction)) {
                    processResultFunction.apply(method, pluginArg, pluginResult);
                    stopWatch.lap("processResult");
                }
                String ret = Objects.isNull(pluginResult) ? null : JacksonUtils.toJson(pluginResult);
                log.info("run DomainPlugin success,apiName:{},method:{},cost:{},result:{}", pluginDescribe.getApiName(), method,
                        System.currentTimeMillis() - startTime, ret);
            } catch (Exception e) {
                if (e instanceof AppBusinessException) {
                    log.warn("run DomainPlugin failed,apiName:{},method:{} ", pluginDescribe.getApiName(), method, e);
                } else {
                    log.error("run DomainPlugin error,apiName:{},method:{} ", pluginDescribe.getApiName(), method, e);
                }
                if (!Objects.equals(method, ActionDomainPlugin.FINALLY_DO)) {
                    throw e;
                }
            } finally {
                stopWatch.logSlow(100);
            }
        });
    }

    private boolean skipByLastValidationResult(SimpleDomainPluginDescribe pluginDescribe) {
        if (RequestType.Action != requestType) {
            return false;
        }
        if (CollectionUtils.empty(skippedValidatorList)) {
            return false;
        }
        ValidatorType validatorType = ValidatorType.getDomainPluginValidatorTypeByMethod(method);
        if (Objects.isNull(validatorType)) {
            return false;
        }
        return skippedValidatorList.stream()
                .filter(x -> validatorType.getCode().equals(x.getValidatorType()))
                .filter(ValidatorInfo::isSkipWhenForceSubmit)
                .anyMatch(x -> Objects.equals(x.getValidatorKey(), pluginDescribe.getApiName()));
    }

    private List<String> getSkippedStepKeys(SimpleDomainPluginDescribe pluginDescribe) {
        if (RequestType.Action != requestType) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.empty(skippedValidatorList)) {
            return Lists.newArrayList();
        }
        ValidatorType validatorType = ValidatorType.getDomainPluginValidatorTypeByMethod(method);
        if (Objects.isNull(validatorType)) {
            return Lists.newArrayList();
        }
        return skippedValidatorList.stream()
                .filter(x -> validatorType.getCode().equals(x.getValidatorType()))
                .filter(x -> Objects.equals(x.getValidatorKey(), pluginDescribe.getApiName()))
                .map(ValidatorInfo::getStepKey)
                .filter(stepKey -> !Strings.isNullOrEmpty(stepKey))
                .collect(Collectors.toList());
    }

    private DomainPlugin getDomainPlugin(String pluginApiName) {
        if (RequestType.Action == requestType) {
            return domainPluginManager.getActionPlugin(pluginApiName, requestCode);
        }
        return domainPluginManager.getControllerPlugin(pluginApiName, requestCode);
    }
}
