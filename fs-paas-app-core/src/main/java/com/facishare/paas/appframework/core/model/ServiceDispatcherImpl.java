package com.facishare.paas.appframework.core.model;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by liyiguang on 2017/7/30.
 */
@Component
public class ServiceDispatcherImpl implements ServiceDispatcher {

    @Autowired
    private ServiceRegistry serviceRegistry;
    @Autowired
    private SerializerManager serializerManager;

    /**
     * @param context
     * @param payload
     * @return
     */
    @Override
    public Object service(ServiceContext context, String payload) {

        String fullServicePath = context.getServiceName() + "/" + context.getServiceMethod();

        ServiceInvoker invoker;

        if (context.isAppModuleRequest()) {
            invoker = serviceRegistry.getServiceMethodInvoker(context.getAppId(), fullServicePath);
        } else {
            invoker = serviceRegistry.getServiceMethodInvoker(fullServicePath);
        }

        Class<?> payloadParameterType = invoker.getParameterType();

        Object payloadParameter = null;
        if (payloadParameterType != null) {
            payloadParameter = serializerManager.getSerializer(context.getContentType()).decode(payloadParameterType, payload);
        }

        Class<?>[] parameterTypes = invoker.getParameterTypes();
        Object[] args = new Object[parameterTypes.length];
        for (int i = 0; i < parameterTypes.length; i++) {
            if (parameterTypes[i].equals(ServiceContext.class)) {
                args[i] = context;
            } else if (payloadParameter != null && parameterTypes[i].equals(payloadParameterType)) {
                args[i] = payloadParameter;
            }
        }

        return invoker.invoke(args);
    }
}
