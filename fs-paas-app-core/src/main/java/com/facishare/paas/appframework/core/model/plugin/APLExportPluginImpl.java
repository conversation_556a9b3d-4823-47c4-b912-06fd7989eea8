package com.facishare.paas.appframework.core.model.plugin;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.appframework.function.plugin.FunctionPluginService;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhaooju on 2022/11/30
 */
@Component
@PluginProvider(apiName = "APLExportPlugin")
public class APLExportPluginImpl implements APLExportPlugin {
    @Autowired
    private FunctionPluginService functionPluginService;

    @Override
    public Result before(PluginContext context, Arg arg) {
        FunctionPluginService.FunctionPluginContext<APLExportPlugin.Result> functionPluginContext = FunctionPluginService.FunctionPluginContext.<APLExportPlugin.Result>builder()
                .user(context.getUser())
                .argProcess(() -> buildArg(arg, context))
                .resultProcess(jsonString -> buildResult(arg, jsonString))
                .build();
        String functionApiName = getFunctionApiName(context, arg);
        return functionPluginService.executeFuncMethod(functionPluginContext, functionApiName, BEFORE);
    }

    @Override
    public Result doExport(PluginContext context, Arg arg) {
        FunctionPluginService.FunctionPluginContext<APLExportPlugin.Result> functionPluginContext = FunctionPluginService.FunctionPluginContext.<APLExportPlugin.Result>builder()
                .user(context.getUser())
                .argProcess(() -> buildArg(arg, context))
                .resultProcess(jsonString -> buildResult(arg, jsonString))
                .build();
        String functionApiName = getFunctionApiName(context, arg);
        return functionPluginService.executeFuncMethod(functionPluginContext, functionApiName, DO_EXPORT);
    }

    @Override
    public Result after(PluginContext context, Arg arg) {
        FunctionPluginService.FunctionPluginContext<APLExportPlugin.Result> functionPluginContext = FunctionPluginService.FunctionPluginContext.<APLExportPlugin.Result>builder()
                .user(context.getUser())
                .argProcess(() -> buildArg(arg, context))
                .resultProcess(jsonString -> buildResult(arg, jsonString))
                .build();
        String functionApiName = getFunctionApiName(context, arg);
        return functionPluginService.executeFuncMethod(functionPluginContext, functionApiName, AFTER);
    }

    private List<String> buildArg(Arg arg, PluginContext context) {
        List<String> result = Lists.newArrayList();
        arg.setCode(context.getModuleCode());
        arg.setType(context.getModuleType());
        String json = JacksonUtils.toJson(arg);
        if (!Strings.isNullOrEmpty(json)) {
            result.add(json);
        }
        return result;
    }

    private APLExportPlugin.Result buildResult(Arg arg, String jsonString) {
        JsonNode jsonNode = JacksonUtils.readTree(jsonString);
        if (Objects.isNull(jsonNode)) {
            return null;
        }
        StandardExportAction.Arg controllerArg = getObjectFromNode(jsonNode, "exportArg", StandardExportAction.Arg.class);
        List<Map> displayDataList = getObjectFromNode(jsonNode, "displayDataList", List.class);
        Boolean supportDoExport = getObjectFromNode(jsonNode, "supportDoExport", Boolean.class);
        return new APLExportPlugin.Result(controllerArg, displayDataList, supportDoExport);
    }

    private <T> T getObjectFromNode(JsonNode jsonNode, String fieldName, Class<T> type) {
        if (Objects.isNull(type)) {
            return null;
        }
        JsonNode controllerArgNode = jsonNode.get(fieldName);
        return JacksonUtils.convertValue(controllerArgNode, type);
    }
}
