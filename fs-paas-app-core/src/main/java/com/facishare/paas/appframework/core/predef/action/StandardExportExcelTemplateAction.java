package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.PrintTemplate;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ImportConfig;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.plugin.APLExportPlugin;
import com.facishare.paas.appframework.core.model.plugin.PluginContext;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.exception.ExportException;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.util.ExcelUtil;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.token.model.TokenInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * create by zhaoju on 2021/02/18
 */
public class StandardExportExcelTemplateAction extends AbstractExportAction<StandardExportExcelTemplateAction.Arg,
        StandardExportExcelTemplateAction.Result> {

    /**
     * 每次查询数据最大允许数量
     */
    public static final int DATA_BATCH_SIZE = 100;

    protected final ExcelUtil excelUtil;
    public final int EXPORT_PRINT_TEMPLATE_EXCEL_THROTTLE;
    //    public final int EXPORT_PRINT_TEMPLATE_EXCEL_THROTTLE_VIP;
    public final int exportFileExpireDay;

    public StandardExportExcelTemplateAction() {
        EXPORT_PRINT_TEMPLATE_EXCEL_THROTTLE = ImportConfig.getExportPrintTemplateExcelThrottle();
//        EXPORT_PRINT_TEMPLATE_EXCEL_THROTTLE_VIP = ImportConfig.getExportPrintTemplateExcelThrottleVip();
        exportFileExpireDay = ImportConfig.getExportFileExpireDay();
        excelUtil = SpringUtil.getContext().getBean(ExcelUtil.class);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.ExportExcelTemplate.getFunPrivilegeCodes();
    }

    @Override
    protected int getExportRowsThrottle() {
        return EXPORT_PRINT_TEMPLATE_EXCEL_THROTTLE;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }


    @Override
    protected String getToken(Arg arg) {
        return arg.getToken();
    }

    @Override
    protected void customInit() {

    }

    @Override
    protected boolean hasToken() {
        return StringUtils.isNotBlank(arg.getToken());
    }

    @Override
    protected String getSearchTemplateId() {
        return arg.getSearchTemplateId();
    }

    @Override
    protected String getSearchQueryInfo() {
        return arg.getSearchQueryInfo();
    }

    @Override
    protected List<String> getDataIdList() {
        return arg.getDataIdList();
    }

    @Override
    protected String getSearchTemplateType() {
        return arg.getSearchTemplateType();
    }

    @Override
    protected boolean isIgnoreSceneFilter() {
        return false;
    }

    @Override
    protected boolean isIgnoreSceneRecordType() {
        return false;
    }

    @Override
    protected boolean isNoExportRelevantTeam() {
        return false;
    }

    @Override
    protected void doExport() {
        StopWatch stopWatch = StopWatch.create("doExport");
        int totalPage = SearchTemplateQueryExt.calculateTotalPage(getExportRowsThrottle(), getDataBatchSize());
        serviceFacade.queryDataAndHandle(actionContext.getUser(), searchQuery, objectDescribe, getDataBatchSize(),
                totalPage, isNoExportRelevantTeam(), queryResult -> export(stopWatch, queryResult));

    }

    private void export(StopWatch stopWatch, QueryResult<IObjectData> queryResult) {
        try {
            List<String> dataIdList = queryResult.getData().stream()
                    .map(data -> (String) data.get(IObjectData.ID)).collect(Collectors.toList());
            List<String> failedDataIds = Lists.newArrayList();
            User user = actionContext.getUser();
            List<PrintTemplate.Result> printTemplateList = Lists.newArrayList();
            for (int i = 0; i < dataIdList.size(); i++) {
                PrintTemplate.Arg printTemplateArg = PrintTemplate.Arg.builder()
                        .objectAPIName(actionContext.getObjectApiName())
                        .objectId(dataIdList.get(i))
                        .templateId(arg.getPrintTemplateId())
                        .orientation("Landscape")
                        .build();
                try {
                    PrintTemplate.Result result = infraServiceFacade.print(user, printTemplateArg);
                    if (Objects.nonNull(result) && StringUtils.isNotEmpty(result.getPath())) {
                        printTemplateList.add(result);
                    } else {
                        failedDataIds.add(dataIdList.get(i));
                    }
                    if ((i + 1) % 10 == 0) {
                        TokenInfo tokenInfo = TokenInfo.builder().id(token).progress(i).build();
                        tokenService.update(tokenInfo, tokenExpireSeconds);
                    }
                } catch (Exception e) {
                    log.warn("print data failed!tenantId:{},dataId:{}", actionContext.getTenantId(), dataIdList.get(i), e);
                    failedDataIds.add(dataIdList.get(i));
                }
            }
            stopWatch.lap("print");
            String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
            List<NDownloadFile.Result> downloadFileList = Lists.newArrayList();
            if (CollectionUtils.notEmpty(printTemplateList)) {
                for (PrintTemplate.Result result : printTemplateList) {
                    try {
                        NDownloadFile.Result downloadFile = excelUtil.downloadFile(result.getPath(), ea, user.getUserId());
                        downloadFileList.add(downloadFile);
                    } catch (Exception e) {
                        log.warn("export data failed!tenantId:{},downloadFile path:{}", actionContext.getTenantId(), result.getPath(), e);
                    }
                }
            }
            stopWatch.lap("downloadFile");
            if (CollectionUtils.notEmpty(downloadFileList)) {
                List<NDownloadFile.Result> resultList = downloadFileList.stream()
                        .filter(file -> Objects.nonNull(file.getData()))
                        .collect(Collectors.toList());
                NUploadFileDirect.Result result = null;
                try {
                    List<INameCache> nameCacheList = null;
                    if (CollectionUtils.notEmpty(failedDataIds)) {
                        nameCacheList = serviceFacade.findRecordName(ActionContextExt.of(user).getContext(), arg.describeApiName, failedDataIds);
                    }
                    SXSSFWorkbook workbook = excelUtil.mergeSheet(resultList, nameCacheList, objectDescribe.getApiName(), actionContext.getTenantId());
                    if (Objects.nonNull(workbook)) {
                        result = excelUtil.exportDataDirect(workbook, actionContext, exportFileExpireDay);
                    }
                } catch (IOException e) {
                    log.warn("mergeSheet failed!tenantId:{},resultList:{}", actionContext.getTenantId(), resultList.size(), e);
                }
                stopWatch.lap("mergeSheet");
                if (Objects.isNull(result) || Strings.isNullOrEmpty(result.getFinalNPath())) {
                    throw new ExportException(I18N.text(I18NKey.UPLOAD_EXCEL_FAIL));
                }
                TokenInfo tokenInfo = TokenInfo.buildSuccess(token, result.getFinalNPath() + "|" + result.getFileSize() + "|" + ImportConfig.getFileExpiredTimeWithNow());
                tokenService.complete(tokenInfo, tokenExpireSeconds);
            } else {
                throw new ExportException(I18N.text(I18NKey.UPLOAD_EXCEL_FAIL));
            }
            List<IObjectData> successObjectDatas = queryResult.getData().stream().filter(x -> !failedDataIds.contains(x.getId())).collect(Collectors.toList());
            pluginDoExport(arg, objectDescribe, successObjectDatas);
        } finally {
            stopWatch.logSlow(3000);
        }
    }

    @Override
    protected void consumerDataList(List<IObjectData> dataList) {

    }

    protected final void pluginDoExport(StandardExportExcelTemplateAction.Arg arg, IObjectDescribe describe, List<IObjectData> originalDataList) {
        runPlugin(describe.getApiName(), APLExportPlugin.DO_EXPORT, false, (plugin, pluginArg) -> {
            APLExportPlugin.Arg exportPluginArg = (APLExportPlugin.Arg) pluginArg;
            exportPluginArg.setOriginalDataList(originalDataList);
            return ((APLExportPlugin) plugin).doExport(getContext(describe), exportPluginArg);
        });
    }

    private PluginContext getContext(IObjectDescribe describe) {
        return PluginContext.fromActionContext(actionContext, describe.getApiName());
    }

    @Override
    protected APLExportPlugin.Arg buildAPLExportPluginArg(String methodName) {
        return new APLExportPlugin.Arg(objectDescribe.getApiName());
    }

    @Override
    protected int getDataBatchSize() {
        return DATA_BATCH_SIZE;
    }

    @Override
    protected Result generateResult(String token) {
        TokenInfo tokenInfo = tokenService.query(token);
        if (tokenInfo == null) {
            throw new MetaDataException("token not exist!");
        }
        int num = Optional.ofNullable(tokenInfo.getProgress()).orElse(0);
        if (tokenInfo.isOngoing()) {
            return Result.builder().ext("xlsx").token(token).path("").file_name("").total_count(totalCount)
                    .currentCount(num).build();
        }

        if (tokenInfo.isSuccess()) {
            if (null == objectDescribe) {
                objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName());
            }
            String pathAndSize = tokenInfo.getResult();
            String[] arr = pathAndSize.split("\\|");
            String fileName = getFileName();
            Long fileExpiredTime = null;
            if (arr.length > 2) {
                fileExpiredTime = Long.valueOf(arr[2]);
            }
            return Result.builder().ext("xlsx").token(token).path(arr[0])
                    .size(Long.parseLong(arr[1]))
                    .file_name(fileName)
                    .fileExpiredTime(fileExpiredTime)
                    .total_count(totalCount)
                    .currentCount(num).build();
        }

        if (tokenInfo.isError()) {
            String errorMsg = tokenInfo.getMessage();
            if (StringUtils.isNotBlank(errorMsg) && errorMsg.contains(ERROR_SPLIT)) {
                String message = StringUtils.substringAfter(errorMsg, ERROR_SPLIT);
                throw new MetaDataBusinessException(message);
            }
            throw new MetaDataException("export data failed! " + errorMsg);
        }
        throw new MetaDataException("export data failed!");
    }

    @Data
    public static class Arg {
        @JSONField(name = "object_describe_api_name")
        @JsonProperty(value = "object_describe_api_name")
        @SerializedName(value = "object_describe_api_name")
        String describeApiName;

        @JSONField(name = "dataIdList")
        @JsonProperty(value = "dataIdList")
        @SerializedName(value = "dataIdList")
        List<String> dataIdList;

        @JSONField(name = "search_template_id")
        @JsonProperty(value = "search_template_id")
        @SerializedName(value = "search_template_id")
        String searchTemplateId;

        @JSONField(name = "search_query_info")
        @JsonProperty(value = "search_query_info")
        @SerializedName(value = "search_query_info")
        String searchQueryInfo;

        @JsonProperty(value = "search_template_type")
        private String searchTemplateType;

        @JsonProperty(value = "print_template_id")
        private String printTemplateId;


        /**
         * 任务 token 用于异步导出
         */
        private String token;

        private String jobId;

    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {

        @JSONField(name = "M1")
        String ext;

        @JSONField(name = "M2")
        String file_name;

        @JSONField(name = "M3")
        String path;

        @JSONField(name = "M4")
        String token;

        long size;

        @JSONField(name = "M5")
        int total_count;

        int currentCount;

        Long fileExpiredTime;

    }
}
