package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ServiceInvokeException;
import com.facishare.paas.appframework.core.exception.ServiceNotFoundException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.foundation.boot.exception.BadRequestException;
import com.facishare.paas.metadata.exception.MetadataValidateException;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 服务注册实现类
 * <p>
 * Created by liyiguang on 2017/7/30.
 */
@Slf4j
@Component
public class ServiceRegistryImpl implements ServiceRegistry {

    /**
     * 默认
     */
    private Map<String, ServiceInvoker> services = Maps.newConcurrentMap();

    /**
     * 应用相关服务
     */
    private Map<String, Map<String, ServiceInvoker>> appService = Maps.newConcurrentMap();


    @Override
    public ServiceInvoker getServiceMethodInvoker(String serviceFullPath) {
        ServiceInvoker invoker = services.get(serviceFullPath);
        if (!Objects.isNull(invoker)) {
            return invoker;
        }

        throw new ServiceNotFoundException(SystemErrorCode.SERVICE_NOT_FOUND_ERROR);
    }

    @Override
    public ServiceInvoker getServiceMethodInvoker(String moduleName, String serviceFullPath) {
        //appId 映射
        for (Map.Entry<String, String> entry : AppIdMapping.appIdMapping.entrySet()) {
            if (entry.getValue().equals(moduleName)) {
                moduleName = entry.getKey();
            }
        }

        Map<String, ServiceInvoker> services = appService.get(moduleName);
        if (services != null) {
            ServiceInvoker invoker = services.get(serviceFullPath);
            if (!Objects.isNull(invoker)) {
                return invoker;
            }
        }
        return getServiceMethodInvoker(serviceFullPath);
    }

    @Override
    public void register(Object bean) {
        Class<?> serviceClass = AopUtils.getTargetClass(bean);
        List<Class<?>> serviceInterfaces = getServiceInterface(serviceClass);
        if (serviceInterfaces.isEmpty() && serviceClass.getAnnotation(ServiceModule.class) == null) {
            return;
        }

        if (!serviceInterfaces.isEmpty()) {
            for (Class<?> interfaceClass : serviceInterfaces) {
                //Service register by interface
                ServiceModule serviceModule = interfaceClass.getAnnotation(ServiceModule.class);
                String serviceName = serviceModule.value();
                String moduleName = serviceModule.name();
                if (Strings.isNullOrEmpty(serviceName)) {
                    log.warn("service name is null or empty,with interface {}", interfaceClass.getCanonicalName());
                    continue;
                }

                if (Strings.isNullOrEmpty(moduleName)) {
                    registerServiceInvoker(bean, interfaceClass, serviceName);
                } else {
                    registerModuleServiceInvoker(bean, interfaceClass, serviceName, moduleName);
                }
            }
        } else {
            //Service register by service class
            ServiceModule serviceModule = serviceClass.getAnnotation(ServiceModule.class);
            String serviceName = serviceModule.value();
            String moduleName = serviceModule.name();
            if (Strings.isNullOrEmpty(serviceName)) {
                log.warn("service name is null or empty,with class {}", serviceClass.getCanonicalName());
                return;
            }

            if (Strings.isNullOrEmpty(moduleName)) {
                registerServiceInvoker(bean, serviceClass, serviceName);
            } else {
                registerModuleServiceInvoker(bean, serviceClass, serviceName, moduleName);
            }
        }
    }

    private List<Class<?>> getServiceInterface(Class clazz) {
        Class<?>[] interfaces = clazz.getInterfaces();
        List<Class<?>> ret = Lists.newArrayList();
        for (Class<?> i : interfaces) {
            if (i.getAnnotation(ServiceModule.class) != null) {
                ret.add(i);
            }
        }
        return ret;
    }

    private void registerModuleServiceInvoker(Object bean, Class<?> clazz, String serviceName, String moduleName) {
        Method[] methods = clazz.getDeclaredMethods();
        Map<String, ServiceInvoker> services = appService.computeIfAbsent(moduleName, (key) -> Maps.newConcurrentMap());
        registerInvoker(bean, clazz, serviceName, methods, services);
    }

    private void registerInvoker(Object bean, Class<?> clazz, String serviceName, Method[] methods, Map<String, ServiceInvoker> services) {
        for (Method m : methods) {
            ServiceMethod serviceMethod = m.getAnnotation(ServiceMethod.class);
            if (serviceMethod == null || Strings.isNullOrEmpty(serviceMethod.value())) {
                log.debug("Skip method:{} of service:{}", m.getName(), clazz.getCanonicalName());
                continue;
            }

            Class<?>[] parameterTypes = m.getParameterTypes();
            DefaultServiceInvoker invoker = new DefaultServiceInvoker(bean, clazz, parameterTypes, m);

            String serviceFullPath = serviceName + "/" + serviceMethod.value();

            services.put(serviceFullPath, invoker);
        }
    }

    private void registerServiceInvoker(Object bean, Class<?> clazz, String serviceName) {
        Method[] methods = clazz.getDeclaredMethods();
        registerInvoker(bean, clazz, serviceName, methods, services);
    }

    @AllArgsConstructor
    @Data
    private static class DefaultServiceInvoker implements ServiceInvoker {
        private Object target;
        private Class<?> targetClass;
        private Class<?>[] parameterTypes;
        private Method method;

        @Override
        public Class<?> getParameterType() {
            if (parameterTypes == null) {
                return null;
            }
            /**
             * 返回第一个不是ServiceContext类型的参数类型
             */
            for (Class<?> clazz : parameterTypes) {
                if (!clazz.equals(ServiceContext.class)) {
                    return clazz;
                }
            }
            return null;
        }

        @Override
        public Object invoke(Object... args) {
            try {
                return method.invoke(target, args);
            } catch (IllegalAccessException e) {
                throw new ServiceInvokeException(SystemErrorCode.INVOKE_ERROR, e);
            } catch (InvocationTargetException e) {
                Throwable target = e.getTargetException();
                if (target instanceof AppBusinessException) {
                    throw (AppBusinessException) target;
                }
                if (target instanceof MetadataValidateException) {
                    throw (MetadataValidateException) target;
                }
                if (target instanceof BadRequestException) {
                    throw (BadRequestException) target;
                }
                throw new ServiceInvokeException(SystemErrorCode.INVOKE_ERROR, e.getTargetException());
            }
        }

        @Override
        public Class<?>[] getParameterTypes() {
            if (parameterTypes != null) {
                return parameterTypes;
            }
            return new Class[0];
        }
    }
}
