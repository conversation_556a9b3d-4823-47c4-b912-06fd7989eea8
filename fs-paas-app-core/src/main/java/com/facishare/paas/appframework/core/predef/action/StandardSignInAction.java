package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.SignInException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.SignInFieldDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.SignIn;
import com.facishare.paas.metadata.impl.describe.SignInFieldDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 签到
 */
public class StandardSignInAction extends BaseObjectSignAction {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.SignIn.getFunPrivilegeCodes();
    }

    @Override
    public String getCurrentLocation(IObjectData data, SignInFieldDescribe signInFieldDescribe) {
        return data.get(signInFieldDescribe.getSignInLocationFieldApiName(), String.class);
    }

    @Override
    protected List<String> process(SignInFieldDescribe signInFieldDescribe, IObjectData stored, IObjectData data) {
        List<String> fields = Lists.newArrayList();

        if (!Boolean.TRUE.equals(signInFieldDescribe.getIsEnableSignOut())) {
            //未启用签退
            stored.set(signInFieldDescribe.getVisitStatusFieldApiName(), SignIn.SIGN_STATUS_COMPLETE);
            fields.add(signInFieldDescribe.getVisitStatusFieldApiName());
        }

        String location = compensateAddress((String) data.get(signInFieldDescribe.getSignInLocationFieldApiName()));
        data.set(signInFieldDescribe.getSignInLocationFieldApiName(), location);

        if (StringUtils.isNotBlank(signInFieldDescribe.getQuoteField())) {
            //启用了目标地址和允差范围
            long distance = getDistance(signInFieldDescribe, data);
            if (distance > Long.parseLong(SignInFieldDescribeExt.of(signInFieldDescribe).getRadiusRange())) {
                throw new SignInException(I18N.text(I18NKey.BEYOND_SCOPE_SIGN_IN));
            }
        }

        stored.setVersion(data.getVersion());
        stored.set(signInFieldDescribe.getSignInTimeFieldApiName(), System.currentTimeMillis());
        stored.set(signInFieldDescribe.getSignInStatusFieldApiName(), SignIn.SIGN_STATUS_COMPLETE);
        stored.set(signInFieldDescribe.getSignInLocationFieldApiName(), data.get(signInFieldDescribe.getSignInLocationFieldApiName()));

        fields.add(signInFieldDescribe.getSignInTimeFieldApiName());
        fields.add(signInFieldDescribe.getSignInStatusFieldApiName());
        fields.add(signInFieldDescribe.getSignInLocationFieldApiName());

        handleNewSignInfo(data, stored, signInFieldDescribe, "sign_in", "sign_in_complete");

        return fields;
    }
}
