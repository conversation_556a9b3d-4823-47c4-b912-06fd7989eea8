package com.facishare.paas.appframework.core.predef.action;

import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.facade.FlowCompletedActionServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.FlowStartCallbackActionServiceFacade;
import com.facishare.paas.appframework.core.predef.handler.flowstartcallback.FlowStartCallbackHandler;
import com.facishare.paas.appframework.core.util.HandlerGrayConfig;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.appframework.metadata.handler.HandlerType;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.Attributes.TRIGGER_WORK_FLOW;
import static com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction.SUPPORT_HANDLER_TRIGGER_TYPES;

@Idempotent(serializer = Serializer.Type.java, waitTime = 15)
public class StandardFlowStartCallbackAction extends PreDefineAction<StandardFlowStartCallbackAction.Arg, StandardFlowStartCallbackAction.Result> {

    protected User user;
    protected ApprovalFlowTriggerType triggerType;
    protected ApprovalFlowStartResult triggerResult;
    protected IObjectData objectData;
    protected IObjectData oldObjectData;
    protected Map<String, Object> callbackData;
    protected Boolean triggerWorkFlow;
    private Map<String, Object> updateFieldMap;

    protected List<IObjectData> dataListToTriggerWorkFlow = Lists.newArrayList();
    protected Map<String, List<IObjectData>> detailObjectData = new HashMap<>();

    private final Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
    private final Map<String, IObjectDescribe> detailDescribeMap = Maps.newHashMap();
    private List<SimpleHandlerDescribe> triggerActionHandlerDescribes;

    private FlowStartCallbackActionServiceFacade flowStartCallbackActionServiceFacade;
    private FlowCompletedActionServiceFacade flowCompletedActionServiceFacade;

    @Override
    protected final List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected final List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected final boolean customSkipHandler() {
        return arg.isTriggerSynchronous() || !SUPPORT_HANDLER_TRIGGER_TYPES.contains(arg.triggerType());
    }

    @Override
    protected final Handler.Arg<StandardFlowStartCallbackAction.Arg> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        return FlowStartCallbackHandler.Arg.builder()
                .describeMap(describeMap)
                .data(ObjectDataDocument.of(objectData))
                .dbData(ObjectDataDocument.of(oldObjectData))
                .detailDataMap(ObjectDataDocument.ofMap(detailObjectData))
                .callbackData(callbackData)
                .updateFieldMap(updateFieldMap)
                .skipWorkFlow(!needTriggerWorkFlow())
                .triggerActionHandlerDescribes(triggerActionHandlerDescribes)
                .build();
    }

    @Override
    protected final void processHandlerResult(HandlerContext handlerContext, Handler.Arg<Arg> handlerArg, Handler.Result<Result> handlerResult) {
        if (handlerResult instanceof FlowStartCallbackHandler.Result) {
            FlowStartCallbackHandler.Result actionResult = (FlowStartCallbackHandler.Result) handlerResult;
            if (Objects.nonNull(actionResult.getData())) {
                ObjectDataExt.of(this.objectData).putAll(actionResult.getData());
                if (isUpdateTriggerType()) {
                    processMasterDataEdit();
                }
            }
            if (Objects.nonNull(actionResult.getDetailDataMap())) {
                if (Objects.isNull(detailObjectData)) {
                    detailObjectData = Maps.newHashMap();
                }
                actionResult.getDetailDataMap().forEach((k, v) -> {
                    if (detailDescribeMap.containsKey(k)) {
                        detailObjectData.put(k, ObjectDataDocument.ofDataList(v));
                    }
                });
            }
        }
    }

    private boolean isUpdateTriggerType() {
        return triggerType == ApprovalFlowTriggerType.UPDATE;
    }

    @Override
    protected void init() {
        super.init();
        flowStartCallbackActionServiceFacade = serviceFacade.getBean(FlowStartCallbackActionServiceFacade.class);
        flowCompletedActionServiceFacade = serviceFacade.getBean(FlowCompletedActionServiceFacade.class);
        user = actionContext.getUser();
        triggerType = arg.triggerType();
        triggerResult = arg.triggerResult();
        triggerWorkFlow = parseTriggerWorkFlow();
        callbackData = parseCallbackData();
        describeMap.put(objectDescribe.getApiName(), objectDescribe);
        initObjectData();
        initDetailData();
        initTriggerActionHandlers();
    }

    private void initObjectData() {
        oldObjectData = serviceFacade.findObjectData(actionContext.getTenantId(), arg.getDataId(), objectDescribe);
        objectData = ObjectDataExt.of(oldObjectData).copy();
        if (ApprovalFlowTriggerType.UPDATE == triggerType && CollectionUtils.notEmpty(callbackData)) {
            ObjectDataExt.of(objectData).putAll(callbackData);
        }
        processMasterDataEdit();
    }

    private void initDetailData() {
        if (!needDetailData()) {
            return;
        }
        if (serviceFacade.isMasterObject(user.getTenantId(), objectDescribe.getApiName())) {
            //查从对象
            List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribes(user.getTenantId(), objectDescribe.getApiName());
            detailDescribes.forEach(desc -> {
                describeMap.put(desc.getApiName(), desc);
                detailDescribeMap.put(desc.getApiName(), desc);
            });
            detailObjectData = serviceFacade.findDetailObjectDataList(detailDescribes, objectData, user);
        }
    }

    private void processMasterDataEdit() {
        if (ApprovalFlowTriggerType.UPDATE != triggerType) {
            return;
        }
        updateFieldMap = ObjectDataExt.of(oldObjectData).diff(objectData, objectDescribe);
        ObjectDataExt.of(updateFieldMap).removeFieldsNotSupportEdit(objectDescribe);
    }

    private void initTriggerActionHandlers() {
        if (!needTriggerActionHandler()) {
            return;
        }
        triggerActionHandlerDescribes = findTriggerActionTenantHandlerDescribes();
    }

    private boolean needDetailData() {
        if (arg.isTriggerSynchronous() || triggerResult != ApprovalFlowStartResult.APPROVAL_NOT_EXIST) {
            return false;
        }
        return ApprovalFlowTriggerType.UPDATE == triggerType || ApprovalFlowTriggerType.INVALID == triggerType;
    }

    private boolean needTriggerActionHandler() {
        if (arg.isTriggerSynchronous() || triggerResult != ApprovalFlowStartResult.APPROVAL_NOT_EXIST) {
            return false;
        }
        return ApprovalFlowTriggerType.UPDATE == triggerType || ApprovalFlowTriggerType.INVALID == triggerType;
    }

    private boolean needUpdateLifeStatus() {
        return triggerResult != ApprovalFlowStartResult.APPROVAL_NOT_EXIST
                || (triggerType != ApprovalFlowTriggerType.UPDATE && triggerType != ApprovalFlowTriggerType.INVALID);
    }

    @Override
    protected Result doAct(Arg arg) {
        if (!arg.isTriggerSynchronous()) {
            if (needUpdateLifeStatus()) {
                updateDataLifeStatus();
            }
            doActionWithTriggerResult();
            if (needTriggerWorkFlow()) {
                startWorkFlow();
            }
        } else if (triggerResult.isSuccess()) {
            updateDataLifeStatus();
        }

        return new Result(true);
    }

    private Boolean parseTriggerWorkFlow() {
        if (arg.getExtraData() == null) {
            return null;
        }
        return (Boolean) arg.getExtraData().get(ExtraDataKeys.TRIGGER_WORK_FLOW);
    }

    protected Map<String, Object> parseCallbackData() {
        return (Map) arg.getCallbackData();
    }

    protected void updateDataLifeStatus() {
        List<String> toUpdateFieldList = ObjectDataExt.of(objectData).modifyObjectDataWhenStartApprovalFlow(triggerType, triggerResult);
        if (CollectionUtils.notEmpty(toUpdateFieldList)) {
            serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(objectData), toUpdateFieldList);
            recordLifeStatusModifyLog();
            //同步更新从对象的生命状态
            updateDetailDataLifeStatus();
        }
    }

    private void updateDetailDataLifeStatus() {
        if (needDetailData()) {
            flowCompletedActionServiceFacade.updateDetailObjectDataLifeStatus(triggerType, actionContext.getUser(), objectData,
                    oldObjectData, detailObjectData, detailDescribeMap);
        } else if (ApprovalFlowTriggerType.CREATE == triggerType || ApprovalFlowTriggerType.UPDATE == triggerType) {
            serviceFacade.updateDetailObjectDataLifeStatusByMasterData(actionContext.getUser(), objectData, objectDescribe.getApiName());
        }
    }

    /**
     * FlowStartCallback 接口记修改记录
     */
    private void recordLifeStatusModifyLog() {
        if (Objects.equals(objectData.get(ObjectLifeStatus.LIFE_STATUS_API_NAME), oldObjectData.get(ObjectLifeStatus.LIFE_STATUS_API_NAME))) {
            return;
        }
        Map<String, Object> toUpdateMap = Maps.newHashMap();
        toUpdateMap.put(ObjectLifeStatus.LIFE_STATUS_API_NAME, objectData.get(ObjectLifeStatus.LIFE_STATUS_API_NAME));
        serviceFacade.log(User.systemUser(actionContext.getTenantId()), EventType.MODIFY, ActionType.Modify, objectDescribe, objectData, toUpdateMap, oldObjectData);
    }

    protected void doActionWithTriggerResult() {
        if (triggerResult != ApprovalFlowStartResult.APPROVAL_NOT_EXIST) {
            return;
        }
        switch (triggerType) {
            case CREATE:
                doCreateAction();
                break;
            case INVALID:
                doInvalidAction();
                break;
            case UPDATE:
                doEditAction();
                break;
            case CHANGE_OWNER:
                doChangeOwnerAction();
                break;
            case CHANGE_PARTNER:
                doChangePartnerAction();
                break;
            case DELETE_PARTNER:
                doDeletePartnerAction();
                break;
            case CHANGE_PARTNER_OWNER:
                doChangePartnerOwnerAction();
                break;
            default:
                doOtherAction();
                break;
        }
    }

    protected void doCreateAction() {
        Boolean triggerWorkflow = (Boolean) CollectionUtils.nullToEmpty(callbackData).get(ExtraDataKeys.TRIGGER_WORK_FLOW);
        if (BooleanUtils.isNotFalse(triggerWorkflow)) {
            dataListToTriggerWorkFlow.add(objectData);
        }
    }

    protected void doOtherAction() {

    }

    protected void doInvalidAction() {
        List<IObjectData> allDataList = Lists.newArrayList();
        executeTriggerActionTenantHandler(Lists.newArrayList(HandlerType.BEFORE, HandlerType.ACT));

        //作废主对象
        allDataList.addAll(serviceFacade.bulkInvalid(Lists.newArrayList(objectData), user));
        //2021/12/17 作废后执行按钮上的current动作
        RuntimeException ex = null;
        try {
            infraServiceFacade.doCurrentAction(callbackData, actionContext, objectDescribe, objectData, detailObjectData);
        } catch (RuntimeException e) {
            log.warn("doCurrentAction fail!", e);
            ex = e;
        }
        //作废从对象
        detailObjectData.forEach((x, y) -> allDataList.addAll(serviceFacade.bulkInvalid(y, user)));
        dealGdpr(Lists.newArrayList(objectData));
        if (Objects.nonNull(ex)) {
            throw ex;
        }
        serviceFacade.masterDetailLog(user, EventType.MODIFY, ActionType.Invalid, describeMap, allDataList);
        serviceFacade.sendActionMq(user, allDataList, ObjectAction.INVALID);

        dataListToTriggerWorkFlow = allDataList;

        if (HandlerGrayConfig.supportManagement(user.getTenantId(), StandardAction.Invalid.name(), objectDescribe.getApiName())) {
            executeTriggerActionTenantHandler(Lists.newArrayList(HandlerType.ACT, HandlerType.AFTER));
        } else {
            doPostAction(ObjectAction.INVALID, objectData, Maps.newHashMap(), detailObjectData);
        }
    }

    private void dealGdpr(List<IObjectData> dataList) {
        List<String> ids = dataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        ParallelUtils.createBackgroundTask()
                .submit(() -> infraServiceFacade.bulkInvalidGdprLegalBase(actionContext.getUser(), objectDescribe.getApiName(), ids))
                .run();
    }

    private List<SimpleHandlerDescribe> findTriggerActionTenantHandlerDescribes() {
        ObjectAction action = ObjectAction.of(triggerType.getActionCode());
        if (!HandlerGrayConfig.supportManagement(user.getTenantId(), action.getInterfaceCode(), objectDescribe.getApiName())) {
            return Collections.emptyList();
        }
        HandlerLogicService handlerLogicService = infraServiceFacade.getSpringBeanHolder().getHandlerLogicService();
        return handlerLogicService.findHandlerDescribeByInterfaceCode(actionContext.getTenantId(),
                actionContext.getObjectApiName(), action.getInterfaceCode());
    }

    private void executeTriggerActionTenantHandler(List<HandlerType> handlerTypes) {
        HandlerContext handlerContext = HandlerContext.builder()
                .requestContext(actionContext.getRequestContext())
                .interfaceCode(StandardAction.FlowStartCallback.name())
                .build();
        FlowStartCallbackHandler.Arg handlerArg = (FlowStartCallbackHandler.Arg) getHandlerArg(null);
        FlowStartCallbackHandler.Result tenantHandlerResult = flowStartCallbackActionServiceFacade
                .executeTriggerActionTenantHandler(handlerContext, handlerArg, handlerTypes);
        processHandlerResult(handlerContext, handlerArg, tenantHandlerResult);
    }

    protected final void doPostAction(ObjectAction action, IObjectData objectData, Map<String, Object> actionParam,
                                      Map<String, List<IObjectData>> detailObjectData) {
        flowCompletedActionServiceFacade.doPostAction(actionContext.getRequestContext(), objectDescribe.getApiName(),
                objectData, detailObjectData, callbackData, action, actionParam);
    }

    protected void doEditAction() {
        executeTriggerActionTenantHandler(Lists.newArrayList(HandlerType.BEFORE, HandlerType.ACT));

        ObjectDataExt.of(objectData).modifyObjectDataWhenStartApprovalFlow(triggerType, triggerResult);
        updateFieldMap = CollectionUtils.nullToEmpty(updateFieldMap);
        updateFieldMap.put(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectDataExt.of(objectData).getLifeStatusText());
        objectData = serviceFacade.updateWithMap(user, objectData, updateFieldMap);
        //同步更新从对象的生命状态
        updateDetailDataLifeStatus();
        serviceFacade.log(user, EventType.MODIFY, ActionType.Modify, objectDescribe, objectData, updateFieldMap, oldObjectData);
        serviceFacade.sendActionMq(user, Lists.newArrayList(objectData), ObjectAction.UPDATE);

        if (HandlerGrayConfig.supportManagement(user.getTenantId(), StandardAction.Edit.name(), objectDescribe.getApiName())) {
            executeTriggerActionTenantHandler(Lists.newArrayList(HandlerType.ACT, HandlerType.AFTER));
        } else {
            doPostAction(ObjectAction.UPDATE, objectData, Maps.newHashMap(), detailObjectData);
        }
    }

    protected void doChangeOwnerAction() {
        String dataId = arg.getDataId();
        StandardChangeOwnerAction.Arg args = StandardChangeOwnerAction.Arg.of(callbackData, dataId);
        ActionContext detailActionContext = buildNewActionContext(StandardAction.ChangeOwner.name());
        serviceFacade.triggerAction(detailActionContext, args, StandardChangeOwnerAction.Result.class);
    }

    protected void doChangePartnerAction() {
        String dataId = arg.getDataId();
        StandardChangePartnerAction.Arg args = StandardChangePartnerAction.Arg.of(callbackData, dataId);
        ActionContext detailActionContext = buildNewActionContext(StandardAction.ChangePartner.name());
        serviceFacade.triggerAction(detailActionContext, args, StandardChangePartnerAction.Result.class);
    }

    protected void doDeletePartnerAction() {
        String dataId = arg.getDataId();
        StandardDeletePartnerAction.Arg args = StandardDeletePartnerAction.Arg.of(dataId);
        ActionContext detailActionContext = buildNewActionContext(StandardAction.DeletePartner.name());
        serviceFacade.triggerAction(detailActionContext, args, StandardDeletePartnerAction.Result.class);
    }

    protected void doChangePartnerOwnerAction() {
        String dataId = arg.getDataId();
        StandardChangePartnerOwnerAction.Arg args = StandardChangePartnerOwnerAction.Arg.of(callbackData, dataId);
        ActionContext detailActionContext = buildNewActionContext(StandardAction.ChangePartnerOwner.name());
        serviceFacade.triggerAction(detailActionContext, args, StandardChangePartnerOwnerAction.Result.class);
    }

    protected void startWorkFlow() {
        if (CollectionUtils.empty(dataListToTriggerWorkFlow)) {
            return;
        }
        dataListToTriggerWorkFlow.forEach(x -> infraServiceFacade.startWorkFlow(x.getId(), x.getDescribeApiName(), triggerType.getId(),
                user, Maps.newHashMap(), actionContext.getEventId()));
    }

    protected boolean needTriggerWorkFlow() {
        return !Boolean.FALSE.equals(triggerWorkFlow) && isNotPublicObject();
    }

    protected boolean isNotPublicObject() {
        return Objects.isNull(objectDescribe) || !objectDescribe.isPublicObject();
    }

    protected ActionContext buildNewActionContext(String actionName) {
        ActionContext context = new ActionContext(actionContext.getRequestContext(),
                objectDescribe.getApiName(), actionName);
        //回调不需要进行基础校验(功能权限、数据权限、生命状态、锁定状态等)
        context.setAttribute(RequestContext.Attributes.SKIP_BASE_VALIDATE, Boolean.TRUE);
        //回调不需要再次触发审批流
        context.setAttribute(RequestContext.Attributes.TRIGGER_FLOW, Boolean.FALSE);
        if (triggerWorkFlow != null) {
            context.setAttribute(TRIGGER_WORK_FLOW, triggerWorkFlow);
        }

        return context;
    }

    @Data
    public static class Arg {
        //是否同步触发审批流
        private boolean triggerSynchronous;
        private String describeApiName;
        private String dataId;
        private String triggerType;
        private int code;
        private Object callbackData;
        private String requestId;
        private Map<String, Object> extraData;

        public ApprovalFlowTriggerType triggerType() {
            return ApprovalFlowTriggerType.getType(triggerType);
        }

        public ApprovalFlowStartResult triggerResult() {
            return ApprovalFlowStartResult.of(code);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result implements Serializable {
        private static final long serialVersionUID = 2596514262644368929L;
        private boolean success;
    }
}
