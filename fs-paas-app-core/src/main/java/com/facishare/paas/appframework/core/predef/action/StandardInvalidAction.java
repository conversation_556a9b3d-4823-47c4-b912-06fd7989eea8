package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction.Arg;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction.Result;
import com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPlugin;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
//@Idempotent(serializer = Serializer.Type.java)
public class StandardInvalidAction extends BaseObjectInvalidAction<Arg, Result> {

    private String lifeStatusBeforeInvalid;

    @Override
    protected String getIndustryCode(Arg arg) {
        return arg.getIndustryCode();
    }

    @Override
    public List<String> getFuncPrivilegeCodes() {
        return StandardAction.Invalid.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected Map<String, Object> getArgs() {
        return CollectionUtils.nullToEmpty(arg.getArgs());
    }

    @Override
    protected void initObjectDataList() {
        IObjectData argObjectData;
        if (CollectionUtils.notEmpty(this.dataList)) {
            argObjectData = this.dataList.get(0);
        } else {
            argObjectData = getObjectData(this.arg.getObjectDataId(), objectDescribe);
        }
        if (null == argObjectData) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.NOT_FIND_OBJECT, this.arg.getObjectDataId()));
        }
        objectDataList.add(argObjectData);
        lifeStatusBeforeInvalid = ObjectDataExt.of(argObjectData).getLifeStatus().getCode();
    }

    private IObjectData getObjectData(String id, IObjectDescribe describe) {
        return serviceFacade.findObjectData(actionContext.getTenantId(), id, describe);
    }

    @Override
    protected Result generateResult() {
        return Result.builder().objectData(ObjectDataDocument.of(objectDataList.get(0))).build();
    }

    @Override
    protected boolean isBatchAction() {
        return false;
    }

    @Override
    protected final List<String> getRecordTypes() {
        if (CollectionUtils.notEmpty(objectDataList)) {
            return Lists.newArrayList(objectDataList.get(0).getRecordType());
        }
        return null;
    }

    @Override
    protected final InvalidActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return InvalidActionDomainPlugin.Arg.builder()
                .lifeStatusBeforeInvalid(lifeStatusBeforeInvalid)
                .objectData(ObjectDataDocument.of(objectDataList.get(0)))
                .detailObjectData(ObjectDataDocument.ofMap(detailObjectData))
                .extraData(arg.getExtraData())
                .build();
    }

    @Override
    protected final Object getInterfaceArg() {
        return arg.toBulkArg();
    }

    @Override
    protected final Result getInterfaceResult(Handler.Result handlerResult) {
        return generateResult();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {

        @JSONField(name = "M1")
        @JsonProperty("object_data_id")
        @SerializedName("object_data_id") //兼容FCP的序列化
        private String objectDataId;

        @JSONField(name = "M2")
        @JsonProperty("object_describe_id")
        @SerializedName("object_describe_id")
        private String objectDescribeId;

        @JSONField(name = "M3")
        @JsonProperty("object_describe_apiname")
        @SerializedName("object_describe_apiname")
        private String objectDescribeApiName;

        private Map<String, Object> args;

        private String industryCode;
        private Map<String, Object> extraData;

        public StandardBulkInvalidAction.Arg toBulkArg() {
            StandardBulkInvalidAction.ArgHelper argHelper = StandardBulkInvalidAction.ArgHelper.builder()
                    .id(objectDataId)
                    .objectDescribeApiName(objectDescribeApiName)
                    .objectDescribeId(objectDescribeId)
                    .build();
            return StandardBulkInvalidAction.Arg.builder()
                    .industryCode(industryCode)
                    .args(args)
                    .json(JSON.toJSONString(argHelper))
                    .extraData(extraData)
                    .build();
        }

        public void putExtraData(String key, Object value) {
            if (Objects.isNull(extraData)) {
                extraData = Maps.newHashMap();
            }
            extraData.put(key, value);
        }

        public Object getExtraData(String key) {
            if (Objects.isNull(extraData)) {
                return null;
            }
            return extraData.get(key);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result implements Serializable {

        private static final long serialVersionUID = 700404523735004910L;
        @JSONField(name = "M3")
        private ObjectDataDocument objectData;
    }
}
