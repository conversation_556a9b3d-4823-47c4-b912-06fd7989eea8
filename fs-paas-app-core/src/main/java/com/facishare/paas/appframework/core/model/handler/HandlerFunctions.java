package com.facishare.paas.appframework.core.model.handler;

import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;

/**
 * 业务处理器的函数集合，定义了构建接口参数函数和处理接口返回值函数
 * Created by zhouwr on 2023/1/6.
 */
public interface HandlerFunctions {
    interface BuildArgFunction<A> {
        Handler.Arg<A> apply(SimpleHandlerDescribe handlerDescribe);
    }

    interface ExecuteFunction {
        void apply();
    }

    interface ProcessResultFunction<A, R> {
        void apply(HandlerContext handlerContext, Handler.Arg<A> handlerArg, Handler.Result<R> handlerResult);
    }
}
