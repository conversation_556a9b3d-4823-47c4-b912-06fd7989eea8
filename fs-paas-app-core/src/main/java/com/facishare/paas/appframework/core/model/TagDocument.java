package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.metadata.api.describe.ISubTagDescribe;
import com.facishare.paas.metadata.api.describe.ITagDescribe;
import com.google.common.collect.Maps;

import javax.print.attribute.standard.MediaSize;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 封装标签组向前端展示的必要字段和样式
 *
 * <AUTHOR>
 * @date 2020/3/19 5:07 下午
 */
public class TagDocument extends DocumentBaseEntity {
    public static final String ID = "id";
    public static final String NAME = "name";
    public static final String API_NAME = "tag_api_name";
    public static final String CREATE_TIME = "create_time";
    public static final String MODIFY_TIME = "last_modified_time";
    public static final String CREATE_USER = "create_user";
    public static final String CREATE_USER__R = "create_user__r";
    public static final String MODIFY_USER = "last_modify_user";
    public static final String MODIFY_USER__R = "last_modify_user__r";
    public static final String TAG_GROUP_ID = "tag_group_id";
    public static final String TAG_GROUP_NAME = "tag_group_name";
    public static final String IS_ACTIVE = "is_active";
    public static final String IS_DELETE = "is_delete";
    public static final String DESCRIPTION = "description";




    private TagDocument(Map<String, Object> map) {
        super(map);
    }

    public static TagDocument of(ISubTagDescribe tag) {
        Map<String, Object> map = Maps.newHashMap();
        map.put(ID, tag.getId());
        map.put(NAME, tag.getName());
        map.put(TAG_GROUP_ID, tag.getTagId());
        map.put(API_NAME, tag.getTagApiName());
        map.put(CREATE_TIME, tag.getCreateTime());
        map.put(MODIFY_TIME, tag.getLastModifiedTime());
        map.put(CREATE_USER, tag.getCreatedBy());
        map.put(IS_ACTIVE,tag.getIsActive());
        map.put(IS_DELETE,tag.isDeleted());
        map.put(MODIFY_USER, null);
        map.put(DESCRIPTION, tag.getDescription());
        return new TagDocument(map);
    }

    public static List<TagDocument> ofList(List<ISubTagDescribe> tags) {
        return CollectionUtils.nullToEmpty(tags).stream().map(TagDocument::of).collect(Collectors.toList());
    }

    public TagDocument createUser(String name) {
        put(CREATE_USER__R, name);
        return this;
    }

    public TagDocument modifyUser(String name) {
        put(MODIFY_USER__R, name);
        return this;
    }

    public TagDocument groupName(String name) {
        put(TAG_GROUP_NAME, name);
        return this;
    }

}
