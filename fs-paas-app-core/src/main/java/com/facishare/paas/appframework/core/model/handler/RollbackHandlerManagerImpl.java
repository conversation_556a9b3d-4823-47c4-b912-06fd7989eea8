package com.facishare.paas.appframework.core.model.handler;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by zhouwr on 2023/7/17.
 */
@Slf4j
@Component
public class RollbackHandlerManagerImpl implements RollbackHandlerManager {

    private final Map<String, RollbackHandler> rollbackHandlerMap = Maps.newHashMap();

    @Override
    public void register(RollbackHandler handler) {
        Class<?> clazz = AopUtils.getTargetClass(handler);
        RollbackHandlerProvider provider = clazz.getAnnotation(RollbackHandlerProvider.class);
        if (provider == null
                || Strings.isNullOrEmpty(provider.interfaceCode())
                || Strings.isNullOrEmpty(provider.objectApiName())) {
            log.warn("ignore rollback handler which has no provider:{}", clazz);
            return;
        }
        String name = getRollbackHandlerName(provider.interfaceCode(), provider.objectApiName());
        if (rollbackHandlerMap.containsKey(name)) {
            throw new ValidateException("RollbackHandler:" + name + " already exists!");
        }
        rollbackHandlerMap.put(name, handler);
        log.info("register RollbackHandler:{}-{}", name, clazz);
    }

    @Override
    public RollbackHandler getRollbackHandler(String interfaceCode, String objectApiName) {
        String name = getRollbackHandlerName(interfaceCode, objectApiName);
        return rollbackHandlerMap.get(name);
    }

    private String getRollbackHandlerName(String interfaceCode, String objectApiName) {
        return "RollbackHandler_" + interfaceCode + "_" + objectApiName;
    }
}
