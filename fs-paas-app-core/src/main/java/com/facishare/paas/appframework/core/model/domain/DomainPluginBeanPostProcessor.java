package com.facishare.paas.appframework.core.model.domain;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 * Created by zhouweirong on 2021/10/18.
 */
@Component
public class DomainPluginBeanPostProcessor implements BeanPostProcessor {

    @Autowired
    private DomainPluginManager domainPluginManager;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof DomainPlugin) {
            domainPluginManager.register(bean);
        }
        return bean;
    }
}
