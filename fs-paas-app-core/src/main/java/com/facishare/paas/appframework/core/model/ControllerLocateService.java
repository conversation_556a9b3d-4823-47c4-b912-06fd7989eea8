package com.facishare.paas.appframework.core.model;

/**
 * Controller locate service interface
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/7/3.
 */
public interface ControllerLocateService {
    Controller locateController(ControllerContext context, Object payload);

    /**
     * @param context
     * @param payload
     * @param resultType Controller的返回值类型，如果此参数为null，则只会调用本地的Action，否则可能走RPC调用
     * @return
     */
    Controller locateController(ControllerContext context, Object payload, Class resultType);
}
