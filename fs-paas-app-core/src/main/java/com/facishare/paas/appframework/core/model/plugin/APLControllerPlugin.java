package com.facishare.paas.appframework.core.model.plugin;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/7/19
 */
public interface APLControllerPlugin extends ControllerPlugin<APLControllerPlugin.Arg, APLControllerPlugin.Result> {

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Arg extends Plugin.Arg {
        private final Object controllerArg;
        private final Object controllerResult;

        private final TriggerInfo triggerInfo;

        private String code;
        private String type;

        public Arg(String objectApiName, Object controllerArg, Object controllerResult, TriggerInfo triggerInfo) {
            setObjectApiName(objectApiName);
            this.triggerInfo = triggerInfo;
            this.controllerArg = controllerArg;
            this.controllerResult = controllerResult;
        }

        public Class<?> argType() {
            return controllerArg.getClass();
        }

        public Class<?> resultType() {
            if (Objects.isNull(controllerResult)) {
                return null;
            }
            return controllerResult.getClass();
        }
    }

    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Result extends Plugin.Result {
        private final Object controllerArg;
        private final Object controllerResult;
        private final Boolean supportAfter;

        public Result(Object controllerArg, Object controllerResult, Boolean supportAfter) {
            this.controllerArg = controllerArg;
            this.controllerResult = controllerResult;
            this.supportAfter = supportAfter;
        }

        public <T> T getControllerArg(Class<T> clazz) {
            return clazz.cast(controllerArg);
        }

        public <T> T getControllerResult(Class<T> clazz) {
            return clazz.cast(controllerResult);
        }

        public boolean supportAfter() {
            return BooleanUtils.isTrue(supportAfter);
        }
    }

    @Data
    class TriggerInfo {
        private String client;
        private String triggerPage;
        private String triggerObject;
        private String triggerField;
    }
}
