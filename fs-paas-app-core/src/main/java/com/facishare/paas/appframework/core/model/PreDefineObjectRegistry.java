package com.facishare.paas.appframework.core.model;

import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.ObjectRegister;
import com.google.common.collect.Maps;
import com.google.common.base.Strings;

import java.util.Map;
import java.util.Objects;

/**
 * 预定义对象注册
 * <p>
 * Created by liyiguang on 2017/6/20.
 */
public abstract class PreDefineObjectRegistry {

    private static Map<String, PreDefineObject> preDefineObjects = Maps.newHashMap();

    private static Map<String, Map<String, PreDefineObject>> modulePredefineObjects = Maps.newHashMap();

    /**
     * 先放在这个地方，以后模块化后重构
     */
    private static Map<String, ModuleClassLocator> moduleClassLocators = Maps.newHashMap();


    public static void registerModuleClassLocator(String appId, ModuleClassLocator moduleClassLocator) {
        moduleClassLocators.put(appId, moduleClassLocator);
    }


    public static void register(PreDefineObject preDefineObject) {
        Objects.requireNonNull(preDefineObject);
        Objects.requireNonNull(preDefineObject.getApiName());
        Objects.requireNonNull(preDefineObject.getPackageName());

        if (!Strings.isNullOrEmpty(preDefineObject.getAppId())) {
            registerModuleObject(preDefineObject);
        } else {
            registerCommonObject(preDefineObject);
        }
    }

    private static void registerCommonObject(PreDefineObject preDefineObject) {
        preDefineObjects.put(preDefineObject.getApiName(), preDefineObject);
        ObjectRegister.registerObjectApiName(preDefineObject.getApiName());
    }

    private static void registerModuleObject(PreDefineObject preDefineObject) {
        Map<String, PreDefineObject> map = modulePredefineObjects.computeIfAbsent(
                preDefineObject.getAppId(), key -> Maps.newHashMap());
        map.put(preDefineObject.getApiName(), preDefineObject);
    }


    public static PreDefineObject getPreDefineObject(String apiName) {
        return preDefineObjects.get(apiName);
    }

    public static PreDefineObject getPreDefineObject(String originalAppId, String apiName) {
        //appId 映射
        String appId = AppIdMapping.getNamedAppId(originalAppId);

        Map<String, PreDefineObject> map = modulePredefineObjects.get(appId);
        if (map != null) {
            PreDefineObject ret = map.get(apiName);
            if (ret != null) {
                return ret;
            }
        }
        return getPreDefineObject(apiName);
    }

    public static ModuleClassLocator getModuleClassLocator(String originalAppId) {

        //appId 映射
        String appId = AppIdMapping.getNamedAppId(originalAppId);

        return moduleClassLocators.get(appId);
    }

}
