package com.facishare.paas.appframework.button.action;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * UpdateFieldAction多语言数据处理专项测试 重点测试当前变更对源代码带来的影响 GenerateByAI
 */
public class UpdateFieldActionMultiLangTest {

    /**
     * GenerateByAI 测试内容描述：测试getFormFieldApiName方法的基本功能 - 验证form_前缀添加逻辑
     */
    @Test
    public void testGetFormFieldApiName_BasicFunctionality() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();

        // When & Then - 测试多种输入情况
        String result1 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "test_field");
        Assert.assertEquals("普通字段名应正确添加form_前缀", "form_test_field", result1);

        String result2 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "field_ml");
        Assert.assertEquals("多语言字段名应正确添加form_前缀", "form_field_ml", result2);

        String result3 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "");
        Assert.assertEquals("空字符串应正确添加form_前缀", "form_", result3);
    }

    /**
     * GenerateByAI 测试内容描述：测试getFormFieldApiName方法处理特殊字符和边界情况
     */
    @Test
    public void testGetFormFieldApiName_EdgeCases() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();

        // When & Then - 测试边界情况
        String result1 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "field-with-dash");
        Assert.assertEquals("带破折号的字段名应正确处理", "form_field-with-dash", result1);

        String result2 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "field.with.dot");
        Assert.assertEquals("带点号的字段名应正确处理", "form_field.with.dot", result2);

        String result3 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "123field");
        Assert.assertEquals("数字开头的字段名应正确处理", "form_123field", result3);
    }

    /**
     * GenerateByAI 测试内容描述：测试getMultiLangDataMap方法处理null和空参数的边界情况
     */
    @Test
    public void testGetMultiLangDataMap_NullAndEmptyParameters() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();
        Map<String, Object> updateFieldMap = new HashMap<>();
        Map<String, Object> args = new HashMap<>();

        // When & Then - 测试null参数
        @SuppressWarnings("unchecked")
        Map<String, Object> result1 = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", null, null, null);
        Assert.assertNotNull("null参数应返回非null结果", result1);
        Assert.assertTrue("null参数应返回空Map", result1.isEmpty());

        // 测试空Map参数
        @SuppressWarnings("unchecked")
        Map<String, Object> result2 = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", updateFieldMap, null, args);
        Assert.assertNotNull("部分null参数应返回非null结果", result2);
        Assert.assertTrue("部分null参数应返回空Map", result2.isEmpty());
    }

    /**
     * GenerateByAI 测试内容描述：验证getFormFieldApiName方法的幂等性和一致性
     */
    @Test
    public void testGetFormFieldApiName_Consistency() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();
        String testField = "consistency_test_field";

        // When - 多次调用同一方法
        String result1 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", testField);
        String result2 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", testField);
        String result3 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", testField);

        // Then - 验证结果一致性
        Assert.assertEquals("多次调用应返回相同结果", result1, result2);
        Assert.assertEquals("多次调用应返回相同结果", result2, result3);
        Assert.assertEquals("结果应符合预期格式", "form_consistency_test_field", result1);
    }

    /**
     * GenerateByAI 测试内容描述：测试变更影响 - 验证getFormFieldApiName在多语言数据获取中的作用
     */
    @Test
    public void testGetFormFieldApiName_IntegrationWithMultiLangProcessing() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();

        // 模拟多语言字段名称处理流程
        String[] multiLangFields = {
            "name_ml", // 姓名多语言字段
            "description_ml", // 描述多语言字段  
            "title_ml", // 标题多语言字段
            "address_ml" // 地址多语言字段
        };

        // When & Then - 验证每个多语言字段都能正确添加form_前缀
        for (String field : multiLangFields) {
            String result = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", field);
            String expected = "form_" + field;

            Assert.assertEquals(
                    String.format("多语言字段 '%s' 应正确添加form_前缀用于从args中获取数据", field),
                    expected,
                    result
            );

            // 验证form_前缀确实被添加
            Assert.assertTrue(
                    String.format("处理后的字段名 '%s' 应以form_开头", result),
                    result.startsWith("form_")
            );
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试字段名转换的业务逻辑正确性
     */
    @Test
    public void testGetFormFieldApiName_BusinessLogicValidation() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();

        // 业务场景：普通字段名转换为表单字段名
        Map<String, String> fieldMappings = new HashMap<>();
        fieldMappings.put("user_name", "form_user_name");
        fieldMappings.put("user_email", "form_user_email");
        fieldMappings.put("user_phone", "form_user_phone");
        fieldMappings.put("company_name", "form_company_name");

        // When & Then - 验证业务场景下的字段名转换
        for (Map.Entry<String, String> entry : fieldMappings.entrySet()) {
            String input = entry.getKey();
            String expected = entry.getValue();

            String actual = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", input);

            Assert.assertEquals(
                    String.format("业务字段 '%s' 转换结果应符合预期", input),
                    expected,
                    actual
            );
        }
    }
}
