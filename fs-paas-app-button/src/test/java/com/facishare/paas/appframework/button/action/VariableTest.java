package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.Map;

/**
 * 变量测试类
 */
public class VariableTest {

    @Test
    public void testCurrentObjectVariable() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();
        objectData.set("id", "test_id");
        objectData.set("name", "测试对象");
        objectData.set("status", "active");
        
        IUdefButton button = Mockito.mock(IUdefButton.class);
        IObjectDescribe describe = Mockito.mock(IObjectDescribe.class);
        
        // 测试变量类型判断
        String variableName = "$__current_object__$";
        
        Variable.Type type = Variable.Type.getByName(variableName, button);
        Assert.assertEquals("当前对象变量应被识别为CURRENT_OBJECT类型", Variable.Type.CURRENT_OBJECT, type);
        
        // 测试普通对象字段变量类型判断
        String fieldVariableName = "$name$";
        Variable.Type fieldType = Variable.Type.getByName(fieldVariableName, button);
        Assert.assertEquals("普通对象字段变量应被识别为OBJECT_FIELD类型", Variable.Type.OBJECT_FIELD, fieldType);
        
        // 测试创建变量对象
        Map<String, Object> args = new HashMap<>();
        Variable variable = new Variable(variableName, describe, objectData, button, args);
        
        // 验证变量类型
        Assert.assertEquals("变量应被正确识别为当前对象变量类型", Variable.Type.CURRENT_OBJECT, variable.getType());
        
        // 验证解析后的变量值
        Map<String, Object> expectedMap = ObjectDataExt.toMap(objectData);
        Assert.assertEquals("当前对象变量的值应为完整的对象数据Map", expectedMap, variable.getValue());
    }
} 