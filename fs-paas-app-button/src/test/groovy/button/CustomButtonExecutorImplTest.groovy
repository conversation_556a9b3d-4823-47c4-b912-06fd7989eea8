package button

import com.facishare.paas.appframework.button.CustomButtonExecutorImpl
import com.facishare.paas.appframework.button.action.*
import com.facishare.paas.appframework.button.dto.ButtonExecutor
import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.core.exception.FunctionException
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.*
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService
import com.facishare.paas.appframework.privilege.UserRoleInfoService
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IUdefAction
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * create by zhaoju on 2020/02/12
 */
@Unroll
class CustomButtonExecutorImplTest extends Specification {
    CustomButtonExecutorImpl customButtonExecutor

    // Mock dependencies
    MetaDataFindServiceImpl metaDataFindService = Mock()
    CustomButtonServiceImpl buttonService = Mock()
    PostActionServiceImpl actionService = Mock()
    ActionExecutorManager actionExecutorManager = Mock()
    DescribeLogicServiceImpl describeLogicService = Mock()
    FunctionPrivilegeService functionPrivilegeService = Mock()
    UserRoleInfoService userRoleInfoService = Mock()
    OrgService orgService = Mock()
    MaskFieldLogicService maskFieldLogicService = Mock()
    UpdateFieldAction updateFieldAction = Mock()
    ValidateRuleService validateRuleService = Mock()

    void setup() {
        customButtonExecutor = new CustomButtonExecutorImpl()

        // Inject mocked dependencies
        customButtonExecutor.metaDataFindService = metaDataFindService
        customButtonExecutor.buttonService = buttonService
        customButtonExecutor.actionService = actionService
        customButtonExecutor.actionExecutorManager = actionExecutorManager
        customButtonExecutor.describeLogicService = describeLogicService
        customButtonExecutor.functionPrivilegeService = functionPrivilegeService
        customButtonExecutor.userRoleInfoService = userRoleInfoService
        customButtonExecutor.orgService = orgService
        customButtonExecutor.maskFieldLogicService = maskFieldLogicService
        customButtonExecutor.updateFieldAction = updateFieldAction
        customButtonExecutor.validateRuleService = validateRuleService
    }

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerValidationFunction方法，当按钮为null时返回空结果
     */
    def "triggerValidationFunctionTestButtonNull"() {
        given:
        User user = User.systemUser("7768")
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .buttonApiName("testButton")
                .describeApiName("TestObj")
                .build()

        when:
        buttonService.findButtonByApiName(user, arg.getButtonApiName(), arg.getDescribeApiName()) >> null
        ButtonExecutor.Result result = customButtonExecutor.triggerValidationFunction(user, arg)

        then:
        result != null
        !result.hasReturnValue
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerValidationFunction方法，当按钮actions为空时返回空结果
     */
    def "triggerValidationFunctionTestEmptyActions"() {
        given:
        User user = User.systemUser("7768")
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .buttonApiName("testButton")
                .describeApiName("TestObj")
                .build()
        IUdefButton button = Mock(IUdefButton)

        when:
        buttonService.findButtonByApiName(user, arg.getButtonApiName(), arg.getDescribeApiName()) >> button
        button.getActions() >> []
        ButtonExecutor.Result result = customButtonExecutor.triggerValidationFunction(user, arg)

        then:
        result != null
        !result.hasReturnValue
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerValidationFunction方法正常执行流程
     */
    def "triggerValidationFunctionTestNormal"() {
        given:
        User user = User.systemUser("7768")
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .buttonApiName("testButton")
                .describeApiName("TestObj")
                .objectDataId("123")
                .build()
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IObjectData objectData = Mock(IObjectData)

        when:
        buttonService.findButtonByApiName(user, arg.getButtonApiName(), arg.getDescribeApiName()) >> button
        button.getActions() >> ["action1"]
        describeLogicService.findObject(user.getTenantId(), arg.getDescribeApiName()) >> describe
        metaDataFindService.findObjectData(user, arg.getObjectDataId(), arg.getDescribeApiName()) >> objectData

        ButtonExecutor.Result result = customButtonExecutor.triggerValidationFunction(user, arg)

        then:
        result != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerFunctionAction方法，当按钮为null时返回空结果
     */
    def "triggerFunctionActionTestButtonNull"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        ButtonExecutorContext context = Mock(ButtonExecutorContext)
        User user = User.systemUser("7768")
        IObjectDescribe describe = Mock(IObjectDescribe)

        when:
        context.getButton() >> null
        context.getUser() >> user
        context.getDescribe() >> describe
        ButtonExecutor.Result result = customButtonExecutor.triggerFunctionAction(arg, context)

        then:
        result != null
        !result.hasReturnValue
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerFunctionAction方法，当actionList为空时返回空结果
     */
    def "triggerFunctionActionTestEmptyActionList"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        User user = User.systemUser("7768")
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .button(button)
                .user(user)
                .describe(describe)
                .stage(UdefActionExt.PRE)
                .build()

        when:
        button.getActions() >> ["action1"]
        describe.getApiName() >> "TestObj"
        actionService.findActionListByStage(user, button, describe.getApiName(), context.getStage()) >> []

        ButtonExecutor.Result result = customButtonExecutor.triggerFunctionAction(arg, context)

        then:
        result != null
        !result.hasReturnValue
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerFunctionAction方法抛出ValidateException异常场景
     */
    def "triggerFunctionActionErrorValidateException"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        User user = User.systemUser("7768")
        IUdefAction action = Mock(IUdefAction)
        ActionExecutor executor = Mock(ActionExecutor)
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .button(button)
                .user(user)
                .describe(describe)
                .stage(UdefActionExt.PRE)
                .build()

        when:
        // 基本Mock设置
        button.getActions() >> ["action1"]
        button.getApiName() >> "testButton"
        button.getParamForm() >> []
        describe.getApiName() >> "TestObj"

        // Action列表Mock
        actionService.findActionListByStage(user, button, describe.getApiName(), context.getStage()) >> [action]

        // Arg Mock
        arg.getObjectData() >> Mock(IObjectData)
        arg.getArgs() >> [:]
        arg.isSkipValidationFunction() >> false

        // Action Mock - 关键设置
        action.getActionType() >> "custom_function"
        action.getStage() >> UdefActionExt.PRE

        // 依赖服务Mock
        functionPrivilegeService.getReadonlyFields(user, describe.getApiName()) >> []
        actionExecutorManager.getActionExecutor(_ as ActionExecutorType) >> executor

        // 让executor抛出异常
        executor.invoke(_, _) >> { throw new ValidateException("Test error", 1001) }

        // 调用被测方法
        customButtonExecutor.triggerFunctionAction(arg, context)

        then:
        FunctionException ex = thrown(FunctionException)
        ex.message.contains("Test error")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerValidateRule方法，当actionList为空时返回空结果
     */
    def "triggerValidateRuleTestEmptyActionList"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        User user = User.systemUser("7768")
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .button(button)
                .user(user)
                .describe(describe)
                .build()

        when:
        describe.getApiName() >> "TestObj"
        actionService.findActionList(user, button, describe.getApiName()) >> []

        ButtonExecutor.Result result = customButtonExecutor.triggerValidateRule(arg, context)

        then:
        result != null
        !result.hasReturnValue
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试startCustomButton方法，当按钮actions为空时返回空结果
     */
    def "startCustomButtonTestEmptyActions"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        User user = User.systemUser("7768")
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .button(button)
                .user(user)
                .describe(describe)
                .build()

        when:
        button.getActions() >> []

        ButtonExecutor.Result result = customButtonExecutor.startCustomButton(arg, context)

        then:
        result != null
        !result.hasReturnValue
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试startCustomButton方法抛出Exception异常场景
     */
    def "startCustomButtonErrorException"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        User user = User.systemUser("7768")
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefAction action = Mock(IUdefAction)
        ActionExecutor executor = Mock(ActionExecutor)
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .button(button)
                .user(user)
                .describe(describe)
                .build()

        when:
        button.getActions() >> ["action1"]
        button.getApiName() >> "testButton"
        describe.getApiName() >> "TestObj"
        actionService.findActionList(user, button, describe.getApiName()) >> [action]
        arg.getObjectData() >> Mock(IObjectData)
        arg.getArgs() >> [:]
        action.getStage() >> UdefActionExt.CURRENT
        action.getActionType() >> "Updates"
        actionExecutorManager.getActionExecutor(_ as ActionExecutorType) >> executor
        executor.invoke(_, _) >> { throw new RuntimeException("Test error") }

        customButtonExecutor.startCustomButton(arg, context)

        then:
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCurrentAction方法，当callbackData为null时直接返回
     */
    def "doCurrentActionTestCallbackDataNull"() {
        given:
        Map<String, Object> callbackData = null
        ActionContext actionContext = Mock(ActionContext)
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectData data = Mock(IObjectData)
        Map<String, List<IObjectData>> details = [:]

        when:
        customButtonExecutor.doCurrentAction(callbackData, actionContext, objectDescribe, data, details)

        then:
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCurrentAction方法，当按钮不存在时抛出ValidateException
     */
    def "doCurrentActionErrorButtonNotExist"() {
        given:
        Map<String, Object> callbackData = [args: [:]]
        ActionContext actionContext = Mock(ActionContext)
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectData data = Mock(IObjectData)
        Map<String, List<IObjectData>> details = [:]
        User user = User.systemUser("7768")

        when:
        actionContext.getUser() >> user
        buttonService.findButtonByApiName(user, "invalid", objectDescribe) >> null

        customButtonExecutor.doCurrentAction(callbackData, actionContext, objectDescribe, data, details)

        then:
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkParam方法处理正常参数表单验证
     */
    def "checkParamTestNormalValidation"() {
        given:
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IObjectData objectData = Mock(IObjectData)
        ObjectDescribeExt objectDescribeExt = Mock(ObjectDescribeExt)
        Map<String, Object> args = ["form_name": "test", "form_age": 25]
        User user = User.systemUser("7768")

        def paramForm = [
                ["api_name": "form_name", "label": "Name", "is_required": true, "type": "text"],
                ["api_name": "form_age", "label": "Age", "is_required": false, "type": "number"]
        ]

        when:
        button.getParamForm() >> paramForm
        functionPrivilegeService.getReadonlyFields(user, describe.getApiName()) >> []
        describe.getApiName() >> "TestObj"
        describe.containsField(_) >> true
        describe.getFieldDescribes() >> []
        customButtonExecutor.checkParam(button, describe, objectData, args, user)

        then:
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkParam方法处理必填字段缺失的异常场景
     */
    def "checkParamErrorMissingRequiredField"() {
        given:
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IObjectData objectData = Mock(IObjectData)
        Map<String, Object> args = ["form_age": 25] // 缺少必填的 form_name
        User user = User.systemUser("7768")

        def paramForm = [
                ["api_name": "form_name", "label": "Name", "is_required": true, "type": "text"],
                ["api_name": "form_age", "label": "Age", "is_required": false, "type": "number"]
        ]

        when:
        button.getParamForm() >> paramForm
        button.getDefineType() >> "custom"
        functionPrivilegeService.getReadonlyFields(user, describe.getApiName()) >> []
        describe.getApiName() >> "TestObj"
        describe.containsField(_) >> true
        describe.getFieldDescribes() >> []
        customButtonExecutor.checkParam(button, describe, objectData, args, user)

        then:
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerFunctionAction方法处理CustomBizAction的场景
     */
    def "triggerFunctionActionTestCustomBizAction"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        User user = User.systemUser("7768")
        IUdefAction action = Mock(IUdefAction)
        ActionExecutor executor = Mock(ActionExecutor)
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .button(button)
                .user(user)
                .describe(describe)
                .stage(UdefActionExt.CURRENT)
                .build()

        when:
        button.getActions() >> ["action1"]
        button.getParamForm() >> []
        describe.getApiName() >> "TestObj"
        actionService.findActionListByStage(user, button, describe.getApiName(), context.getStage()) >> [action]
        arg.getObjectData() >> Mock(IObjectData)
        arg.getArgs() >> [:]
        arg.isSkipValidationFunction() >> false

        action.getActionType() >> "custom_biz"
        action.getStage() >> UdefActionExt.CURRENT

        functionPrivilegeService.getReadonlyFields(user, describe.getApiName()) >> []
        actionExecutorManager.getActionExecutor(_ as ActionExecutorType) >> executor
        executor.invoke(_, _) >> ButtonExecutor.Result.builder().hasReturnValue(true).returnValue("success").build()

        ButtonExecutor.Result result = customButtonExecutor.triggerFunctionAction(arg, context)

        then:
        result.hasReturnValue
        result.returnValue == "success"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerFunctionAction方法处理OneFlowAction的场景
     */
    def "triggerFunctionActionTestOneFlowAction"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        User user = User.systemUser("7768")
        IUdefAction action = Mock(IUdefAction)
        ActionExecutor executor = Mock(ActionExecutor)
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .button(button)
                .user(user)
                .describe(describe)
                .stage(UdefActionExt.CURRENT)
                .build()

        when:
        button.getActions() >> ["action1"]
        button.getParamForm() >> []
        describe.getApiName() >> "TestObj"
        actionService.findActionListByStage(user, button, describe.getApiName(), context.getStage()) >> [action]
        arg.getObjectData() >> Mock(IObjectData)
        arg.getArgs() >> [:]
        arg.isSkipValidationFunction() >> false

        action.getActionType() >> "one_flow"
        action.getStage() >> UdefActionExt.CURRENT

        functionPrivilegeService.getReadonlyFields(user, describe.getApiName()) >> []
        actionExecutorManager.getActionExecutor(_ as ActionExecutorType) >> executor
        executor.invoke(_, _) >> ButtonExecutor.Result.builder().hasReturnValue(true).returnValue("flow_success").build()

        ButtonExecutor.Result result = customButtonExecutor.triggerFunctionAction(arg, context)

        then:
        result.hasReturnValue
        result.returnValue == "flow_success"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerFunctionAction方法处理阻塞结果的场景
     */
    def "triggerFunctionActionTestBlockingResult"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        User user = User.systemUser("7768")
        IUdefAction action = Mock(IUdefAction)
        ActionExecutor executor = Mock(ActionExecutor)
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .button(button)
                .user(user)
                .describe(describe)
                .stage(UdefActionExt.PRE)
                .build()

        when:
        button.getActions() >> ["action1"]
        button.getParamForm() >> []
        describe.getApiName() >> "TestObj"
        actionService.findActionListByStage(user, button, describe.getApiName(), context.getStage()) >> [action]
        arg.getObjectData() >> Mock(IObjectData)
        arg.getArgs() >> [:]
        arg.isSkipValidationFunction() >> false

        action.getActionType() >> "custom_function"
        action.getStage() >> UdefActionExt.PRE

        functionPrivilegeService.getReadonlyFields(user, describe.getApiName()) >> []
        actionExecutorManager.getActionExecutor(_ as ActionExecutorType) >> executor

        // 模拟返回阻塞结果
        def blockingResult = ButtonExecutor.Result.builder()
                .hasReturnValue(true)
                .block(true)
                .returnValue("blocked")
                .build()
        executor.invoke(_, _) >> blockingResult

        ButtonExecutor.Result result = customButtonExecutor.triggerFunctionAction(arg, context)

        then:
        result.hasReturnValue
        result.block
        result.returnValue == "blocked"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试startCustomButton方法处理FunctionException异常的场景
     */
    def "startCustomButtonErrorFunctionException"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        User user = User.systemUser("7768")
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefAction action = Mock(IUdefAction)
        ActionExecutor executor = Mock(ActionExecutor)
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .button(button)
                .user(user)
                .describe(describe)
                .build()

        when:
        button.getActions() >> ["action1"]
        button.getApiName() >> "testButton"
        button.getParamForm() >> []
        describe.getApiName() >> "TestObj"
        actionService.findActionList(user, button, describe.getApiName()) >> [action]
        arg.getObjectData() >> Mock(IObjectData)
        arg.getArgs() >> [:]
        action.getStage() >> UdefActionExt.CURRENT
        action.getActionType() >> "custom_function"
        actionExecutorManager.getActionExecutor(_ as ActionExecutorType) >> executor
        functionPrivilegeService.getReadonlyFields(user, describe.getApiName()) >> []

        executor.invoke(_, _) >> { throw new FunctionException("Function error", 1001) }

        customButtonExecutor.startCustomButton(arg, context)

        then:
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试startCustomButton方法跳过PRE阶段action的场景
     */
    def "startCustomButtonTestSkipPreStage"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        User user = User.systemUser("7768")
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefAction preAction = Mock(IUdefAction)
        IUdefAction currentAction = Mock(IUdefAction)
        ActionExecutor executor = Mock(ActionExecutor)
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .button(button)
                .user(user)
                .describe(describe)
                .build()

        when:
        button.getActions() >> ["action1", "action2"]
        button.getParamForm() >> []
        button.getApiName() >> "testButton"
        describe.getApiName() >> "TestObj"
        actionService.findActionList(user, button, describe.getApiName()) >> [preAction, currentAction]
        arg.getObjectData() >> Mock(IObjectData)
        arg.getArgs() >> [:]

        // PRE阶段的action应该被跳过
        preAction.getStage() >> UdefActionExt.PRE
        preAction.getActionType() >> "custom_function"

        // CURRENT阶段的action应该被执行
        currentAction.getStage() >> ""
        currentAction.getActionType() >> "updates"

        functionPrivilegeService.getReadonlyFields(user, describe.getApiName()) >> []
        actionExecutorManager.getActionExecutor(_ as ActionExecutorType) >> executor
        ButtonExecutor.Result result = customButtonExecutor.startCustomButton(arg, context)

        then:
        result.hasReturnValue
        result.returnValue == "success"
        // 验证只调用了一次（即只有current action被执行）
        1 * executor.invoke(_ as ButtonExecutor.Arg, _ as ActionExecutorContext) >> ButtonExecutor.Result.builder().hasReturnValue(true).returnValue("success").build()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCurrentAction方法处理正常执行流程
     */
    def "doCurrentActionTestNormalFlow"() {
        given:
        Map<String, Object> callbackData = [args: [param1: "value1"]]
        ActionContext actionContext = Mock(ActionContext)
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectData data = Mock(IObjectData)
        Map<String, List<IObjectData>> details = [:]
        User user = User.systemUser("7768")
        IUdefButton button = Mock(IUdefButton)
        IUdefAction action = Mock(IUdefAction)
        ActionExecutor executor = Mock(ActionExecutor)

        when:
        actionContext.getUser() >> user
        objectDescribe.getApiName() >> "TestObj"
        // 修正按钮名称，根据错误信息实际调用的是'Abolish_button_default'
        buttonService.findButtonByApiName(user, "Abolish_button_default", objectDescribe) >> button
        actionService.findActionList(user, button, objectDescribe.getApiName()) >> [action]
        action.getStage() >> UdefActionExt.CURRENT
        action.getActionType() >> "Updates"  // 添加actionType的Mock
        actionExecutorManager.getActionExecutor(ActionExecutorType.UPDATES) >> executor

        customButtonExecutor.doCurrentAction(callbackData, actionContext, objectDescribe, data, details)

        then:
        1 * executor.invoke(_, _)
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doCurrentAction方法处理空currentActions的场景
     */
    def "doCurrentActionTestEmptyCurrentActions"() {
        given:
        Map<String, Object> callbackData = [args: [param1: "value1"]]
        ActionContext actionContext = Mock(ActionContext)
        IObjectDescribe objectDescribe = Mock(IObjectDescribe)
        IObjectData data = Mock(IObjectData)
        Map<String, List<IObjectData>> details = [:]
        User user = User.systemUser("7768")
        IUdefButton button = Mock(IUdefButton)
        IUdefAction action = Mock(IUdefAction)

        when:
        actionContext.getUser() >> user
        objectDescribe.getApiName() >> "TestObj"
        buttonService.findButtonByApiName(user, "Abolish_button_default", objectDescribe) >> button
        actionService.findActionList(user, button, objectDescribe.getApiName()) >> [action]
        // 设置为非CURRENT阶段，这样getActionMapForStage不会包含CURRENT
        action.getStage() >> UdefActionExt.PRE

        customButtonExecutor.doCurrentAction(callbackData, actionContext, objectDescribe, data, details)

        then:
        noExceptionThrown()
        // 不应该调用executor，因为没有CURRENT阶段的action
        0 * actionExecutorManager.getActionExecutor(_)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getActionType方法返回正确的ActionExecutorType
     */
    def "getActionTypeTest"() {
        when:
        def result = customButtonExecutor.getActionType(stage, actionType)

        then:
        result == expected

        where:
        stage                 | actionType        | expected
        UdefActionExt.CURRENT | "custom_function" | ActionExecutorType.CURRENT_FUNCTION
        UdefActionExt.PRE     | "custom_function" | ActionExecutorType.VALIDATE_FUNCTION
        UdefActionExt.POST    | "custom_function" | ActionExecutorType.POST_FUNCTION
        ""                    | "updates"         | ActionExecutorType.UPDATES
        UdefActionExt.CURRENT | "one_flow"        | ActionExecutorType.CURRENT_ONE_FLOW
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试queryObjectData方法查询对象数据
     */
    def "queryObjectDataTestWithObjectDataId"() {
        given:
        User user = User.systemUser("7768")
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData expectedObjectData = Mock(IObjectData)

        when:
        arg.getObjectData() >> null
        arg.getObjectDataId() >> "123"
        arg.getDescribeApiName() >> "TestObj"
        metaDataFindService.findObjectData(user, "123", "TestObj") >> expectedObjectData

        IObjectData result = customButtonExecutor.queryObjectData(user, arg)

        then:
        result == expectedObjectData
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试queryObjectData方法返回已有的objectData
     */
    def "queryObjectDataTestWithExistingObjectData"() {
        given:
        User user = User.systemUser("7768")
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData existingObjectData = Mock(IObjectData)

        when:
        arg.getObjectData() >> existingObjectData
        arg.getObjectDataId() >> "123"

        IObjectData result = customButtonExecutor.queryObjectData(user, arg)

        then:
        result == existingObjectData
        // 不应该调用查询方法
        0 * metaDataFindService.findObjectData(_, _, _)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isRequired方法的不同场景
     */
    def "isRequiredTest"() {
        given:
        IParamForm paramForm = Mock(IParamForm)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IFieldDescribe fieldDescribe = Mock(IFieldDescribe)

        when:
        paramForm.getIsRequired() >> isRequired
        paramForm.getObjectApiName() >> objectApiName
        paramForm.getApiName() >> "form_name"
        describe.getApiName() >> describeApiName
        if (shouldCallGetFieldDescribe) {
            describe.getFieldDescribe("name") >> fieldDescribe
            fieldDescribe.isRequired() >> fieldIsRequired
        }

        def result = customButtonExecutor.isRequired(paramForm, describe)

        then:
        result == expected

        where:
        isRequired | objectApiName | describeApiName | shouldCallGetFieldDescribe | fieldIsRequired | expected
        true       | ""            | ""              | false                      | false           | true
        false      | "123"         | "321"           | false                      | false           | false
        false      | "AccountObj"  | "AccountObj"    | true                       | true            | true
        false      | "AccountObj"  | "AccountObj"    | true                       | false           | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试outOfBoundsForManyField方法检查多选字段越界
     */
    def "outOfBoundsForManyFieldTest"() {
        given:
        def paramFormList = [
                createParamForm("form_employees", "Employees", "employee_many", ["emp1", "emp2"]),
                createParamForm("form_text", "Text", "text", "simple text")
        ]
        IObjectDescribe describe = Mock(IObjectDescribe)
        Map<String, Object> args = [
                "form_employees": ["emp1", "emp2"],
                "form_text"     : "simple text"
        ]

        when:
        def result = customButtonExecutor.outOfBoundsForManyField(paramFormList, describe, args)

        then:
        // 由于我们无法完全模拟FieldManyMaxConfig的行为，这里主要验证方法不抛异常
        result != null
    }

    private IParamForm createParamForm(String apiName, String label, String type, Object defaultValue) {
        IParamForm paramForm = Mock(IParamForm)
        paramForm.getApiName() >> apiName
        paramForm.getLabel() >> label
        paramForm.getType() >> type
        return paramForm
    }

    def "test checkParam no exception"() {
        given:
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        Map arg = [:]
        IObjectData objectData = Mock(IObjectData)
        User user = User.systemUser("7768")
        when:
        button.getParamForm() >> []
        customButtonExecutor.checkParam(button, describe, objectData, arg, user)
        then:
        noExceptionThrown()
    }

    def "test isRequired"() {
        given:
        IParamForm paramForm = Mock(IParamForm)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IFieldDescribe fieldDescribe = Mock(IFieldDescribe)
        when:
        1 * paramForm.getIsRequired() >> isRequired
        getObjectApiNameNum * paramForm.getObjectApiName() >> objectApiName
        getParamApiNameNum * paramForm.getApiName() >> "name"

        getApiNameNum * describe.getApiName() >> describeApiName
        getFieldDescribeNum * describe.getFieldDescribe("name") >> fieldDescribe

        fieldDescribe.isRequired() >> fieldIsRequired

        def result = customButtonExecutor.isRequired(paramForm, describe)
        println(result)
        then:
        result == expect
        where:
        isRequired | objectApiName | describeApiName || expect | fieldIsRequired | getObjectApiNameNum | getApiNameNum | getParamApiNameNum | getFieldDescribeNum
        true       | ""            | ""              || true   | false           | 0                   | 0             | 0                  | 0

        false      | "123"         | "321"           || false  | false           | 1                   | 1             | 0                  | 0

        false      | "AccountObj"  | "AccountObj"    || true   | true            | 1                   | 1             | 1                  | 1

    }

    def "test isSystemButtonCustomParams"() {
        when:
        CustomButtonExecutorImpl customButtonExecutor = new CustomButtonExecutorImpl()
        IUdefButton button = Mock(IUdefButton)
        IParamForm paramForm = Mock(IParamForm)
        User user = new User("any", userId)

        button.getDefineType() >> buttonDefineType
        paramForm.getDefineType() >> paramDefineType
        then:
        println button.getDefineType()
        println paramForm.getDefineType()
        println user
        def result = customButtonExecutor.isSystemButtonCustomParams(button, paramForm, user)
        result == except
        where:
        buttonDefineType | paramDefineType | userId   || except
        "system"         | "custom"        | "-10000" || false
        "system"         | "system"        | "1000"   || true
        "custom"         | "system"        | "-10000" || true
        "system"         | "system"        | "-10000" || false
    }
}
