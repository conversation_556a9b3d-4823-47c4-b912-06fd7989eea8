package com.facishare.paas.appframework.button.action


import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.MetaDataFindService
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import spock.lang.Specification
import spock.lang.Unroll

/**
 * ArgumentProcessorServiceImpl单元测试
 * 用于测试ArgumentProcessorServiceImpl类的所有功能
 */
@Unroll
class ArgumentProcessorServiceImplTest extends Specification {

    ArgumentProcessorServiceImpl argumentProcessorService
    ParseVarService parseVarService
    MetaDataFindService metaDataFindService

    def setup() {
        parseVarService = Mock(ParseVarService)
        metaDataFindService = Mock(MetaDataFindService)

        argumentProcessorService = new ArgumentProcessorServiceImpl()
        argumentProcessorService.parseVarService = parseVarService
        argumentProcessorService.metaDataFindService = metaDataFindService
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processArguments方法处理空参数列表的场景
     */
    def "processArgumentsTestEmptyArgs"() {
        given:
        def args = []
        def variableData = [:]
        def objectData = new ObjectData()
        def describe = Mock(IObjectDescribe)
        def user = User.systemUser('74255')
        def button = Mock(IUdefButton)

        when:
        def result = argumentProcessorService.processArguments(args, variableData, objectData, describe, user, button)

        then:
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processArguments方法处理正常参数的场景
     */
    def "processArgumentsTestNormalFlow"() {
        given:
        def arg1 = createTestArg("param1", "\$field1\$")
        def arg2 = createTestArg("param2", "\$field2\$")
        def args = [arg1, arg2]
        def variableData = [:]
        def objectData = new ObjectData()
        def describe = Mock(IObjectDescribe)
        def user = User.systemUser('74255')
        def button = Mock(IUdefButton)

        def variable1 = createTestVariable("field1", "value1", "text")
        def variable2 = createTestVariable("field2", "value2", "number")
        def variables = [variable1, variable2]

        when:
        parseVarService.getVarList(["field1", "field2"], variableData, objectData, user, describe, button, true) >> variables
        def result = argumentProcessorService.processArguments(args, variableData, objectData, describe, user, button)

        then:
        result.size() == 2
        result[0].getRealValue() == "value1"
        result[1].getRealValue() == "value2"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processArguments方法启用数据类型转换的场景
     */
    def "processArgumentsTestWithDataTypeConversion"() {
        given:
        def arg1 = createTestArg("param1", "\$field1\$")
        def args = [arg1]
        def variableData = [:]
        def objectData = new ObjectData()
        def describe = Mock(IObjectDescribe)
        def user = User.systemUser('74255')
        def button = Mock(IUdefButton)

        def variable1 = createTestVariable("field1", "123.45", "number")
        def variables = [variable1]

        when:
        parseVarService.getVarList(["field1"], variableData, objectData, user, describe, button, true) >> variables
        def result = argumentProcessorService.processArguments(args, variableData, objectData, describe, user, button, true)

        then:
        result.size() == 1
        result[0].getRealValue() instanceof BigDecimal
        result[0].getRealValue() == new BigDecimal("123.45")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convertValueByFieldType方法处理数字类型的场景
     */
    def "convertValueByFieldTypeTestNumberField"() {
        given:

        when:
        def result = argumentProcessorService.convertValueByFieldType("test_field", fieldType, value)

        then:
        result == expected

        where:
        fieldType | value     | expected
        "number"  | "123.45"  | new BigDecimal("123.45")
        "number"  | "  456  " | new BigDecimal("456")
        "number"  | ""        | null
        "number"  | "   "     | null
        "number"  | 789       | 789
        "number"  | 123.45    | 123.45
        "text"    | "not num" | "not num"
        "number"  | "invalid" | "invalid"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试convertValueByFieldType方法处理null值的场景
     */
    def "convertValueByFieldTypeTestNullValues"() {
        when:
        def result = argumentProcessorService.convertValueByFieldType("test_field", fieldType, value)

        then:
        result == expected

        where:
        fieldType | value | expected
        null      | "123" | "123"
        "number"  | null  | null
        null      | null  | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processOneFlowArguments方法的正常流程
     */
    def "processOneFlowArgumentsTestNormalFlow"() {
        given:
        def arg1 = createOneFlowArg("param1", "\$field1\$", "object", "ObjectA")
        def arg2 = createOneFlowArg("param2", "\$field2\$", "text", null)
        def args = [arg1, arg2]
        def variableData = [:]
        def objectData = new ObjectData()
        def describe = Mock(IObjectDescribe)
        def user = User.systemUser('74255')
        def button = Mock(IUdefButton)

        def variable1 = createTestVariable("field1", "obj123", "object")
        def variable2 = createTestVariable("field2", "test value", "text")
        def variables = [variable1, variable2]

        def queryObjectData = new ObjectData(['_id': 'obj123', 'name': 'Test Object'])

        when:
        parseVarService.getVarList(["field1", "field2"], variableData, objectData, user, describe, button, true) >> variables
        metaDataFindService.findObjectDataByIds(user.getTenantId(), ["obj123"], "ObjectA") >> [queryObjectData]
        def result = argumentProcessorService.processOneFlowArguments(args, variableData, objectData, describe, user, button)

        then:
        result.size() == 2
        result[0].getRealValue() == queryObjectData
        result[1].getRealValue() == "test value"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processOneFlowArguments方法处理列表类型对象的场景
     */
    def "processOneFlowArgumentsTestListObjectType"() {
        given:
        def arg1 = createOneFlowArgListArg("param1", "\$fieldList\$", "list", "object", "ObjectB")
        def args = [arg1]
        def variableData = [:]
        def objectData = new ObjectData()
        def describe = Mock(IObjectDescribe)
        def user = User.systemUser('74255')
        def button = Mock(IUdefButton)

        def variable1 = createTestVariable("fieldList", ["obj1", "obj2"], "list")
        def variables = [variable1]

        def queryObjectData1 = new ObjectData(['_id': 'obj1', 'name': 'Object 1'])
        def queryObjectData2 = new ObjectData(['_id': 'obj2', 'name': 'Object 2'])

        when:
        parseVarService.getVarList(["fieldList"], variableData, objectData, user, describe, button, true) >> variables
        metaDataFindService.findObjectDataByIds(user.getTenantId(), ["obj1", "obj2"], "ObjectB") >> [queryObjectData1, queryObjectData2]
        def result = argumentProcessorService.processOneFlowArguments(args, variableData, objectData, describe, user, button)

        then:
        result.size() == 1
        result[0].getRealValue() instanceof List
        ((List) result[0].getRealValue()).size() == 2
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processOneFlowArguments方法处理JSON数组字符串的场景
     */
    def "processOneFlowArgumentsTestJsonArrayString"() {
        given:
        def arg1 = createOneFlowArgListArg("param1", "\$fieldList\$", "list", "object", "ObjectC")
        def args = [arg1]
        def variableData = [:]
        def objectData = new ObjectData()
        def describe = Mock(IObjectDescribe)
        def user = User.systemUser('74255')
        def button = Mock(IUdefButton)

        def jsonArrayString = '[{"id":"obj1"},{"id":"obj2"}]'
        def variable1 = createTestVariable("fieldList", jsonArrayString, "list")
        def variables = [variable1]

        def queryObjectData1 = new ObjectData(['_id': 'obj1', 'name': 'Object 1'])
        def queryObjectData2 = new ObjectData(['_id': 'obj2', 'name': 'Object 2'])

        when:
        parseVarService.getVarList(["fieldList"], variableData, objectData, user, describe, button, true) >> variables
        metaDataFindService.findObjectDataByIds(user.getTenantId(), ["obj1", "obj2"], "ObjectC") >> [queryObjectData1, queryObjectData2]
        def result = argumentProcessorService.processOneFlowArguments(args, variableData, objectData, describe, user, button)

        then:
        result.size() == 1
        result[0].getRealValue() instanceof List
        ((List) result[0].getRealValue()).size() == 2
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processOneFlowArguments方法处理查询异常的场景
     */
    def "processOneFlowArgumentsTestQueryException"() {
        given:
        def arg1 = createOneFlowArg("param1", "\$field1\$", "object", "ObjectA")
        def args = [arg1]
        def variableData = [:]
        def objectData = new ObjectData()
        def describe = Mock(IObjectDescribe)
        def user = User.systemUser('74255')
        def button = Mock(IUdefButton)

        def variable1 = createTestVariable("field1", "obj123", "object")
        def variables = [variable1]

        when:
        parseVarService.getVarList(["field1"], variableData, objectData, user, describe, button, true) >> variables
        metaDataFindService.findObjectDataByIds(user.getTenantId(), ["obj123"], "ObjectA") >> { throw new RuntimeException("Query failed") }
        def result = argumentProcessorService.processOneFlowArguments(args, variableData, objectData, describe, user, button)

        then:
        result.size() == 1
        result[0].getRealValue() == "obj123" // 保持原值，不会填充对象数据
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试collectObjectIds方法收集对象ID的场景
     */
    def "collectObjectIdsTest"() {
        given:
        def arg1 = createOneFlowArg("param1", "\$field1\$", "object", "ObjectA")
        arg1.setRealValue("obj123")

        def arg2 = createOneFlowArgListArg("param2", "\$field2\$", "list", "object", "ObjectB")
        arg2.setRealValue(["obj456", "obj789"])

        def args = [arg1, arg2]

        when:
        def result = argumentProcessorService.collectObjectIds(args)

        then:
        result.size() == 2
        result["ObjectA"] == ["obj123"]
        result["ObjectB"] == ["obj456", "obj789"]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试extractObjectId方法提取对象ID的不同场景
     */
    def "extractObjectIdTest"() {
        when:
        def result = argumentProcessorService.extractObjectId(value)

        then:
        result == expected

        where:
        value                            | expected
        "simple_string_id"               | "simple_string_id"
        ['id': 'map_id', 'name': 'test'] | "map_id"
        null                             | null
        123                              | null
        ['name': 'no_id']                | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试extractObjectIds方法提取对象ID列表的不同场景
     */
    def "extractObjectIdsTest"() {
        when:
        def result = argumentProcessorService.extractObjectIds(value)

        then:
        result == expected

        where:
        value                            | expected
        ["id1", "id2", "id3"]            | ["id1", "id2", "id3"]
        [['id': 'obj1'], ['id': 'obj2']] | ["obj1", "obj2"]
        '["id1","id2"]'                  | ["id1", "id2"]
        '[{"id":"obj1"},{"id":"obj2"}]'  | ["obj1", "obj2"]
        'invalid_json'                   | []
        null                             | []
        "not_a_list"                     | []
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchQueryObjectData方法批量查询对象数据的场景
     */
    def "batchQueryObjectDataTest"() {
        given:
        def user = User.systemUser('74255')
        def objectApiNameToIds = [
                "ObjectA": ["id1", "id2"],
                "ObjectB": ["id3"]
        ]

        def objectData1 = new ObjectData(['_id': 'id1', 'name': 'Object 1'])
        def objectData2 = new ObjectData(['_id': 'id2', 'name': 'Object 2'])
        def objectData3 = new ObjectData(['_id': 'id3', 'name': 'Object 3'])

        when:
        metaDataFindService.findObjectDataByIds(user.getTenantId(), ["id1", "id2"], "ObjectA") >> [objectData1, objectData2]
        metaDataFindService.findObjectDataByIds(user.getTenantId(), ["id3"], "ObjectB") >> [objectData3]
        def result = argumentProcessorService.batchQueryObjectData(user, objectApiNameToIds)

        then:
        result.size() == 2
        result["ObjectA"].size() == 2
        result["ObjectA"]["id1"] == objectData1
        result["ObjectA"]["id2"] == objectData2
        result["ObjectB"].size() == 1
        result["ObjectB"]["id3"] == objectData3
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillObjectData方法填充单个对象数据的场景
     */
    def "fillObjectDataTestSingleObject"() {
        given:
        def arg = createOneFlowArg("param1", "\$field1\$", "object", "ObjectA")
        arg.setRealValue("obj123")

        def objectData = new ObjectData(['id': 'obj123', 'name': 'Test Object'])
        def objectDataMap = [
                "ObjectA": ["obj123": objectData]
        ]

        when:
        argumentProcessorService.fillObjectData(arg, objectDataMap)

        then:
        arg.getRealValue() == objectData
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillObjectData方法填充列表对象数据的场景
     */
    def "fillObjectDataTestListObjects"() {
        given:
        def arg = createOneFlowArgListArg("param1", "\$field1\$", "list", "object", "ObjectB")
        arg.setRealValue(["obj1", "obj2", "obj3"])

        def objectData1 = new ObjectData(['id': 'obj1', 'name': 'Object 1'])
        def objectData2 = new ObjectData(['id': 'obj2', 'name': 'Object 2'])
        // obj3 不存在于查询结果中
        def objectDataMap = [
                "ObjectB": ["obj1": objectData1, "obj2": objectData2]
        ]

        when:
        argumentProcessorService.fillObjectData(arg, objectDataMap)

        then:
        arg.getRealValue() instanceof List
        def resultList = (List) arg.getRealValue()
        resultList.size() == 2 // 只有找到的对象
        resultList.contains(objectData1)
        resultList.contains(objectData2)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processOneFlowArguments方法处理空参数列表的场景
     */
    def "processOneFlowArgumentsTestEmptyArgs"() {
        given:
        def args = []
        def variableData = [:]
        def objectData = new ObjectData()
        def describe = Mock(IObjectDescribe)
        def user = User.systemUser('74255')
        def button = Mock(IUdefButton)

        when:
        def result = argumentProcessorService.processOneFlowArguments(args, variableData, objectData, describe, user, button)

        then:
        result.isEmpty()
    }

    /**
     * 创建测试用的BaseActionArg
     */
    private BaseActionArg createTestArg(String name, String value) {
        def arg = new TestActionArg()
        arg.setName(name)
        arg.setValue(value)
        return arg
    }

    /**
     * 创建测试用的OneFlowArg
     */
    private OneFlowArg createOneFlowArg(String name, String value, String type, String objectApiName) {
        def arg = new OneFlowArg()
        arg.setName(name)
        arg.setValue(value)
        arg.setType(type)
        arg.setObjectApiName(objectApiName)
        return arg
    }

    /**
     * 创建测试用的OneFlowArg（列表类型）
     */
    private OneFlowArg createOneFlowArgListArg(String name, String value, String type, String elementType, String elementObjectApiName) {
        def arg = new OneFlowArg()
        arg.setName(name)
        arg.setValue(value)
        arg.setType(type)
        arg.setElementType(elementType)
        arg.setElementObjectApiName(elementObjectApiName)
        return arg
    }

    /**
     * 创建测试用的Variable
     */
    private Variable createTestVariable(String variableName, Object value, String fieldType) {
        def variable = Mock(Variable)
        variable.getVariableName() >> variableName
        variable.getValue() >> value
        variable.getFieldType() >> fieldType
        return variable
    }

    /**
     * 测试用的BaseActionArg实现类
     */
    static class TestActionArg implements BaseActionArg {
        private String name
        private String type
        private String value
        private Object realValue

        @Override
        String getName() { return name }

        @Override
        void setName(String name) { this.name = name }

        @Override
        String getType() { return type }

        @Override
        void setType(String type) { this.type = type }

        @Override
        String getValue() { return value }

        @Override
        void setValue(String value) { this.value = value }

        @Override
        Object getRealValue() { return realValue }

        @Override
        void setRealValue(Object value) { this.realValue = value }
    }
} 