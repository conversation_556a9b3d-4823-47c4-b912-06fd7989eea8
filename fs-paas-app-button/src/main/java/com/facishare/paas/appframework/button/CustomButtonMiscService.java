package com.facishare.paas.appframework.button;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/04/09
 */
public interface CustomButtonMiscService {
    void checkParam(IUdefButton button, IObjectDescribe describe, IObjectData objectData, Map<String, Object> args, User user);
}
