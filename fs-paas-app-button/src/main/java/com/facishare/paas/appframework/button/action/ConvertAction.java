package com.facishare.paas.appframework.button.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by linqy on 2018/01/18.
 */
@Slf4j
@Component
public class ConvertAction implements ActionExecutor {

    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private MetaDataMiscService metaDataMiscService;

    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.CONVERT;
    }

    @Override
    public ButtonExecutor.Result invoke(ButtonExecutor.Arg arg, ActionExecutorContext context) {
        IObjectData objectData = arg.getObjectData();
        Map<String, List<IObjectData>> details = arg.toDetails();
        return startCustomButton(objectData, details, context.getUser(), arg,
                context.getAction(), context.getButton(), context.getDescribe());
    }

    public ButtonExecutor.Result startCustomButton(IObjectData objectData,
                                                   Map<String, List<IObjectData>> detailObjectData,
                                                   User user,
                                                   ButtonExecutor.Arg arg,
                                                   IUdefAction action,
                                                   IUdefButton button,
                                                   IObjectDescribe describe) {
        //获取映射规则
        IObjectMappingRuleInfo mappingRule = getMappingRule(user, action);
        if (Objects.isNull(mappingRule)) {
            throw new ValidateException(I18N.text(I18NKey.MAPPING_RULE_NOT_EXIST));
        }
        validateTargetApiName(user, mappingRule);

        //获取从对象数据
        Map<String, List<IObjectData>> details = Maps.newHashMap();
        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribes(user.getTenantId(), describe.getApiName());
        detailDescribes.forEach(detail -> {
            Optional<MasterDetailFieldDescribe> masterField = ObjectDescribeExt.of(detail).getMasterDetailFieldDescribe();
            masterField.ifPresent(m -> {
                if (Objects.equals(m.getIsCreateWhenMasterCreate(), Boolean.TRUE)) {
                    SearchTemplateQuery query = metaDataFindService.buildDetailSearchTemplateQuery(user, ObjectDescribeExt.of(detail), objectData);
                    List<IObjectData> detailObjectDataList = metaDataFindService
                            .findBySearchQuery(user, detail.getApiName(), query).getData();
                    details.put(detail.getApiName(), detailObjectDataList);
                }
            });
        });

        //不映射掩码字段
        removeMaskFieldValue(user, objectData, details, describe, detailDescribes);

        //构建arg
        ObjectMappingService.MappingDataArg mappingDataArg = new ObjectMappingService.MappingDataArg();
        mappingDataArg.setDetails(details);
        mappingDataArg.setRuleApiName(mappingRule.getRuleApiName());
        mappingDataArg.setObjectData(objectData);

        ObjectMappingService.MappingDataResult result = objectMappingService.mappingData(user, mappingDataArg);

        return ButtonExecutor.Result.builder()
                .details(result.getDetails())
                .objectData(result.getObjectData())
                .targetDescribeApiName(mappingRule.getTargetApiName())
                .build();
    }

    private void removeMaskFieldValue(User user, IObjectData objectData, Map<String, List<IObjectData>> details,
                                      IObjectDescribe describe, List<IObjectDescribe> detailDescribes) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(describe.getApiName(), describe);
        detailDescribes.forEach(x -> describeMap.put(x.getApiName(), x));
        metaDataMiscService.removeMaskFieldValue(user, objectData, details, describeMap);
    }

    //配置了隐藏单独入口的从对象不支持通过映射按钮新建
    private void validateTargetApiName(User user, IObjectMappingRuleInfo mappingRule) {
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), mappingRule.getTargetApiName());
        if (ObjectDescribeExt.of(describe).isSlaveObjectCreateWithMasterAndHiddenDetailButton()) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_MAPPING_SLAVE_OBJECT));
        }
    }

    /**
     * 获取映射规则
     *
     * @param user
     * @param action
     * @return
     */
    private IObjectMappingRuleInfo getMappingRule(User user, IUdefAction action) {
        String actionParameter = action.getActionParamter();
        ObjectMappingExt.ObjectMappingActionParam paramMap = JSON.parseObject(actionParameter, ObjectMappingExt.ObjectMappingActionParam.class);
        String ruleApiName = paramMap.getObjectMappingApiName();
        List<IObjectMappingRuleInfo> list = objectMappingService.findByApiName(user, ruleApiName);
        return CollectionUtils.empty(list) ? null : ObjectMappingExt.of(list);
    }
}
