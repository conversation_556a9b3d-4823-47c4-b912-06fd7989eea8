package com.facishare.paas.appframework.button;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.action.*;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.FieldManyMaxConfig;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateException;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.IRule;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.ButtonExt.ACTION_SOURCE_FLOW;

/**
 * Created by linqiuying on 2018/1/18.
 */

@Slf4j
@Service("customButtonExecutor")
public class CustomButtonExecutorImpl implements CustomButtonExecutor, CustomButtonMiscService, ButtonActionExecutor {
    @Autowired
    private MetaDataFindServiceImpl metaDataFindService;
    @Autowired
    private CustomButtonServiceImpl buttonService;
    @Autowired
    private PostActionServiceImpl actionService;
    @Autowired
    private ActionExecutorManager actionExecutorManager;
    @Autowired
    private DescribeLogicServiceImpl describeLogicService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private UserRoleInfoService userRoleInfoService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private MaskFieldLogicService maskFieldLogicService;
    @Autowired
    private UpdateFieldAction updateFieldAction;
    @Autowired
    private ValidateRuleService validateRuleService;

    /**
     * TODO: 从startCustomButton 拷贝而来，需要重构
     */
    @Override
    public ButtonExecutor.Result triggerValidationFunction(User user, ButtonExecutor.Arg arg) {
        ButtonExecutor.Result ret = ButtonExecutor.Result.empty();
        IUdefButton button = buttonService.findButtonByApiName(user, arg.getButtonApiName(), arg.getDescribeApiName());
        if (button == null) {
            return ret;
        }
        if (CollectionUtils.empty(button.getActions())) {
            return ret;
        }
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), arg.getDescribeApiName());
        IObjectData objectData = queryObjectData(user, arg);

        arg.setObjectData(objectData);

        ButtonExecutorContext executorContext = ButtonExecutorContext.builder()
                .stage(Strings.isNullOrEmpty(arg.getActionStage()) ? UdefActionExt.PRE : arg.getActionStage())
                .describe(describe)
                .button(button)
                .user(user)
                .build();

        return triggerFunctionAction(arg, executorContext);
    }

    @Override
    public ButtonExecutor.Result triggerFunctionAction(ButtonExecutor.Arg arg, ButtonExecutorContext context) {
        ButtonExecutor.Result ret = ButtonExecutor.Result.empty();
        IUdefButton button = context.getButton();
        User user = context.getUser();
        IObjectDescribe describe = context.getDescribe();
        IObjectData objectData = arg.getObjectData();
        if (button == null) {
            return ret;
        }
        if (CollectionUtils.empty(button.getActions())) {
            return ret;
        }
        List<IUdefAction> actionList = actionService.findActionListByStage(user, button, describe.getApiName(), context.getStage());
        if (CollectionUtils.empty(actionList)) {
            return ret;
        }
        checkParam(button, describe, objectData, arg.getArgs(), user);
        try {
            for (IUdefAction action : actionList) {
                //只有函数操作
                UdefActionExt actionExt = UdefActionExt.of(action);
                if (needFunctionAction(actionExt, arg.isSkipValidationFunction()) || actionExt.isCustomBizAction() || actionExt.isOneFlowAction()) {
                    ActionExecutor executor = actionExecutorManager.getActionExecutor(getActionType(context.getStage(), action.getActionType()));
                    // 执行验证函数
                    ButtonExecutor.Result result = executor.invoke(arg, ActionExecutorContext.of(context, action));
                    if (result != null) {
                        if (actionExt.isCustomFunctionAction() && isBlock(result)) {    // 一个action阻塞了就不执行了, 但是注意部分地方只支持一个APL函数(比如前验证)
                            return result;
                        }
                        ret = result;
                    }
                }
            }
        } catch (ValidateException | MetaDataBusinessException e) {
            log.warn("按钮执行异常，按钮apiName：{}，对象apiName：{},arg:{}", button.getApiName(), describe.getApiName(), JSON.toJSONString(arg), e);
            if (e instanceof ValidateException) {
                if (StringUtils.isNotEmpty(e.getMessage())) {
                    throw e;
                }
            }
            throw new FunctionException(I18N.text(I18NKey.FUNC_FAIL) + ":" + e.getMessage(), e.getErrorCode());
        }

        return ret;
    }

    @Override
    public ButtonExecutor.Result triggerValidateRule(ButtonExecutor.Arg arg, ButtonExecutorContext context) {
        ButtonExecutor.Result ret = ButtonExecutor.Result.empty();
        List<IUdefAction> actionList = actionService.findActionList(context.getUser(), context.getButton(), context.getDescribe().getApiName());
        if (CollectionUtils.empty(actionList)) {
            return ret;
        }
        validateRule(context.getButton(), context.getDescribe(), actionList, arg.getObjectData(), arg, context, ret);
        return ret;
    }

    private void validateRule(IUdefButton button, IObjectDescribe describe, List<IUdefAction> actionList,
                              IObjectData objectData, ButtonExecutor.Arg arg, ButtonExecutorContext context, ButtonExecutor.Result result) {
        String extendInfo = button.getExtendInfo();
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BUTTON_VALIDATE_RULE_GRAY_EI, context.getUser().getTenantId())
                || StringUtils.isBlank(extendInfo) || arg.isSkippedValidateRule()) {
            return;
        }
        ButtonExecutor.ExtendInfo info = JacksonUtils.fromJson(extendInfo, ButtonExecutor.ExtendInfo.class);
        if (Objects.isNull(info) || !info.isEnableValidateRule()) {
            return;
        }
        List<IUdefAction> updateActionList = actionList.stream().filter(x -> ActionExecutorType.UPDATES == getActionType(context.getStage(), x.getActionType())).collect(Collectors.toList());
        if (CollectionUtils.empty(updateActionList)) {
            return;
        }
        IObjectData objectDataCopy = ObjectDataExt.of(objectData).copy();

        Map<String, Object> updateFieldMap = Maps.newHashMap();

        for (IUdefAction action : updateActionList) {
            updateFieldMap.putAll(updateFieldAction.getUpdateFieldMap(describe, objectDataCopy, button, action, arg, context.getUser()));
        }
        if (updateFieldMap.isEmpty()) {
            return;
        }
        /**
         * 如果是流程调用，则不校验非阻断验证规则
         */
        if (ACTION_SOURCE_FLOW.equals(context.getActionSource())) {
            List<IRule> ruleList = validateRuleService.findRuleList(context.getUser().getTenantId(), describe.getApiName());
            List<IRule> enableBlockRuleList = ruleList.stream()
                    .filter(IRule::isActive)
                    .filter(x -> Boolean.TRUE.equals(x.getEnableBlocking()))
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(enableBlockRuleList)) {
                return;
            }
        }
        ObjectDataExt.of(objectDataCopy).putAll(updateFieldMap);
        RuleResult ruleResult = validateRuleService.validateRule(context.getUser(), IRule.UPDATE, describe,
                Lists.newArrayList(objectDataCopy), !ACTION_SOURCE_FLOW.equals(context.getActionSource()));
        if (ruleResult.isMatch()) {
            result.setValidateRuleResult(ruleResult);
        }
    }

    private boolean isBlock(ButtonExecutor.Result result) {
        if (RequestUtil.isCepRequest()) {
            return result.isHasReturnValue();
        }
        return result.isHasReturnValue() && result.isBlock();
    }

    private boolean needFunctionAction(UdefActionExt actionExt, boolean skipValidationFunction) {
        return actionExt.isCustomFunctionAction() && !skipValidationFunction;
    }

    @Override
    public ButtonExecutor.Result startCustomButton(ButtonExecutor.Arg arg, ButtonExecutorContext context) {
        ButtonExecutor.Result ret = ButtonExecutor.Result.empty();
        User user = context.getUser();
        IUdefButton button = context.getButton();
        IObjectDescribe describe = context.getDescribe();
        IObjectData objectData = arg.getObjectData();
        if (CollectionUtils.empty(button.getActions())) {
            return ret;
        }
        List<IUdefAction> actionList = actionService.findActionList(user, button, describe.getApiName());
        if (CollectionUtils.empty(actionList)) {
            return ret;
        }
        checkParam(button, describe, objectData, arg.getArgs(), user);
//        validateRule(button, describe, actionList, objectData, arg, context, ret);
//        if (Objects.nonNull(ret.getValidateRuleResult())) {
//            return ret;
//        }
        try {
            for (IUdefAction action : actionList) {
                if (UdefActionExt.PRE.equals(action.getStage())) {
                    continue;
                }
                ActionExecutor executor = actionExecutorManager.getActionExecutor(getActionType(context.getStage(), action.getActionType()));
                ButtonExecutor.Result result = executor.invoke(arg, ActionExecutorContext.of(context, action));
                if (result != null) {
                    ret = result;
                }
            }
        } catch (ValidateException | MetaDataBusinessException | FunctionException e) {
            log.warn("按钮执行异常，按钮apiName：{}，对象apiName：{},arg:{}", button.getApiName(), describe.getApiName(), JSON.toJSONString(arg), e);
            if (e instanceof FunctionException && e.getErrorCode() == AppFrameworkErrorCode.FUNCTION_USER_BIZ_ERROR.getCode()) {
                //函数中的业务异常属于用户通过Fx.message.throwErrorMessage抛出的异常直接提示即可。
                throw new ValidateException(e.getMessage(), e.getErrorCode());
            }
            throw new ValidateException(I18N.text(I18NKey.OPERATE_FAIL) + ":" + e.getMessage(), e.getErrorCode());
        } catch (ExpressionCalculateException e) {
            log.warn("按钮执行异常，按钮apiName：{}，对象apiName：{},arg:{}", button.getApiName(), describe.getApiName(), JSON.toJSONString(arg), e);
            throw new ValidateException(e.getMessage(), e.getErrorCode());
        } catch (Exception e) {
            log.error("按钮执行异常，按钮apiName：{}，对象apiName：{},arg:{}", button.getApiName(), describe.getApiName(), JSON.toJSONString(arg), e);
            throw new ValidateException(I18N.text(I18NKey.OPERATE_FAIL));
        }
        return ret;
    }

    private ActionExecutorType getActionType(String stage, String actionType) {
        return ActionExecutorType.getActionType(ButtonActionType.of(actionType), stage);
    }

    private IObjectData queryObjectData(User user, ButtonExecutor.Arg arg) {
        if (arg.getObjectData() == null && !Strings.isNullOrEmpty(arg.getObjectDataId())) {
            return metaDataFindService.findObjectData(user, arg.getObjectDataId(), arg.getDescribeApiName());
        }
        return arg.getObjectData();
    }

    //校验按钮的参数：必填字段需要
    @Override
    public void checkParam(IUdefButton button, IObjectDescribe describe, IObjectData objectData, Map<String, Object> args, User user) {
        List<Map> paramForm = button.getParamForm();
        if (CollectionUtils.empty(paramForm)) {
            return;
        }
        Set<String> readonlyFields = functionPrivilegeService.getReadonlyFields(user, describe.getApiName());
        List<IParamForm> paramFormList = ParamForm.fromList(paramForm);
        List<String> disableFieldDescribes = ObjectDescribeExt.of(describe).getDisableFieldDescribes().stream()
                .map(IFieldDescribe::getApiName).collect(Collectors.toList());
        paramFormList.removeIf(it -> Objects.equals(describe.getApiName(), it.getObjectApiName())
                && (!describe.containsField(it.convertToFieldApiName())
                || disableFieldDescribes.contains(it.convertToFieldApiName())));

        ButtonParamRender buttonParamRender = ButtonParamRender.builder()
                .data(objectData)
                .describe(describe)
                .user(user)
                .paramFormList(paramFormList)
                .orgService(orgService)
                .userRoleInfoService(userRoleInfoService)
                .maskFieldLogicService(maskFieldLogicService)
                .build()
                .initMaskFieldsWithParamFormList();
        if (buttonParamRender.isAllisShowMask()) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_UPDATE_MASK_FIELD));
        }

        String message = paramFormList.stream()
                // 临时处理，系统用户（-10000）请求的预置按钮，不校验按钮参数的必填项
                .filter(it -> isSystemButtonCustomParams(button, it, user))
                .filter(it -> isRequired(it, describe) && !isReadonly(it, readonlyFields))
                .filter(it -> ObjectDataExt.isValueEmpty(args.get(it.getApiName())) && !buttonParamRender.isShowMask(it.convertToFieldApiName()))
                .map(IParamForm::getLabel)
                .collect(Collectors.joining(","));

        if (!Strings.isNullOrEmpty(message)) {
            throw new ValidateException(I18N.text(I18NKey.FIELD_UST_FILL, message));
        }
        //2022/8/3 校验 人员（多选）、部门（多选）、查找关联（多选）
        String manyFieldMsg = outOfBoundsForManyField(paramFormList, describe, args);
        if (StringUtils.isNotEmpty(manyFieldMsg)) {
            throw new ValidateException(I18NExt.text(I18NKey.EMPLOYEE_DEPARTMENT_MANY_FIELD_BEYOND_MAX_LIMIT_V3, manyFieldMsg));
        }
    }

    /**
     * 系统用户（-10000）请求的预置按钮，不校验按钮参数的必填项
     *
     * @param button
     * @param paramForm
     * @param user
     * @return true paramForm 需要校验必填， false 不需要校验必填
     */
    private boolean isSystemButtonCustomParams(IUdefButton button, IParamForm paramForm, User user) {
        return !ButtonExt.of(button).isSystemButton()
//                || IObjectDescribe.DEFINE_TYPE_SYSTEM.equals(paramForm.getDefineType())
                || !user.isSupperAdmin();
    }

    private boolean isReadonly(IParamForm paramForm, Set<String> readonlyFields) {
        if (CollectionUtils.empty(readonlyFields)) {
            return false;
        }
        if (Strings.isNullOrEmpty(paramForm.getObjectApiName())) {
            return false;
        }
        return readonlyFields.contains(paramForm.convertToFieldApiName());
    }

    public boolean isRequired(IParamForm paramForm, IObjectDescribe describe) {
        if (paramForm.getIsRequired()) {
            return true;
        }
        if (Objects.equals(paramForm.getObjectApiName(), describe.getApiName())) {
            return ObjectDescribeExt.of(describe).getFieldDescribeSilently(StringUtils.replace(paramForm.getApiName(), "form_", ""))
                    .filter(it -> BooleanUtils.isTrue(it.isRequired()))
                    .isPresent();
        }
        return false;
    }

    public String outOfBoundsForManyField(List<IParamForm> params, IObjectDescribe describe, Map<String, Object> args) {
        return params.stream()
                .filter(paramForm -> StringUtils.equalsAny(paramForm.getType(), IFieldType.EMPLOYEE_MANY, IFieldType.DEPARTMENT_MANY, IFieldType.OBJECT_REFERENCE_MANY))
                .map(paramForm -> FieldManyMaxConfig.outBoundsForManyField(paramForm.getType(), paramForm.getLabel(), args.getOrDefault(paramForm.getApiName(), new ArrayList<>()), describe))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining(","));
    }

    /**
     * 如果callBack需要作废后执行button current action
     */
    public void doCurrentAction(Map<String, Object> callbackData, ActionContext actionContext, IObjectDescribe objectDescribe,
                                IObjectData data, Map<String, List<IObjectData>> details) {
        if (callbackData == null) {
            return;
        }
        Map<String, Object> buttonArgs = CollectionUtils.nullToEmpty((Map<String, Object>) callbackData.get("args"));
        //find button
        IUdefButton button = buttonService.findButtonByApiName(actionContext.getUser(), ObjectAction.INVALID.getButtonApiName(), objectDescribe);
        if (Objects.isNull(button)) {
            throw new ValidateException(I18NExt.text(I18NKey.BUTTON_IS_DISABLE_OR_DELETE));
        }
        //find button currentAction
        List<IUdefAction> allAction = actionService.findActionList(actionContext.getUser(), button, objectDescribe.getApiName());
        List<IUdefAction> currentActions = UdefActionExt.getActionMapForStage(allAction).get(UdefActionExt.CURRENT);
        if (CollectionUtils.empty(currentActions)) {
            return;
        }
        //build ButtonExecutor.Arg
        ButtonExecutor.Arg buttonExeArg = ButtonExecutor.Arg.of(data, details, buttonArgs);
        buttonExeArg.setSkipUpdateFieldActionValidation(true);
        //build ButtonExecutorContext
        ButtonExecutorContext buttonExeCtx = ButtonExecutorContext.builder()
                .user(actionContext.getUser())
                .describe(objectDescribe)
                .button(button)
                .build();
        //invoke UpdateFieldAction.invoke
        ActionExecutor executor = actionExecutorManager.getActionExecutor(ActionExecutorType.UPDATES);
        currentActions.forEach(action -> {
            ActionExecutorContext actionExeArg = ActionExecutorContext.of(buttonExeCtx, action);
            executor.invoke(buttonExeArg, actionExeArg);
        });
    }

}
