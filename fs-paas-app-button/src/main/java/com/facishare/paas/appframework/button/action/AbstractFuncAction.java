package com.facishare.paas.appframework.button.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.FunctionTimeoutException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.FuncBizExtendParam;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.QuoteValueService;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.SocketTimeoutException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2020/06/20
 */
@Slf4j
public abstract class AbstractFuncAction implements ActionExecutor {

    @Autowired
    protected FunctionLogicService functionLogicService;
    @Autowired
    protected ParseVarService parseVarService;
    @Autowired
    protected MetaDataMiscService metaDataMiscService;
    @Autowired
    protected DescribeLogicService describeLogicService;
    @Autowired
    protected QuoteValueService quoteValueService;
    @Autowired
    protected ArgumentProcessorService argumentProcessorService;


    protected ActionParameter getActionParameter(IUdefAction action) {
        return ActionParameter.fromJson(action.getActionParamter());
    }

    /**
     * 1. 准备参数
     * 2. 调用函数接口
     * 3. 处理返回结果
     * 4. 处理异常
     *
     * @param arg
     * @param context
     * @return
     */
    @Override
    public final ButtonExecutor.Result invoke(ButtonExecutor.Arg arg, ActionExecutorContext context) {
        User user = context.getUser();
        IObjectDescribe describe = context.getDescribe();
        IUdefButton button = context.getButton();
        if (needAsyncInvoke(user, describe.getApiName(), button.getApiName())) {
            if (log.isInfoEnabled()) {
                log.info("invoke async ei:{}, describeApiName:{}, buttonApiName:{}", user.getTenantId(), arg.getDescribeApiName(), arg.getButtonApiName());
            }
            arg.synchronizeData();
            ParallelUtils.createBackgroundTask()
                    .submit(() -> _invoke(arg, context))
                    .run();
            return null;
        }
        return _invoke(arg, context);
    }

    protected boolean needAsyncInvoke(User user, String describeApiName, String buttonApiName) {
        return false;
    }

    private ButtonExecutor.Result _invoke(ButtonExecutor.Arg arg, ActionExecutorContext context) {
        IUdefAction action = context.getAction();
        IObjectData objectData = arg.getObjectData();
        Map<String, List<IObjectData>> details = arg.toDetails();
        User user = context.getUser();
        IObjectDescribe describe = context.getDescribe();
        IUdefButton button = context.getButton();
        // 准备按钮参数
        ActionParameter actionParameter = getActionParameter(action);
        log.debug("startCustomButton objectData:{} details:{} actionParameter:{}", objectData, details, actionParameter);

        // 查询函数描述
        IUdefFunction udefFunction = functionLogicService.findUDefFunction(user, actionParameter.getFunctionAPIName(), describe.getApiName());
        if (Objects.isNull(udefFunction)) {
            throw new FunctionException(I18NExt.text(I18NKey.FUNCTION_DOES_NOT_EXIST));
        }
        try {
            initDetails(user, arg, details);
            // 处理函数入参
            Map<String, Object> functionArgMap = Maps.newHashMap();
            processFunctionArgs(actionParameter.getFunctionArgList(), arg.getArgs(), objectData, describe, user, button)
                    .forEach(x -> functionArgMap.put(x.getName(), x.getRealValue()));
            log.debug("executeUDefFunction function:{}, arg:{}", udefFunction, functionArgMap);
            // 执行函数
            RunResult runResult = executeFunction(arg, context, udefFunction, functionArgMap, details);
            log.info("executeUDefFunction function:{}, result:{}", udefFunction.getApiName(), runResult);
            // 校验执行结果
            validateResult(runResult);
            // 校验主对象数据
            validateObjectData(runResult, describe, user, context.getIgnoreFields());
            // 处理数据的合并
            handleData(user, objectData, runResult, context.getIgnoreFields());
            ObjectDataExt.correctValue(user, Lists.newArrayList(objectData), describe);
            // 处理返回结果
            return handleResult(user, arg, runResult);
        } catch (RuntimeException e) {
            log.warn("executeUDefFunction failed,tenantId:{},describeApiName:{},functionApiName:{},buttonApiName:{}",
                    user.getTenantId(), describe.getApiName(), actionParameter.getFunctionAPIName(), button.getApiName(), e);
            // 处理 异常信息
            return handleException(e, udefFunction);
        }
    }

    protected void initDetails(User user, ButtonExecutor.Arg arg, Map<String, List<IObjectData>> details) {
    }

    protected void validateObjectData(RunResult runResult, IObjectDescribe objectDescribe, User user, Collection<String> ignoreFields) {
    }

    protected void validateResult(RunResult runResult) {
        if (!runResult.isSuccess()) {
            if (runResult.getCode() == 400) {
                throw new FunctionException(runResult.getErrorInfo(), AppFrameworkErrorCode.FUNCTION_USER_BIZ_ERROR.getCode());
            }
            throw new FunctionException(runResult.getErrorInfo());
        }
    }

    protected void handleData(User user, IObjectData objectData, RunResult runResult, Collection<String> ignoreFields) {
    }

    protected ButtonExecutor.Result handleException(RuntimeException e, IUdefFunction udefFunction) {
        if (e instanceof RestProxyBusinessException) {
            throw new FunctionException(e.getMessage(), ((RestProxyBusinessException) e).getCode());
        }
        if (e instanceof FunctionException) {
            String msg = I18N.text(I18NKey.FUNC_FAIL) + e.getMessage();
            if (((FunctionException) e).getErrorCode() == AppFrameworkErrorCode.FUNCTION_USER_BIZ_ERROR.getCode()) {
                msg = e.getMessage();
            }
            throw new FunctionException(msg, ((FunctionException) e).getErrorCode());
        }
        Throwable rootCause = ExceptionUtils.getRootCause(e);
        if (rootCause instanceof SocketTimeoutException || rootCause instanceof FunctionTimeoutException) {
            throw new FunctionException(Optional.ofNullable(udefFunction).map(x -> x.getApiName()).orElse("") + I18N.text(I18NKey.FUNC_TIMEOUT));
        }
        throw new FunctionException(I18N.text(I18NKey.FUNC_FAIL));
    }

    protected ButtonExecutor.Result handleResult(User user, ButtonExecutor.Arg arg, RunResult runResult) {
        Object ret = runResult.getFunctionResult();
        ButtonExecutor.Result result = ButtonExecutor.Result.builder()
                .hasReturnValue(!Objects.isNull(ret) && !Strings.isNullOrEmpty(String.valueOf(ret)))
                .returnValue(Objects.isNull(ret) ? null : ret.toString())
                .build();
        return result;
    }

    protected RunResult executeFunction(ButtonExecutor.Arg arg, ActionExecutorContext context, IUdefFunction function,
                                        Map<String, Object> functionArgMap, Map<String, List<IObjectData>> details) {
        Map<String, Object> actionParams = Maps.newHashMap();
        //尽量控制影响范围，只有新建、编辑、修改相关团队，以及更换负责人，将改参数传递到map中，这4个按钮不允许配置按钮入参
        if (CollectionUtils.notEmpty(arg.getArgs())) {
            actionParams.putAll(arg.getArgs());
        }
        if (CollectionUtils.notEmpty(arg.getActionParams())) {
            actionParams.putAll(arg.getActionParams());
        }
        ActionExecutorType actionExecutorType = getType();
        String actionStage = getType() == null ? null : actionExecutorType.getActionStage();
        String buttonApiName = Optional.ofNullable(context.getButton()).map(IUdefButton::getApiName).orElse(null);
        // 暂不传 依赖前端传递参数
        FuncBizExtendParam.Arg funcBizExtendParamArg = makeFuncBizExtendParamArg(actionStage, buttonApiName, context);
        quoteValueService.fillQuoteValueVirtualField(context.getUser(), arg.getObjectData(), details);
        return functionLogicService.executeUDefFunction(context.getUser(), function, functionArgMap, arg.getObjectData(),
                details, getObjectRelatedData(arg), actionParams, funcBizExtendParamArg);
    }

    private FuncBizExtendParam.Arg makeFuncBizExtendParamArg(String actionStage, String buttonApiName, ActionExecutorContext context) {
        FuncBizExtendParam.Arg result = FuncBizExtendParam.Arg.builder()
                .apiName(buttonApiName)
                .actionStage(actionStage)
                .build();
        Map<String, Object> searchQuery = context.getSearchQuery();
        if (CollectionUtils.empty(searchQuery)) {
            return result;
        }

        result.setSearchQuery(searchQuery);
        return result;
    }

    private Map<String, List<IObjectData>> getObjectRelatedData(ButtonExecutor.Arg arg) {
        Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> relatedDataList = arg.getRelatedDataList();
        if (CollectionUtils.empty(relatedDataList)) {
            return Collections.emptyMap();
        }
        Map<String, List<IObjectData>> resultMap = Maps.newHashMap();
        relatedDataList.forEach((apiName, relatedData) -> {
            List<IObjectData> dataList = relatedData.stream()
                    .map(SaveMasterAndDetailData.RelatedObjectData::getDataList)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            resultMap.put(apiName, dataList);
        });
        return resultMap;
    }

    /**
     * 处理函数参数
     *
     * @deprecated 使用 {@link ArgumentProcessorService#processArguments} 代替
     */
    @Deprecated
    protected List<FunctionArg> processFunctionArgs(
            List<FunctionArg> args, Map<String, Object> variableData, IObjectData objectData, IObjectDescribe describe,
            User user, IUdefButton button) {
        return argumentProcessorService.processArguments(args, variableData, objectData, describe, user, button);
    }


    @Data
    public static class ActionParameter {
        @JSONField(name = "func_api_name")
        String functionAPIName;
        @JSONField(name = "func_args")
        List<CustomFuncAction.FunctionArg> functionArgList;
        @JSONField(name = "ui_event_id")
        String uiEventId;

        public static ActionParameter fromJson(String jsonStr) {
            return JSON.parseObject(jsonStr, ActionParameter.class);
        }
    }

    @Data
    public static class FunctionArg implements BaseActionArg {
        String name;
        String type;
        Object realValue;
        String value;
    }
}
