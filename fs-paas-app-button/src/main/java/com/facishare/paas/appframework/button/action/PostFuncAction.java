package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.dto.RunResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * create by z<PERSON><PERSON> on 2020/06/20
 */
@Slf4j
@Component
public class PostFuncAction extends AbstractFuncAction {

    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.POST_FUNCTION;
    }

    @Override
    protected void validateResult(RunResult runResult) {

    }

    @Override
    protected boolean needAsyncInvoke(User user, String describeApiName, String buttonApiName) {
        return AppFrameworkConfig.buttonPostFuncActionAsyncGray(user.getTenantId(), describeApiName, buttonApiName);
    }

    @Override
    protected ButtonExecutor.Result handleResult(User user, ButtonExecutor.Arg arg, RunResult runResult) {
        return null;
    }

}
