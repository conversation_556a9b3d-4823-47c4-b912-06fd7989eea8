package com.facishare.paas.appframework.button;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

public interface ButtonActionExecutor {

    void doCurrentAction(Map<String, Object> callbackData, ActionContext actionContext, IObjectDescribe objectDescribe,
                                IObjectData data, Map<String, List<IObjectData>> details) ;
}
