package com.facishare.paas.appframework.button;

import com.facishare.paas.appframework.button.action.ButtonExecutorContext;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.core.model.User;


/**
 * Created by linqy on 2018/01/11.
 * 按钮执行器
 */
public interface CustomButtonExecutor {

    /**
     * 触发验证函数
     *
     * @param user
     * @param arg
     * @return
     */
    ButtonExecutor.Result triggerValidationFunction(User user, ButtonExecutor.Arg arg);

    /**
     * 触发函数动作，支持触发 oneflow
     *
     * @param arg 按钮执行参数
     * @param context 按钮执行上下文
     * @return 执行结果
     */
    ButtonExecutor.Result triggerFunctionAction(ButtonExecutor.Arg arg, ButtonExecutorContext context);

    ButtonExecutor.Result triggerValidateRule(ButtonExecutor.Arg arg, ButtonExecutorContext context);

    /**
     * 触发自定义按钮
     *
     * @param arg
     * @param context
     * @return
     */
    ButtonExecutor.Result startCustomButton(ButtonExecutor.Arg arg, ButtonExecutorContext context);
}
