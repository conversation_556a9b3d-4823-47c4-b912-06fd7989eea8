package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.GlobalVarService;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager;
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * create by z<PERSON><PERSON> on 2019/11/05
 */
@Slf4j
@Data
public class ParseVarRender {

    private GlobalVarService globalVarService;
    private FieldDataConverterManager fieldDataConverterManager;
    private OrgService orgService;

    @NonNull
    private List<Variable> variables;
    @NonNull
    private User user;


    private transient Map<String, IGlobalVariableDescribe> globalVariables;
    private transient Map<String, Object> globalVarParseValueMap;

    @Builder
    public ParseVarRender(GlobalVarService globalVarService, FieldDataConverterManager fieldDataConverterManager, OrgService orgService, List<Variable> variables, User user) {
        this.globalVarService = globalVarService;
        this.fieldDataConverterManager = fieldDataConverterManager;
        this.orgService = orgService;
        this.variables = variables;
        this.user = user;
    }

    public void render() {
        Map<Variable.Type, List<Variable>> variableMap = variables.stream().collect(Collectors.groupingBy(Variable::getType));
        // 预处理全局变量
        pretreatmentGlobalVar(variableMap);
        pretreatmentButtonVariables(variableMap);
        variableMap.forEach(Variable.Type::handelVariable);
    }

    private void pretreatmentButtonVariables(Map<Variable.Type, List<Variable>> variableMap) {

    }

    private void pretreatmentGlobalVar(Map<Variable.Type, List<Variable>> variableMap) {
        if (Objects.isNull(globalVarService)) {
            return;
        }
        List<String> globalVars = CollectionUtils.nullToEmpty(variableMap.get(Variable.Type.GLOBAL_CONSTANT)).stream()
                .map(Variable::getVariableName).collect(Collectors.toList());
        if (CollectionUtils.empty(globalVars)) {
            return;
        }
        globalVariables = globalVarService.findGlobalVariables(user.getTenantId(), globalVars);
        globalVarParseValueMap = batchParseValue(globalVariables.values());
    }

    private Map<String, Object> batchParseValue(Collection<IGlobalVariableDescribe> globalVariableDescribeList) {
        Map<String, Object> ret = Maps.newHashMap();
        for (IGlobalVariableDescribe x : globalVariableDescribeList) {
            ret.put(x.getApiName(), globalVarService.parseValue(x));
        }
        return ret;
    }
}
