package com.facishare.paas.appframework.button.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by linqiuying on 2018/1/25.
 */
@Data
public class UpdatesPojo {
    private List<Field> fields;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Field{
        private String field;

        private Object value;

        private boolean default_to_zero;

        private String var_type;

        private String return_type;

        private int decimal_places;

        private String other_value;
    }
}
