package com.facishare.paas.appframework.button.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.button.dto.SendEmailPojo;
import com.facishare.paas.appframework.common.service.SendEmailProxy;
import com.facishare.paas.appframework.common.service.dto.SendEmailModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Created by linqy on 2018/01/18.
 * 发邮件
 */
@Slf4j
@Component
public class SendEmailAction implements ActionExecutor {
    @Autowired
    private SendEmailProxy sendEmailProxy;
    @Autowired
    private GetEmployeeManager getEmployeeManager;
    @Autowired
    private ParseVarService parseVarService;

    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.SEND_MAIL;
    }

    @Override
    public ButtonExecutor.Result invoke(ButtonExecutor.Arg arg, ActionExecutorContext context) {
        Map<String, List<IObjectData>> details = arg.toDetails();
        IObjectData objectData = arg.getObjectData();
        return startCustomButton(objectData, details, context.getUser(), arg, context.getAction(), context.getButton(),
                context.getDescribe());
    }

    private ButtonExecutor.Result startCustomButton(IObjectData objectData,
                                                    Map<String, List<IObjectData>> details,
                                                    User user,
                                                    ButtonExecutor.Arg arg,
                                                    IUdefAction action,
                                                    IUdefButton button,
                                                    IObjectDescribe describe) {
        SendEmailPojo sendEmailPojo = getSendEmailPojo(action);
        SendEmailModel.Arg sendArg = SendEmailModel.Arg.builder()
                .email_list(getEmailAddress(user, sendEmailPojo, arg, objectData, describe, button))
                .obj_api_name(describe.getApiName())
                .obj_data_id(objectData.getId())
                .template_id(sendEmailPojo.getTemplate())
                .userid_list(getReceiveIds(user, sendEmailPojo, arg, objectData, describe, button))
                .forCalc(true)
                .build();
        Map<String, String> headers = RestUtils.buildSendEmailHeaders(user);
        SendEmailModel.Result result = sendEmailProxy.sendEmail(headers, sendArg);
        return null;
    }

    private SendEmailPojo getSendEmailPojo(IUdefAction action) {
        String actionParameter = action.getActionParamter();
        SendEmailPojo pojo = JSON.parseObject(actionParameter, SendEmailPojo.class);
        return pojo;
    }

    private Set<String> getReceiveIds(User user, SendEmailPojo pojo, ButtonExecutor.Arg arg, IObjectData objectData, IObjectDescribe describe, IUdefButton button) {
        Map<String, List<String>> recipients = pojo.getRecipients();
        Set<String> keySet = recipients.keySet();
        Set<String> varReceiveIds = Sets.newHashSet();
        List<String> varEmployee = Lists.newArrayList();
        Map<String, List<String>> varRecipients = Maps.newHashMap();
        // 处理变量中的数据
        if (keySet.contains("vars")) {
            List<String> var = recipients.get("vars");
            List<Variable> varList = parseVarService.getVarList(var, arg.getArgs(), objectData, user, describe, button);
            varList.forEach(x -> {
                String fieldType = x.getFieldType();
                if (IFieldType.EMPLOYEE.equals(fieldType) && x.getValue() != null) {
                    varReceiveIds.addAll((ArrayList<String>) x.getValue());
                }
                if (IFieldType.DEPARTMENT.equals(fieldType) && x.getValue() != null) {
                    varEmployee.addAll((ArrayList<String>) x.getValue());
                }
            });
            if (CollectionUtils.notEmpty(varEmployee)) {
                varRecipients.put("dept", varEmployee);
                Set<String> varDepartmentEmployees = getEmployeeManager.getReceives(user, describe.getApiName(), objectData.getId(), varRecipients);
                varReceiveIds.addAll(varDepartmentEmployees);
            }
            recipients.remove("vars");//去掉变量，变量做特殊处理
        }
        // 处理除变量以外的数据
        Set<String> receiveIds = getEmployeeManager.getReceives(user, describe.getApiName(), objectData.getId(), recipients);
        receiveIds.addAll(varReceiveIds);
        return receiveIds;
    }

    private Set<String> getEmailAddress(User user, SendEmailPojo sendEmailPojo, ButtonExecutor.Arg arg, IObjectData objectData, IObjectDescribe describe, IUdefButton button) {

        Set<String> emailAddress = new HashSet<>();
        emailAddress.addAll(sendEmailPojo.getEmail_address());

        if (CollectionUtils.empty(sendEmailPojo.getRecipients()) || !sendEmailPojo.getRecipients().containsKey("vars") ||
                CollectionUtils.empty(sendEmailPojo.getRecipients().get("vars"))) {
            return emailAddress;
        }

        List<Variable> varList = parseVarService.getVarList(sendEmailPojo.getRecipients().get("vars"),
                arg.getArgs(), objectData, user, describe, button);
        varList.forEach(variable -> {
            if (IFieldType.EMAIL.equals(variable.getFieldType())) {
                emailAddress.add(replaceVariableName(variable));
            }
        });

        return emailAddress;
    }

    private String replaceVariableName(Variable variable) {
        String fieldName = variable.getVariableName().replace("$", "");
        Object email = variable.getData().get(fieldName);
        return email == null ? null : String.valueOf(email);
    }


}
