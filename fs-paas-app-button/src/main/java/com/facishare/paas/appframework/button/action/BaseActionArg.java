package com.facishare.paas.appframework.button.action;

/**
 * 基础参数接口，用于统一参数处理
 */
public interface BaseActionArg {
  /**
   * 获取参数名称
   */
  String getName();

  /**
   * 设置参数名称
   */
  void setName(String name);

  /**
   * 获取参数类型
   */
  String getType();

  /**
   * 设置参数类型
   */
  void setType(String type);

  /**
   * 获取参数值表达式
   */
  String getValue();

  /**
   * 设置参数值表达式
   */
  void setValue(String value);

  /**
   * 获取实际参数值
   */
  Object getRealValue();

  /**
   * 设置实际参数值
   */
  void setRealValue(Object value);
}
