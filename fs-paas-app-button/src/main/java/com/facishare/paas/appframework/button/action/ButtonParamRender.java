package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.UpdatesPojo;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds.MainDeptInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.IParamForm;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2020/03/12
 */
@Data
@Slf4j
public class ButtonParamRender {
    private List<IParamForm> paramFormList;
    private List<UpdatesPojo.Field> fields;
    private IObjectData data;
    private IObjectDescribe describe;
    private User user;

    private UserRoleInfoService userRoleInfoService;
    private MaskFieldLogicService maskFieldLogicService;
    private OrgService orgService;

    private Map<String, IFieldDescribe> maskFieldMap;

    @Builder
    private ButtonParamRender(List<IParamForm> paramFormList, List<UpdatesPojo.Field> fields, IObjectData data,
                              IObjectDescribe describe, User user,
                              UserRoleInfoService userRoleInfoService, OrgService orgService,
                              MaskFieldLogicService maskFieldLogicService) {
        this.paramFormList = paramFormList;
        this.fields = fields;
        this.data = data;
        this.describe = describe;
        this.user = user;
        this.userRoleInfoService = userRoleInfoService;
        this.orgService = orgService;
        this.maskFieldLogicService = maskFieldLogicService;
    }

    public ButtonParamRender initMaskFieldsWithParamFormList() {
        maskFieldMap = paramFormList.stream()
                .filter(x -> describe.getApiName().equals(x.getObjectApiName()))
                .map(IParamForm::convertToFieldApiName)
                .filter(describe::containsField)
                .map(describe::getFieldDescribe)
                .filter(x -> FieldDescribeExt.of(x).isShowMask())
                .collect(Collectors.toMap(IFieldDescribe::getApiName, x -> x));
        return this;
    }

    public ButtonParamRender initMaskFieldsWithUpdatesPojoFields() {
        maskFieldMap = fields.stream()
                .map(UpdatesPojo.Field::getField)
                .filter(describe::containsField)
                .map(describe::getFieldDescribe)
                .filter(x -> FieldDescribeExt.of(x).isShowMask())
                .collect(Collectors.toMap(IFieldDescribe::getApiName, x -> x));
        return this;
    }


    public boolean isShowMask(String fieldApiName) {
        if (CollectionUtils.empty(maskFieldMap) || !maskFieldMap.containsKey(fieldApiName)) {
            return false;
        }
        // 超级管理员不展示掩码
        if (user.isSupperAdmin()) {
            return false;
        }
        IFieldDescribe fieldDescribe = maskFieldMap.get(fieldApiName);
        List<IFieldDescribe> fieldDescribes = maskFieldLogicService.maskFieldRoleFilter(user, Lists.newArrayList(fieldDescribe));
        if (CollectionUtils.empty(fieldDescribes)) {
            return false;
        }
        // 下游人员展示掩码
        if (user.isOutUser()) {
            if (!FieldDescribeExt.of(fieldDescribe).isRemoveMaskForOutOwner()) {
                return true;
            }
            String outOwnerId = ObjectDataExt.of(data).getOutOwnerId().orElse(null);
            return StringUtils.isBlank(outOwnerId) || !StringUtils.equals(user.getUserId(), outOwnerId);
        }
        // CRM管理员展示掩码
        if (userRoleInfoService.isAdmin(user)) {
            return false;
        }
        // 数据没有负责人则统一展示掩码
        String ownerId = ObjectDataExt.of(data).getOwnerId().orElse(null);
        if (StringUtils.isBlank(ownerId)) {
            return true;
        }
        // 配置了负责人去掩码
        if (FieldDescribeExt.of(fieldDescribe).isRemoveMaskForOwner() && StringUtils.equals(user.getUserId(), ownerId)) {
            return false;
        }
        // 配置了主属部门负责人去掩码
        if (FieldDescribeExt.of(fieldDescribe).isRemoveMaskForDataOwnerMainDeptLeader()) {
            Map<String, MainDeptInfo> mainDeptInfoMap = orgService.getMainDeptInfo(user.getTenantId(),
                    user.getUserId(), Lists.newArrayList(ownerId));
            if (CollectionUtils.notEmpty(maskFieldMap) && mainDeptInfoMap.containsKey(ownerId)) {
                MainDeptInfo deptInfo = mainDeptInfoMap.get(ownerId);
                return !StringUtils.isNotEmpty(deptInfo.getLeaderId()) || !StringUtils.equals(user.getUserId(), deptInfo.getLeaderId());
            }
        }
        return true;
    }

    public boolean isAllisShowMask() {
        if (CollectionUtils.empty(paramFormList)) {
            return false;
        }
        return paramFormList.stream().allMatch(it -> isShowMask(it.convertToFieldApiName()));
    }
}
