package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.CustomBizButtonExecutor;
import com.facishare.rest.core.annotation.*;
import lombok.Data;

import java.util.Map;

@RestResource(
        value = "CUSTOM_BIZ_ACTION",
        desc = "业务自定义action", // ignoreI18n
        codec = "com.facishare.paas.appframework.common.service.codec.AppDefaultCodeC",
        contentType = "application/json")
public interface CustomBizActionProxy {
    @POST(desc = "执行自定义业务规则", contentType = "application/json", socketReadTimeoutSecond = 1000)
    ButtonActionResponse startCustomBizButton(@Body CustomBizButtonExecutor.Arg arg, @ServiceURLParam String url, @HeaderMap Map<String, String> header);

    @Data
    class ButtonActionResponse {
        private CustomBizButtonExecutor.Result result;
        private String errMessage;
        private int errCode;
    }
}
