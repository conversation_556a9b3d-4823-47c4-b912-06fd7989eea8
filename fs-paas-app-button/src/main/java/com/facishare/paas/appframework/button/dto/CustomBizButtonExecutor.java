package com.facishare.paas.appframework.button.dto;

import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface CustomBizButtonExecutor {

    @Data
    @Builder
    class Arg {
        @SerializedName("describeApiName")
        private String describeApiName;

        @SerializedName("objectDataId")
        private String objectDataId;

        private List<String> dataIds;

        @SerializedName("buttonApiName")
        private String buttonApiName;

        @SerializedName("args")
        private Map<String, Object> args;

        @SerializedName("objectData")
        private Map<String, Object> objectData;

        @SerializedName("details")
        Map<String, List<Map<String, Object>>> details;

        //业务方自定义的key
        @SerializedName("bizKey")
        String bizKey;

        private String actionStage;

        private Map<String, Object> actionParams;

        public static CustomBizButtonExecutor.Arg of(
                String describeApiName,
                String objectDataId,
                List<String> dataIds,
                String buttonApiName,
                Map<String, Object> args,
                IObjectData objectData,
                Map<String, List<IObjectData>> details,
                String bizKey,
                String actionStage,
                Map<String, Object> actionParams
        ) {
            return builder()
                    .describeApiName(describeApiName)
                    .objectDataId(objectDataId)
                    .dataIds(dataIds)
                    .buttonApiName(buttonApiName)
                    .objectData(ObjectDataExt.of(objectData).toMap())
                    .details(ObjectDataExt.convertData2Map(details))
                    .args(args)
                    .actionParams(actionParams)
                    .bizKey(bizKey)
                    .actionStage(actionStage)
                    .build();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private Map<String, Object> objectData;
        private Map<String, List<Map<String, Object>>> details;
        private String targetDescribeApiName;
        private boolean hasReturnValue;
        private Object returnValue;
        private String returnType;
        /**
         * 默认为阻断
         */
        @Builder.Default
        private boolean block = true;

        public static ButtonExecutor.Result empty() {
            return ButtonExecutor.Result.builder().build();
        }

        public IObjectData toObjectData() {
            if (Objects.isNull(objectData)) {
                return null;
            }
            return ObjectDataExt.of(objectData).getObjectData();
        }

        public Map<String, List<IObjectData>> toDetails() {
            if (Objects.isNull(details)) {
                return Maps.newHashMap();
            }
            return ObjectDataExt.convertMap2Data(details);
        }

    }
}
