package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import java.util.Collection;
import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2020/06/20
 */
@Data
public class ActionExecutorContext {
    @NonNull
    private User user;
    @NonNull
    private IObjectDescribe describe;
    @NonNull
    private IUdefButton button;
    @NonNull
    private IUdefAction action;

    private Collection<String> ignoreFields;

    private Map<String, Object> searchQuery;

    @Builder
    private ActionExecutorContext(User user, IObjectDescribe describe, IUdefButton button, IUdefAction action, Collection<String> ignoreFields, Map<String, Object> searchQuery) {
        this.user = user;
        this.describe = describe;
        this.button = button;
        this.action = action;
        this.ignoreFields = ignoreFields;
        this.searchQuery = searchQuery;
    }

    public static ActionExecutorContext of(ButtonExecutorContext context, IUdefAction action) {
        return builder().button(context.getButton())
                .describe(context.getDescribe())
                .user(context.getUser())
                .ignoreFields(context.getIgnoreFields())
                .action(action)
                .searchQuery(context.getSearchQuery())
                .build();
    }
}
