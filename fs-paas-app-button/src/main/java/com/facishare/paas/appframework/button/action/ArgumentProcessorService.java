package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

/**
 * 参数处理服务接口，用于处理各种类型的参数
 */
public interface ArgumentProcessorService {

    /**
     * 处理参数列表，将变量替换为实际值
     *
     * @param args 参数列表
     * @param variableData 变量数据
     * @param objectData 对象数据
     * @param describe 对象描述
     * @param user 用户信息
     * @param button 按钮信息
     * @param <T> 参数类型
     * @return 处理后的参数列表
     */
    <T extends BaseActionArg> List<T> processArguments(
            List<T> args, Map<String, Object> variableData, IObjectData objectData, IObjectDescribe describe,
            User user, IUdefButton button);

    /**
     * 处理参数列表，将变量替换为实际值，并可选择性地进行数据类型转换
     *
     * @param args 参数列表
     * @param variableData 变量数据
     * @param objectData 对象数据
     * @param describe 对象描述
     * @param user 用户信息
     * @param button 按钮信息
     * @param convertDataType 是否根据Variable.fieldType进行数据类型转换
     * @param <T> 参数类型
     * @return 处理后的参数列表
     */
    <T extends BaseActionArg> List<T> processArguments(
            List<T> args, Map<String, Object> variableData, IObjectData objectData, IObjectDescribe describe,
            User user, IUdefButton button, boolean convertDataType);

    /**
     * 处理OneFlow参数列表，将变量替换为实际值并执行必要的类型转换
     *
     * @param args OneFlow参数列表
     * @param variableData 变量数据
     * @param objectData 对象数据
     * @param describe 对象描述
     * @param user 用户信息
     * @param button 按钮信息
     * @param <T> 参数类型
     * @return 处理后的OneFlow参数列表
     */
    <T extends BaseActionArg> List<T> processOneFlowArguments(
            List<T> args, Map<String, Object> variableData, IObjectData objectData, IObjectDescribe describe,
            User user, IUdefButton button);
}
