package com.facishare.paas.appframework.button.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.google.common.base.Strings;

import java.util.Map;
import java.util.Objects;

public abstract class UIActionFuncAction extends AbstractFuncAction {

    protected static final String WEB_ACTION = "WebAction";
    protected static final String APP_ACTION = "AppAction";
    protected static final String UI_ACTION_RETURN_TYPE = "UIAction";
    protected static final String ALERT_ACTION = "AlertAction";
    public static final String UI_ACTION_TYPE = "action";

    @Override
    protected ButtonExecutor.Result handleResult(User user, ButtonExecutor.Arg arg, RunResult runResult) {
        Object ret = runResult.getFunctionResult();
        String returnType = runResult.getReturnType();

        boolean returnByUIAction = returnByUIAction(returnType);
        if (!returnByUIAction && !Objects.isNull(ret)) {
            ret = ret.toString();
        } else {
            checkClientVersionAndSupportType(ret);
        }

        ButtonExecutor.Result result = ButtonExecutor.Result.builder()
                .hasReturnValue(!Objects.isNull(ret) && !Strings.isNullOrEmpty(String.valueOf(ret)))
                .returnType(returnType)
                .returnValue(ret)
                .build();
        return result;
    }

    private void checkClientVersionAndSupportType(Object returnValue) {
        if (!(returnValue instanceof Map)) {
            return;
        }

        Map uiAction = (Map) returnValue;
        Object action = uiAction.get(UI_ACTION_TYPE);
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_740)) {
            throw new FunctionException(I18N.text(I18NKey.FUNCTION_UIACTION_CLIENT_UPGRADE));
        }
        if (isMobileRequest() && Objects.equals(WEB_ACTION, action)) {
            throw new FunctionException(I18N.text(I18NKey.FUNCTION_UIACTION_NOT_SUPPORT, action));
        }
        if (isWebRequest() && Objects.equals(APP_ACTION, action)) {
            throw new FunctionException(I18N.text(I18NKey.FUNCTION_UIACTION_NOT_SUPPORT, action));
        }
    }

    public boolean isMobileRequest() {
        return RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest();
    }

    public boolean isWebRequest() {
        return !isMobileRequest();
    }

    private boolean returnByUIAction(String returnType) {
        return Objects.equals(UI_ACTION_RETURN_TYPE, returnType);
    }
}
