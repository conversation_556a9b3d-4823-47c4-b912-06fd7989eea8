package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 通过spring来管理数据后动作执行器
 * <p>
 * Created by linqy on 2018/01/18.
 */
@Slf4j
@Component
public class ActionExecutorManager implements ApplicationContextAware {

    private Map<ActionExecutorType, ActionExecutor> actionExecutors = Maps.newHashMap();

    @Autowired
    private UpdateFieldAction updateFieldAction;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            Map<String, ActionExecutor> springMap = applicationContext.getBeansOfType(ActionExecutor.class);
            if (CollectionUtils.notEmpty(springMap)) {
                springMap.forEach((x, y) -> actionExecutors.put(y.getType(), y));
            }
        } catch (BeansException e) {
            log.error("init ActionExecutor error", e);
        }
    }

    public ActionExecutor getActionExecutor(ActionExecutorType type) {
        return actionExecutors.getOrDefault(type, updateFieldAction);
    }
}
