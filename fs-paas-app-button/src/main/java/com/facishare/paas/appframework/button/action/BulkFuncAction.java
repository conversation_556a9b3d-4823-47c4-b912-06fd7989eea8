package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.function.dto.BatchDataExecuteFunction;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2020/06/20
 */
@Slf4j
@Component
public class BulkFuncAction extends UIActionFuncAction {

    @Override
    public ActionExecutorType getType() {
        return ActionExecutorType.BULK_FUNCTION;
    }

    @Override
    protected RunResult executeFunction(ButtonExecutor.Arg arg, ActionExecutorContext context, IUdefFunction function, Map<String, Object> functionArgMap, Map<String, List<IObjectData>> details) {
        BatchDataExecuteFunction.Result result = functionLogicService.batchDataExecuteFunction(context.getUser(), function, arg.getDataIds(), functionArgMap);

        return RunResult.builder()
                .success(result.isSuccess())
                .functionResult(result.getFunctionResult())
                .errorInfo(result.getErrorInfo())
                .returnType(result.getReturnType())
                .build();
    }
}
