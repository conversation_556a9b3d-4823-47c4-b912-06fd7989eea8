package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.IParamForm;
import com.facishare.paas.appframework.metadata.ParamForm;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * create by z<PERSON><PERSON> on 2019/11/05
 */
public class Variable {
    private Type type;
    private String variableName;
    private String fieldType; //字段类型
    private Object value;     //字段的值
    private IObjectData data;
    private IUdefButton button;
    private IObjectDescribe describe;
    private Map<String, Object> args;
    private String apiName;//被计算的字段的apiName

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public IUdefButton getButton() {
        return button;
    }

    public void setButton(IUdefButton button) {
        this.button = button;
    }

    public IObjectData getData() {
        return data;
    }

    public void setData(IObjectData data) {
        this.data = data;
    }

    public IObjectDescribe getDescribe() {
        return describe;
    }

    public void setDescribe(IObjectDescribe describe) {
        this.describe = describe;
    }

    public Map<String, Object> getArgs() {
        return args;
    }

    public void setArgs(Map<String, Object> args) {
        this.args = args;
    }

    public Type getType() {
        return type;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public String getVariableName() {
        return variableName;
    }

    public void setVariableName(String variableName) {
        this.variableName = variableName;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public Variable(String apiName, String variableName, IObjectDescribe describe, IObjectData objectData, IUdefButton button, Map<String, Object> args) {
        this.apiName = apiName;
        this.variableName = variableName;
        this.type = Type.getByName(variableName, button);
        this.describe = describe;
        this.data = objectData;
        this.button = button;
        this.args = args;
    }

    public Variable(String variableName, IObjectDescribe describe, IObjectData objectData, IUdefButton button, Map<String, Object> args) {
        this.variableName = variableName;
        this.type = Type.getByName(variableName, button);
        this.describe = describe;
        this.data = objectData;
        this.button = button;
        this.args = args;
    }

    public enum Type {
        OBJECT_FIELD {
            @Override
            protected void handelVariable(List<Variable> variables) {

            }
        },
        VAR_FIELD {
            @Override
            protected void handelVariable(List<Variable> variables) {

            }
        }, //目前支持两级
        GLOBAL_CONSTANT {
            @Override
            protected void handelVariable(List<Variable> variables) {

            }
        },
        BUTTON_VARIABLES {
            @Override
            protected void handelVariable(List<Variable> variables) {

            }
        },
        CURRENT_OBJECT {
            @Override
            protected void handelVariable(List<Variable> variables) {

            }
        },
        ;

        protected abstract void handelVariable(List<Variable> variables);

        public static Type getByName(String name, IUdefButton button) {
            // 当前对象变量
            if ("__current_object__".equals(StringUtils.replace(name, "$", ""))) {
                return CURRENT_OBJECT;
            }
            // 全局变量
            if (StringUtils.endsWith(StringUtils.replace(name, "$", ""), "__g")) {
                return GLOBAL_CONSTANT;
            }
            // 按钮自定义表单入参
            if (StringUtils.startsWith(StringUtils.replace(name, "$", ""), "form_")) {
                return VAR_FIELD;
            }
            // 按钮变量 var_executor var_execution_time
            if (StringUtils.startsWith(StringUtils.replace(name, "$", ""), "var_")) {
                return BUTTON_VARIABLES;
            }
            // 特殊 处理「ExtendExpireTime 延期」预置按钮的参数绑定
            if (ObjectAction.EXTEND_EXPIRETIME.getButtonApiName().equals(button.getApiName())) {
                List<IParamForm> paramForms = ParamForm.fromList(button.getParamForm());
                if (paramForms.stream().anyMatch(it -> Objects.equals(StringUtils.replace(name, "$", ""), it.getApiName()))) {
                    return VAR_FIELD;
                }
            }
            // 对象下字段
            return OBJECT_FIELD;
        }

    }
}
