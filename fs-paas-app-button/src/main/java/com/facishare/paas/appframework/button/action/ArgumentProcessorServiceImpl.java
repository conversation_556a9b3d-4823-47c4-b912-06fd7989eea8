package com.facishare.paas.appframework.button.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 参数处理服务实现类，用于处理各种类型的参数
 */
@Slf4j
@Component
public class ArgumentProcessorServiceImpl implements ArgumentProcessorService {

    @Autowired
    private ParseVarService parseVarService;

    @Autowired
    private MetaDataFindService metaDataFindService;

    @Override
    public <T extends BaseActionArg> List<T> processArguments(
            List<T> args, Map<String, Object> variableData, IObjectData objectData, IObjectDescribe describe,
            User user, IUdefButton button) {
        // 默认不进行数据类型转换
        return processArguments(args, variableData, objectData, describe, user, button, false);
    }

    @Override
    public <T extends BaseActionArg> List<T> processArguments(
            List<T> args, Map<String, Object> variableData, IObjectData objectData, IObjectDescribe describe,
            User user, IUdefButton button, boolean convertDataType) {
        if (CollectionUtils.empty(args)) {
            return Collections.emptyList();
        }

        // Extract all variable names
        List<String> vars = args.stream()
                .filter(x -> Objects.nonNull( x.getValue()))
                .map(x -> x.getValue().replace("$", ""))
                .distinct()
                .collect(Collectors.toList());

        // Build variable mapping
        Map<String, String> varsMap = Maps.newHashMap();
        args.forEach(x -> {
            if (Objects.isNull(x.getValue())) {
                return;
            }
            varsMap.put(x.getValue().replace("$", ""), x.getValue());
        });

        // Get variable values
        Map<String, Variable> variableMap = Maps.newHashMap();
        parseVarService.getVarList(vars, variableData, objectData, user, describe, button, true).forEach(x -> {
            variableMap.put(varsMap.get(x.getVariableName()), x);
        });
        log.debug("processArguments variables:{}", variableMap);

        // Set actual values
        args.forEach(x -> {
            Variable variable = variableMap.get(x.getValue());
            if (variable != null) {
                Object value = variable.getValue();

                // 如果需要进行数据类型转换
                if (convertDataType && value != null) {
                    value = convertValueByFieldType(x.getValue(), variable.getFieldType(), value);
                }

                x.setRealValue(value);
            }
        });

        return args;
    }

    /**
     * 根据字段类型转换值的数据格式
     * 参考NumberValidator.java的验证逻辑
     *
     * @param value 原始值
     * @param fieldType 字段类型
     * @return 转换后的值
     */
    private Object convertValueByFieldType(String fieldApiName, String fieldType, Object value) {
        if (Objects.isNull(value) || Objects.isNull(fieldType)) {
            return value;
        }

        // 处理数字类型字段
        if (ObjectDescribeExt.NUMBER_TYPE_FIELD.contains(fieldType)) {
            // 如果值已经是数字类型，直接返回
            if (value instanceof Number) {
                return value;
            }

            // 处理字符串类型
            if (value instanceof String) {
                String strValue = ((String) value).trim();
                // 如果是空字符串，返回null
                if (Strings.isNullOrEmpty(strValue)) {
                    return null;
                }

                try {
                    // 尝试转换为BigDecimal
                    return new BigDecimal(strValue);
                } catch (Exception e) {
                    log.warn("Failed to convert string value to number field: {} type: {}", fieldApiName, value, e);
                }
            } else {
                // 其他类型尝试转换
                try {
                    return new BigDecimal(String.valueOf(value));
                } catch (Exception e) {
                    log.warn("Failed to convert value to number field: {} type: {}", fieldApiName, value, e);
                }
            }
        }
        return value;
    }

    @Override
    public <T extends BaseActionArg> List<T> processOneFlowArguments(
            List<T> args, Map<String, Object> variableData, IObjectData objectData, IObjectDescribe describe,
            User user, IUdefButton button) {
        // First call the general processing method with data type conversion enabled
        List<T> processedArgs = processArguments(args, variableData, objectData, describe, user, button, true);

        if (CollectionUtils.empty(processedArgs)) {
            return processedArgs;
        }

        // 第一步：收集所有需要查询的对象ID，按objectApiName分组
        Map<String, List<String>> objectApiNameToIds = collectObjectIds(processedArgs);

        // 第二步：批量查询对象数据
        Map<String, Map<String, Object>> objectDataMap = batchQueryObjectData(user, objectApiNameToIds);

        // 第三步：处理每个参数，包括类型转换和对象数据填充
        processedArgs.forEach(arg -> {
            // 处理对象数据填充
            if (Objects.nonNull(arg.getRealValue())) {
                fillObjectData(arg, objectDataMap);
            }
        });

        return processedArgs;
    }

    /**
     * 收集需要查询的对象ID，按objectApiName分组
     */
    private Map<String, List<String>> collectObjectIds(List<? extends BaseActionArg> args) {
        Map<String, List<String>> objectApiNameToIds = new HashMap<>();

        args.forEach(arg -> {
            if (Objects.isNull(arg.getRealValue())) {
                return;
            }

            OneFlowArg oneFlowArg = (OneFlowArg) arg;

            // 处理单个对象类型
            if ("object".equals(oneFlowArg.getType()) && Objects.nonNull(oneFlowArg.getObjectApiName())) {
                String objectId = extractObjectId(oneFlowArg.getRealValue());
                if (Objects.nonNull(objectId)) {
                    String key = oneFlowArg.getObjectApiName();
                    objectApiNameToIds.computeIfAbsent(key, k -> new ArrayList<>()).add(objectId);
                }
            }
            // 处理对象列表类型
            else if ("list".equals(oneFlowArg.getType()) && "object".equals(oneFlowArg.getElementType())
                    && Objects.nonNull(oneFlowArg.getElementObjectApiName())) {
                List<String> objectIds = extractObjectIds(oneFlowArg.getRealValue());
                if (!CollectionUtils.empty(objectIds)) {
                    String key = oneFlowArg.getElementObjectApiName();
                    objectApiNameToIds.computeIfAbsent(key, k -> new ArrayList<>()).addAll(objectIds);
                }
            }
        });

        return objectApiNameToIds;
    }

    /**
     * 批量查询对象数据
     */
    private Map<String, Map<String, Object>> batchQueryObjectData(User user, Map<String, List<String>> objectApiNameToIds) {
        Map<String, Map<String, Object>> result = new HashMap<>();

        objectApiNameToIds.forEach((objectApiName, ids) -> {
            if (!CollectionUtils.empty(ids)) {
                try {
                    // 批量查询对象数据
                    List<IObjectData> objectDataList = metaDataFindService.findObjectDataByIds(user.getTenantId(), ids, objectApiName);

                    // 将查询结果转换为ID->数据的映射
                    Map<String, Object> idToDataMap = new HashMap<>();
                    if (!CollectionUtils.empty(objectDataList)) {
                        objectDataList.forEach(data -> {
                            idToDataMap.put(data.getId(), data);
                        });
                    }

                    result.put(objectApiName, idToDataMap);
                } catch (Exception e) {
                    log.error("Failed to query object data for apiName: {}, ids: {}", objectApiName, ids, e);
                    // 查询失败时，放入空映射
                    result.put(objectApiName, new HashMap<>());
                }
            }
        });

        return result;
    }

    /**
     * 填充对象数据
     */
    private void fillObjectData(BaseActionArg arg, Map<String, Map<String, Object>> objectDataMap) {
        // 强制转换为OneFlowArg
        OneFlowArg oneFlowArg = (OneFlowArg) arg;

        // 处理单个对象类型
        if ("object".equals(oneFlowArg.getType()) && Objects.nonNull(oneFlowArg.getObjectApiName())) {
            String objectId = extractObjectId(oneFlowArg.getRealValue());
            if (Objects.nonNull(objectId)) {
                Map<String, Object> idToDataMap = objectDataMap.get(oneFlowArg.getObjectApiName());
                if (Objects.nonNull(idToDataMap) && idToDataMap.containsKey(objectId)) {
                    oneFlowArg.setRealValue(idToDataMap.get(objectId));
                }
            }
        }
        // 处理对象列表类型
        else if ("list".equals(oneFlowArg.getType()) && "object".equals(oneFlowArg.getElementType())
                && Objects.nonNull(oneFlowArg.getElementObjectApiName())) {
            List<String> objectIds = extractObjectIds(oneFlowArg.getRealValue());
            if (!CollectionUtils.empty(objectIds)) {
                Map<String, Object> idToDataMap = objectDataMap.get(oneFlowArg.getElementObjectApiName());
                if (Objects.nonNull(idToDataMap)) {
                    List<Object> dataList = objectIds.stream()
                            .map(idToDataMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    oneFlowArg.setRealValue(dataList);
                }
            }
        }
    }

    /**
     * 提取单个对象ID
     */
    private String extractObjectId(Object value) {
        if (value instanceof String) {
            return (String) value;
        } else if (value instanceof Map) {
            return (String) ((Map<?, ?>) value).get("id");
        }
        return null;
    }

    /**
     * 提取对象ID列表
     */
    private List<String> extractObjectIds(Object value) {
        List<String> result = new ArrayList<>();

        if (value instanceof List) {
            List<?> list = (List<?>) value;
            for (Object item : list) {
                String id = extractObjectId(item);
                if (id != null) {
                    result.add(id);
                }
            }
        } else if (value instanceof String) {
            try {
                List<?> list = JSON.parseArray((String) value);
                for (Object item : list) {
                    String id = extractObjectId(item);
                    if (id != null) {
                        result.add(id);
                    }
                }
            } catch (Exception e) {
                log.warn("Failed to parse {} as JSON array", value);
            }
        }

        return result;
    }
}
