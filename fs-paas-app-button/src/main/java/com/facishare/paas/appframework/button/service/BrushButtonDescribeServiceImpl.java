package com.facishare.paas.appframework.button.service;

import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.DescribeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.PostActionService;
import com.facishare.paas.appframework.metadata.UdefActionExt;
import com.facishare.paas.appframework.metadata.button.ButtonType;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IUdefButtonService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.UdefButtonPGMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.UdefFunctionService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.ButtonExt.DEFAULT_ADD_BUTTON_API_NAME;
import static com.facishare.paas.appframework.metadata.ButtonExt.DEFAULT_EDIT_BUTTON_API_NAME;

/**
 * create by zhaoju on 2020/09/11
 */
@Slf4j
@Service
public class BrushButtonDescribeServiceImpl implements BrushButtonDescribeService {
    @Autowired
    private DescribeLogicServiceImpl describeLogicService;
    @Autowired
    private IUdefButtonService buttonService;
    @Autowired
    private PostActionService actionService;
    @Autowired
    protected FunctionLogicService functionLogicService;

    @Autowired
    private UdefButtonPGMapper udefButtonPGMapper;

    @Resource
    private BrushButtonDescribeServiceImpl brushButtonDescribeService;
    @Autowired
    private UdefFunctionService udefFunctionService;

    @Override
    public void brushButtonDescribe(Set<String> objectApiNames, User user) {
        List<String> describeApiNames = getDescribeApiNames(objectApiNames, user);
        if (CollectionUtils.empty(describeApiNames)) {
            return;
        }
        Map<String, List<IUdefButton>> buttonMap = buttonService.findButtonsByDescribeApiNamesAndType(user.getTenantId(), describeApiNames, ButtonType.COMMON.getId());
        if (CollectionUtils.empty(buttonMap)) {
            return;
        }
        buttonMap.forEach((objectApiName, buttonList) -> {
            try {
                brushButtonDescribeService.handleButtonAndAction(objectApiName, buttonList, user);
            } catch (Exception e) {
                log.error("handleButtonAndAction fail, objectApiName:{}, ei:{}", objectApiName, user.getTenantId(), e);
            }
        });

    }

    @Override
    public void brushButtonDescribe(Set<String> objectApiNames, Set<String> tenantIds) {
        if (CollectionUtils.empty(tenantIds)) {
            return;
        }
        for (String tenantId : tenantIds) {
            brushButtonDescribe(objectApiNames, User.systemUser(tenantId));
        }
    }

    @Transactional
    public void handleButtonAndAction(String objectApiName, List<IUdefButton> buttonList, User user) {
        List<IUdefButton> buttons = buttonList.stream()
                .filter(button -> ButtonType.COMMON.getId().equals(button.getButtonType()))
                .filter(button -> DEFAULT_ADD_BUTTON_API_NAME.equals(button.getApiName()) || DEFAULT_EDIT_BUTTON_API_NAME.equals(button.getApiName()))
                .filter(button -> BooleanUtils.isNotTrue(button.isDeleted()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(buttons)) {
            return;
        }
        for (IUdefButton button : buttons) {
            // 修改按钮apiName并更新
            IUdefButton udefButton = updateAndCreate(button, objectApiName);
            if (udefButton == null) {
                continue;
            }
            // 处理按钮绑定函数的  usedInfo
            List<String> actionIds = button.getActions();
            if (CollectionUtils.notEmpty(actionIds)) {
                List<IUdefAction> actionList = actionService.findActionList(user, button, objectApiName);
                // 移除原由按钮中函数的依赖关系
                changeFunctionStatus(user, button, actionList, IUdefFunction.NOT_USED);
                changeFunctionStatus(user, udefButton, actionList, IUdefFunction.USED);
            }
        }
    }

    public IUdefButton updateAndCreate(IUdefButton button, String objectApiName) {
        try {
            int count = udefButtonPGMapper.setTenantId(button.getTenantId()).countById(button.getTenantId(), button.getId());
            if (count == 0) {
                return null;
            }
            ButtonExt buttonExt = ButtonExt.of(button);
            ButtonExt buttonCopy = ButtonExt.of(buttonExt.copy());
            // 逻辑删除旧的按钮 Add_button_default
            buttonService.deleteUdefButton(buttonCopy.getApiName(), objectApiName, button.getTenantId());
            // 新建新的按钮 Add_Save_button_default
            buttonCopy.handleButtonDescribe();
            buttonService.create(buttonCopy.getButton());
            return buttonCopy.getButton();
        } catch (MetadataServiceException e) {
            log.error("update button fail, button:{}", button.toJsonString(), e);
            throw new MetaDataBusinessException(e);
        }
    }

    private void changeFunctionStatus(User user, IUdefButton button, List<IUdefAction> createActions,
                                      String status) {
        IActionContext actionContext = new ActionContext();
        actionContext.setUserId(user.getUserId());
        List<ReferenceData> referenceDataList = new ArrayList<>();
        createActions.stream()
                .filter(x -> UdefActionExt.of(x).isFunctionAction())
                .forEach(x -> {
                    Gson gson = new Gson();
                    Map<String, String> functionMap = gson.fromJson(x.getActionParamter(), Map.class);
                    String functionApiName = functionMap.get("func_api_name");
                    IUdefFunction function = udefFunctionService.findFunctionByApiName(user.getTenantId(), functionApiName
                            , button.getDescribeApiName());

                    if (IUdefFunction.USED.equals(status)) {
                        if (Objects.isNull(function)) {
                            throw new FunctionException(I18NExt.text(I18NKey.FUNCTION_DOES_NOT_EXIST));
                        }
                        udefFunctionService.updateUdefFunctionStatus(user.getTenantId(), functionApiName,
                                button.getDescribeApiName(), status, function.getUsedInfo(), actionContext);
                        referenceDataList.add(buildReferenceData(button, functionApiName));
                    } else {
                        functionLogicService.clearUdefFunctionUsedInfo(user, function, button.getDescribeApiName(), button.getApiName(), status);
                        functionLogicService.deleteRelation(user, SourceTypes.BUTTON, button.getApiName(), functionApiName);
                    }
                });
        if (!CollectionUtils.empty(referenceDataList)) {
            functionLogicService.saveRelation(user, referenceDataList);
        }
    }

    private ReferenceData buildReferenceData(IUdefButton button, String functionApiName) {
        ReferenceData referenceData = ReferenceData.builder()
                .sourceType(SourceTypes.BUTTON)
                .sourceLabel(button.getLabel())
                .sourceValue(button.getApiName())
                .targetType(TargetTypes.FUNCTION)
                .targetValue(functionApiName).build();
        return referenceData;
    }

    private List<String> getDescribeApiNames(Set<String> objectApiNames, User user) {
        if (!AppFrameworkConfig.isAddEditUIActionGrayTenant(user.getTenantId())) {
            return Collections.emptyList();
        }
        // objectApiNames 为空时刷所有自定义对象和在灰度名单中的企业
        if (CollectionUtils.empty(objectApiNames)) {
            List<IObjectDescribe> customDescribes = describeLogicService.findObjectsByTenantId(user.getTenantId(), false, false, false, false);
            if (CollectionUtils.empty(customDescribes)) {
                return Collections.emptyList();
            }
            return customDescribes.stream()
                    .map(IObjectDescribe::getApiName)
                    .filter(apiName -> AppFrameworkConfig.isAddEditUIActionGray(user.getTenantId(), apiName))
                    .distinct()
                    .collect(Collectors.toList());
        }

        Set<String> describeApiNames = objectApiNames.stream()
                .filter(apiName -> AppFrameworkConfig.isAddEditUIActionGray(user.getTenantId(), apiName))
                .collect(Collectors.toSet());
        Map<String, IObjectDescribe> objects = describeLogicService.findObjects(user.getTenantId(), describeApiNames);
        if (CollectionUtils.empty(objects)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(objects.keySet());
    }
}
