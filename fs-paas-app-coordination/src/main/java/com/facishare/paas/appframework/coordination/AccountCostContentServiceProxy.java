package com.facishare.paas.appframework.coordination;


import com.facishare.fsi.proxy.annotation.FsiService;
import com.facishare.fsi.proxy.annotation.FsiUri;
import com.facishare.paas.appframework.coordination.dto.GetAccountCostContentByID;

/**
 * 调用协同获取CRM客户费用
 * <p>
 * Created by yuanjl on 19/5/11.
 */
@FsiService("MDS")
public interface AccountCostContentServiceProxy {
    @FsiUri("XT/FeedApprove/GetCostContentInfoByCustomerID")
    GetAccountCostContentByID.Result getCostContentInfoByCustomerID(GetAccountCostContentByID.Arg arg, String ea);
}

