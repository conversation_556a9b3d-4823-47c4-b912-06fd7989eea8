package com.facishare.paas.appframework.coordination.dto;

import io.protostuff.Tag;
import lombok.Data;

/**
 * Created by liyiguang on 17/5/5.
 */
public interface GetAccountCostContentByID {

  @Data
  class Arg {
    @Tag(1)
    String customerID;
    @Tag(2)
    Integer currentEmployeeId;
  }

  @Data
  class Result {
    @Tag(1)
    Integer refundItemNumber;
    @Tag(2)
    Integer noRefundItemNumber;
    @Tag(3)
    String refundAmount;
    @Tag(4)
    String noRefundAmount;
    @Tag(5)
    Integer count;
  }

}
