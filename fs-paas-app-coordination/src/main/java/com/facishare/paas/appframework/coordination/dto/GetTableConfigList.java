package com.facishare.paas.appframework.coordination.dto;

import io.protostuff.Tag;
import java.util.List;
import lombok.Data;

public interface GetTableConfigList {

  @Data
  class Arg {
    @Tag(1)
    private String tableName;
  }

  @Data
  class Result {
    @Tag(1)
    private TableConfig config;
  }

  @Data
  class TableConfig {
    @Tag(1)
    private String id;
    @Tag(2)
    private String name;
    @Tag(3)
    private String caption;
    @Tag(4)
    private Boolean isTableFilter;
    @Tag(5)
    private List<TableConfigDetail> details;
    @Tag(6)
    private Boolean isSwitchFilter;
    @Tag(7)
    private String ptName;
    @Tag(8)
    private String pkName;
    @Tag(9)
    private String jtName;
    @Tag(10)
    private String jtNameEx;
    @Tag(11)
    private String joinDb;
  }

  @Data
  class TableConfigDetail {
    @Tag(1)
    private String id;
    @Tag(2)
    private String detailId;
    @Tag(3)
    private String fieldName;
    @Tag(4)
    private String caption;
    @Tag(5)
    private Integer type;
    @Tag(6)
    private Boolean isIndex;
    @Tag(7)
    private Boolean isOrdered;
    @Tag(8)
    private Boolean isVisible;
    @Tag(9)
    private Integer order;
    @Tag(10)
    private String rule;
    @Tag(11)
    private List<TableConfigDetailEnum> enums;
    @Tag(12)
    private Integer property;
    @Tag(13)
    private String filterFieldName;
    @Tag(14)
    private String width;
    @Tag(15)
    private String returnValueType;
    @Tag(16)
    private String tableName;
    @Tag(17)
    private String tableFieldName;
    @Tag(18)
    private Integer employeeId;
  }

  @Data
  class TableConfigDetailEnum {
    @Tag(1)
    private String id;
    @Tag(2)
    private String name;
    @Tag(3)
    private String detailId;
    @Tag(4)
    private String code;
    @Tag(5)
    private String optionName;
    @Tag(6)
    private List<TableConfigDetailEnum> children;
    @Tag(7)
    private String parentId;
    @Tag(8)
    private Boolean isSystem;
    @Tag(9)
    private Integer order;
    @Tag(10)
    private Long updateTime;
    @Tag(11)
    private Boolean isDeleted;
  }
}
