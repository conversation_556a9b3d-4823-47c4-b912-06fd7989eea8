package com.facishare.paas.appframework.coordination;

import com.facishare.paas.appframework.coordination.dto.GetSimpleEventList;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

/**
 * Created by zhouwr on 2019/10/23
 */
@RestResource(value = "FEED", desc = "Feed服务", contentType = "application/json")
public interface FeedRestProxy {

    @POST(value = "/getSimpleEvent")
    GetSimpleEventList.Result getSimpleEventList(@Body GetSimpleEventList.Arg arg);

}
