package com.facishare.paas.appframework.coordination.dto;

import io.protostuff.Tag;
import lombok.Data;

import java.util.List;

/**
 * Created by liyigua<PERSON> on 17/5/5.
 */
public interface GetCRMFeedByObjectID {

  @Data
  class Arg {
    @Tag(1)
    Integer objectType;
    @Tag(2)
    String objectId;
    @Tag(3)
    Integer pageSize;
    @Tag(4)
    Integer sinceId;
    @Tag(5)
    Boolean hasPermission;
    @Tag(6)
    Integer currentEmployeeId;
  }

  @Data
  class Result {
    @Tag(1)
    List<SimpleFeed> feeds;
    @Tag(2)
    Boolean hasMore;
  }

  @Data
  class SimpleFeed {
    @Tag(1)
    String senderName;
    @Tag(2)
    Long createTime;
    @Tag(3)
    String content;
    @Tag(4)
    Integer feedType;
    @Tag(5)
    Integer feedId;
    @Tag(6)
    Integer senderId;
    @Tag(7)
    String profileImage;
  }
}
