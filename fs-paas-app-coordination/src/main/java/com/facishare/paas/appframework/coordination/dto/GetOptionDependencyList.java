package com.facishare.paas.appframework.coordination.dto;

import io.protostuff.Tag;
import java.util.List;
import lombok.Data;

public interface GetOptionDependencyList {
  @Data
  class Arg {
    @Tag(1)
    private Integer source;
    @Tag(2)
    private String controlFieldName;
    @Tag(3)
    private String dependFieldName;
  }

  @Data
  class Result {
    @Tag(2)
    private List<OptionDependency> dependencyList;
  }

  @Data
  class OptionDependency {
    @Tag(1)
    private String code;
    @Tag(2)
    private String fieldName;
    @Tag(3)
    private String itemCode;
  }
}
