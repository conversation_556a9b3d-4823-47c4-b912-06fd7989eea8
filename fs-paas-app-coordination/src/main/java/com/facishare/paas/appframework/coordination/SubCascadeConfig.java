package com.facishare.paas.appframework.coordination;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * 数据权限是否级联下级的配置
 * <p>
 * Created by zhouwr on 2019/6/17
 */
@Slf4j
public class SubCascadeConfig {

    //数据权限非级联下级的企业id列表，其他企业都是默认级联
    private static Set<String> tenantIdBlackList;

    private static volatile String lastCascadeEnabledTenantIds;

    static {
        ConfigFactory.getConfig("fs-gray-data-auth", config -> {
            String cascadeEnabledTenantIds = config.get("cascadeEnabledTenantIds");
            if (StringUtils.equals(lastCascadeEnabledTenantIds, cascadeEnabledTenantIds)) {
                return;
            }
            log.warn("reload cascadeEnabledTenantIds,lastValue:{},newValue:{}", lastCascadeEnabledTenantIds, cascadeEnabledTenantIds);
            lastCascadeEnabledTenantIds = cascadeEnabledTenantIds;

            if (StringUtils.isBlank(cascadeEnabledTenantIds)) {
                tenantIdBlackList = Sets.newHashSet();
            } else {
                tenantIdBlackList = Sets.newHashSet(Splitter.on("|").omitEmptyStrings().trimResults()
                        .split(StringUtils.substringAfter(cascadeEnabledTenantIds, "black:")));
            }
        });
    }

    public static boolean isInBlackList(String tenantId) {
        if (CollectionUtils.empty(tenantIdBlackList)) {
            return false;
        }
        return tenantIdBlackList.contains(tenantId);
    }

}
