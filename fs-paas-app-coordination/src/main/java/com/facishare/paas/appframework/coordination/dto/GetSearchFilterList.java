package com.facishare.paas.appframework.coordination.dto;

import io.protostuff.Tag;
import java.util.List;
import lombok.Data;

public interface GetSearchFilterList {

  @Data
  class Arg {
    @Tag(1)
    private String tableName;
  }

  @Data
  class Result {
    private List<SearchFilter> filters;
  }

  @Data
  class SearchFilter {
    @Tag(1)
    private String id;
    @Tag(2)
    private String name;
    @Tag(3)
    private Integer type;
    @Tag(4)
    private Integer key;
    @Tag(5)
    private List<SearchFilterDetail> details;
    @Tag(6)
    private Boolean isDefault;
    @Tag(7)
    private Integer mode;
    @Tag(8)
    private Integer relationType;
    @Tag(9)
    private Boolean isHidden;
    @Tag(10)
    private Integer employeeId;
  }

  @Data
  class SearchFilterDetail {
    @Tag(1)
    private String id;
    @Tag(2)
    private String name;
    @Tag(3)
    private Integer operator;
    @Tag(4)
    private String value;
  }
}
