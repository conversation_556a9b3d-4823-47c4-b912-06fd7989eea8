package com.facishare.paas.appframework.coordination;

import com.facishare.fsi.proxy.annotation.FsiService;
import com.facishare.fsi.proxy.annotation.FsiUri;
import com.facishare.paas.appframework.coordination.dto.GetConfig;
import com.facishare.paas.appframework.coordination.dto.GetFieldDependencyList;
import com.facishare.paas.appframework.coordination.dto.GetOptionDependencyList;

/**
 * Created by zhouwr on 2017/10/23
 */
@FsiService("CRM")
public interface CrmServiceProxy {

    @FsiUri("/CRM/UserDefinedField/GetFieldDependencyList")
    GetFieldDependencyList.Result getFieldDependencyList(GetFieldDependencyList.Arg arg, String ea);

    @FsiUri("/CRM/UserDefinedField/GetCascadeEnumRelationListByFieldName")
    GetOptionDependencyList.Result getOptionDependencyList(GetOptionDependencyList.Arg arg, String ea);
}
