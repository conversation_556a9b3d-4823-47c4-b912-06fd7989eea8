package com.facishare.paas.appframework.coordination;

import com.facishare.paas.appframework.coordination.dto.GetCRMFeedByObjectID;
import com.facishare.paas.appframework.coordination.dto.GetSimpleEventList;
import com.facishare.paas.appframework.core.model.User;

import java.util.List;

public interface FeedService {
    List<GetCRMFeedByObjectID.SimpleFeed> findFeedList(String apiName, String dataId, User user);

    List<GetCRMFeedByObjectID.SimpleFeed> findFeedList(String apiName, String dataId, User user, boolean hasPermission);

    List<GetSimpleEventList.Event> findEventList(String apiName, String dataId, User user, boolean hasPermission);
}
