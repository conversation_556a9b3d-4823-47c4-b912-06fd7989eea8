package com.facishare.paas.appframework.coordination;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.coordination.dto.GetCRMFeedByObjectID;
import com.facishare.paas.appframework.coordination.dto.GetSimpleEventList;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.support.GDSHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service("feedService")
public class FeedServiceImpl implements FeedService {
    @Autowired
    private GDSHandler gdsHandler;
    @Autowired
    private FeedRestProxy feedRestProxy;

    @Override
    public List<GetCRMFeedByObjectID.SimpleFeed> findFeedList(String apiName, String dataId, User user, boolean hasPermission) {
        List<GetSimpleEventList.Event> events = findEventList(apiName, dataId, user, hasPermission);
        return CollectionUtils.nullToEmpty(events).stream().map(GetSimpleEventList.Event::toSimpleFeed).collect(Collectors.toList());
    }

    @Override
    public List<GetCRMFeedByObjectID.SimpleFeed> findFeedList(String apiName, String dataId, User user) {
        return findFeedList(apiName, dataId, user, true);
    }

    @Override
    public List<GetSimpleEventList.Event> findEventList(String apiName, String dataId, User user, boolean hasPermission) {
        GetSimpleEventList.Arg arg = new GetSimpleEventList.Arg();
        arg.setCurrentEmployeeId(Integer.valueOf(user.getUserId()));
        arg.setHasPermission(hasPermission);
        String objectId = String.format("%s|%s", apiName, dataId);
        arg.setObjectId(objectId);
        arg.setObjectType(200);
        arg.setPageSize(2);
        arg.setSinceId(0);
        String ea = gdsHandler.getEAByEI(user.getTenantId());
        arg.setEnterpriseAccount(ea);
        arg.setEnterpriseId(Integer.parseInt(user.getTenantId()));
        if (user.isOutUser()) {
            arg.setOutTenantId(Long.parseLong(user.getOutTenantId()));
            arg.setOutUserId(Long.parseLong(user.getOutUserId()));
            arg.setOutAppId(RequestContextManager.getContext().getAppId());
        }

        return feedRestProxy.getSimpleEventList(arg).getEvents();
    }
}
