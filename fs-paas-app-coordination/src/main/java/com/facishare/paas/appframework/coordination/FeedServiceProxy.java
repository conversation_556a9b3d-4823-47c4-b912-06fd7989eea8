package com.facishare.paas.appframework.coordination;


import com.facishare.fsi.proxy.annotation.FsiService;
import com.facishare.fsi.proxy.annotation.FsiUri;
import com.facishare.paas.appframework.coordination.dto.GetCRMFeedByObjectID;

/**
 * 调用协同获取CRM 对象相关Feed
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/5/5.
 */
@FsiService("MDS")
public interface FeedServiceProxy {

    @FsiUri("CRM/FCustomer/GetCrmFeedsByObjectId")
    GetCRMFeedByObjectID.Result getCRMFeedByObjectID(GetCRMFeedByObjectID.Arg arg, String ea);
}

