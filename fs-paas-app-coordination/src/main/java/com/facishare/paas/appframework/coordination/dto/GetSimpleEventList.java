package com.facishare.paas.appframework.coordination.dto;

import lombok.Data;

import java.util.List;

/**
 * Created by zhouwr on 2019/10/23
 */
public interface GetSimpleEventList {

    @Data
    class Arg {
        private Integer objectType;
        private String objectId;
        private Integer pageSize;
        private Integer sinceId;
        private Boolean hasPermission;
        private Integer currentEmployeeId;
        private Integer enterpriseId;
        private String enterpriseAccount;
        private Long outUserId;
        private Long outTenantId;
        private String outAppId;
    }

    @Data
    class Result {
        private List<Event> events;
    }

    @Data
    class Event {
        private String senderName;
        private Long createTime;
        private String content;
        private Integer feedType;
        private Integer feedId;
        private Integer senderId;
        private String profileImage;
        private Boolean isCross;
        private String outTenantName;

        public GetCRMFeedByObjectID.SimpleFeed toSimpleFeed() {
            GetCRMFeedByObjectID.SimpleFeed simpleFeed = new GetCRMFeedByObjectID.SimpleFeed();
            simpleFeed.setSenderName(senderName);
            simpleFeed.setCreateTime(createTime);
            simpleFeed.setContent(content);
            simpleFeed.setFeedType(feedType);
            simpleFeed.setFeedId(feedId);
            simpleFeed.setSenderId(senderId);
            simpleFeed.setProfileImage(profileImage);
            return simpleFeed;
        }
    }

}
