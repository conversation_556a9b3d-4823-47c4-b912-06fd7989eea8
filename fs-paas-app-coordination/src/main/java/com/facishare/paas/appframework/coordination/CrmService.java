package com.facishare.paas.appframework.coordination;

import com.facishare.paas.appframework.coordination.dto.GetFieldDependencyList;
import com.facishare.paas.appframework.coordination.dto.GetOptionDependencyList;
import com.facishare.paas.appframework.coordination.dto.GetSearchFilterList;
import com.facishare.paas.appframework.coordination.dto.GetTableConfigList;

/**
 * Created by zhouwr on 2017/10/23
 */
public interface CrmService {
    String getConfigValue(String tenantId, int key);

    boolean canUpdateAccountName(String tenantId);

    boolean getIsSubCascadeConfig(String tenantId);

    GetFieldDependencyList.Result getFieldDependencyList(String tenantId, GetFieldDependencyList.Arg arg);

    GetOptionDependencyList.Result getOptionDependencyList(String tenantId, GetOptionDependencyList.Arg arg);

    GetSearchFilterList.Result getSearchFilterList(String tenantId, GetSearchFilterList.Arg arg);

    GetTableConfigList.Result getTableConfigList(String tenantId, GetTableConfigList.Arg arg);
}
