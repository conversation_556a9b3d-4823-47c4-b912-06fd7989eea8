package com.facishare.paas.appframework.coordination.dto;

import io.protostuff.Tag;
import java.util.List;
import lombok.Data;

public interface GetFieldDependencyList {

  int SOURCE_CLUE = 1;
  int SOURCE_ACCOUNT = 2;
  int SOURCE_CONTACT = 3;
  int SOURCE_PRODUCT = 4;
  int SOURCE_PAYMENT = 5;

  @Data
  class Arg {
    @Tag(1)
    private Integer source;
  }

  @Data
  class Result {
    @Tag(2)
    private List<FieldDependency> dependencyList;
  }

  @Data
  class FieldDependency {
    @Tag(1)
    private Integer source;
    @Tag(2)
    private String controlFieldName;
    @Tag(3)
    private String controlFieldCaption;
    @Tag(4)
    private String dependFieldName;
    @Tag(5)
    private String dependFieldCaption;
  }
}
