<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:spring/common.xml"/>
    <import resource="classpath:META-INF/spring/fs-fsi-proxy-warehouse.xml"/>

    <bean id="feedRestProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.coordination.FeedRestProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <!--协调Feed服务-->
    <bean id="fsiCrmServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory"
          init-method="init">
        <property name="configKey" value="fs-crm-java-fsi-proxy"/>
    </bean>

    <bean id="feedServiceProxy" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiCrmServiceProxyFactory"/>
        <property name="type" value="com.facishare.paas.appframework.coordination.FeedServiceProxy"/>
    </bean>

    <bean id="crmServiceProxy" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiCrmServiceProxyFactory"/>
        <property name="type" value="com.facishare.paas.appframework.coordination.CrmServiceProxy"/>
    </bean>

    <bean id="crmBasicServiceProxy" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiCrmServiceProxyFactory"/>
        <property name="type" value="com.facishare.paas.appframework.coordination.CrmBasicServiceProxy"/>
    </bean>

    <!-- 文件上传配置-->
    <!--        <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory"-->
    <!--              init-method="init">-->
    <!--            <property name="configKey" value="fs-qixin-fsi-proxy"/>-->
    <!--        </bean>-->
    <!--        <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
    <!--            <property name="factory" ref="fsiServiceProxyFactory"/>-->
    <!--            <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>-->
    <!--        </bean>-->
    <!--        <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory"-->
    <!--              init-method="init">-->
    <!--            <property name="configKey" value="fs-qixin-fsi-proxy"/>-->
    <!--            <property name="globalConfigService" ref="globalConfigService"/>-->
    <!--        </bean>-->
    <!--        <bean id="aFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
    <!--            <property name="factory" ref="fsiWarehouseProxyFactory"/>-->
    <!--            <property name="type" value="com.facishare.fsi.proxy.service.AFileStorageService"/>-->
    <!--        </bean>-->
    <!--        <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
    <!--            <property name="factory" ref="fsiWarehouseProxyFactory"/>-->
    <!--            <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>-->
    <!--        </bean>-->

    <!--获取客户费用服务-->
    <bean id="accountCostContentServiceProxy" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiCrmServiceProxyFactory"/>
        <property name="type" value="com.facishare.paas.appframework.coordination.AccountCostContentServiceProxy"/>
    </bean>
    <!--合并销售记录服务-->
    <bean id="feedsExternalResourceServiceProxy" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiCrmServiceProxyFactory"/>
        <property name="type" value="com.facishare.paas.appframework.coordination.FeedsExternalResourceServiceProxy"/>
    </bean>
</beans>