# 设置根日志级别为WARN，并定义输出目标为控制台
log4j.rootLogger=WARN, console

# 控制台输出配置
log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n

# 设置特定包的日志级别
log4j.logger.com.github.trace=ERROR
log4j.logger.com.facishare=INFO
log4j.logger.com.github.autoconf=ERROR
log4j.logger.org.apache.commons.beanutils=ERROR
log4j.logger.org.apache.commons=ERROR
log4j.logger.org.apache=ERROR
log4j.logger.org.springframework=ERROR
log4j.logger.org.mybatis=ERROR
log4j.logger.org.hibernate=ERROR
log4j.logger.com.alibaba=ERROR
log4j.logger.com.github=ERROR
log4j.logger.io.netty=ERROR
log4j.logger.org.jboss=ERROR
log4j.logger.org.apache.zookeeper=ERROR
log4j.logger.org.apache.dubbo=ERROR
log4j.logger.com.netflix=ERROR 