package com.facishare.crm.valueobject

import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.timezone.TimeZoneContextHolder
import spock.lang.Specification
import spock.lang.Unroll

import java.time.ZoneId

/**
 * GenerateByAI
 * 测试内容描述：SessionContext类的单元测试
 */
class SessionContextTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试SessionContext构造函数和基本属性设置
     */
    def "构造函数和基本属性设置测试"() {
        given:
        def sessionContext = new SessionContext()
        sessionContext.setEa("testEa")
        sessionContext.setEId(123456L)
        sessionContext.setUserId(7890)
        sessionContext.setClientInfo("Chrome")
        sessionContext.setAppId("testAppId")
        sessionContext.setLanguageLocale("zh_CN")
        sessionContext.setOutUserId("outUser123")
        sessionContext.setOuterTenantId("outTenant456")
        
        when: "获取属性值"
        def ea = sessionContext.getEa()
        def eId = sessionContext.getEId()
        def userId = sessionContext.getUserId()
        def userIdString = sessionContext.getUserIdString()
        def clientInfo = sessionContext.getClientInfo()
        def appId = sessionContext.getAppId()
        def languageLocale = sessionContext.getLanguageLocale()
        def outUserId = sessionContext.getOutUserId()
        def outerTenantId = sessionContext.getOuterTenantId()
        
        then: "属性值与设置的一致"
        ea == "testEa"
        eId == 123456L
        userId == 7890
        userIdString == "7890"
        clientInfo == "Chrome"
        appId == "testAppId"
        languageLocale == "zh_CN"
        outUserId == "outUser123"
        outerTenantId == "outTenant456"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试SessionContext.of方法，从User对象创建SessionContext
     */
    def "of方法测试，从User对象创建SessionContext"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "123456"
        user.getUserId() >> "7890"
        user.getOutUserId() >> "outUser123"
        user.getOutTenantId() >> "outTenant456"
        
        when:
        def sessionContext = SessionContext.of(user)
        
        then:
        sessionContext.getEId() == 123456L
        sessionContext.getUserId() == 7890
        sessionContext.getOutUserId() == "outUser123"
        sessionContext.getOuterTenantId() == "outTenant456"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试isOutUser方法判断是否为外部用户
     */
    @Unroll
    def "isOutUser方法测试 - outerTenantId=#outerTenantId, outUserId=#outUserId, 期望结果=#expected"() {
        given:
        def sessionContext = new SessionContext()
        sessionContext.setOuterTenantId(outerTenantId)
        sessionContext.setOutUserId(outUserId)
        
        when:
        def result = sessionContext.isOutUser()
        
        then:
        result == expected
        
        where:
        outerTenantId | outUserId    | expected
        null          | "outUser123" | true
        ""            | "outUser123" | true
        "outTenant456"| "outUser123" | false
        null          | null         | false
        ""            | ""           | false
        "0"           | "outUser123" | false
        null          | "0"          | false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试sourceFrom方法，确定请求来源
     */
    @Unroll
    def "sourceFrom方法测试 - clientInfo=#clientInfo, 期望结果=#expected"() {
        given:
        def sessionContext = new SessionContext()
        sessionContext.setClientInfo(clientInfo)
        
        when:
        def result = sessionContext.sourceFrom()
        
        then:
        result == expected
        
        where:
        clientInfo                      | expected
        null                            | SessionContext.SOURCE_UNKNOWN_END
        ""                              | SessionContext.SOURCE_UNKNOWN_END
        "Mozilla/5.0 Chrome/90.0.4430.212" | SessionContext.SOURCE_WEB_END
        "Mozilla/5.0 Android 11.0"      | SessionContext.SOURCE_MOBILE_END
        "Mozilla/5.0 iOS/14.5"          | SessionContext.SOURCE_MOBILE_END
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getUserIdString方法
     */
    def "getUserIdString方法测试"() {
        given:
        def sessionContext = new SessionContext()
        
        when: "userId为空时"
        def nullResult = sessionContext.getUserIdString()
        
        then: "返回null"
        nullResult == null
        
        when: "userId有值时"
        sessionContext.setUserId(123)
        def result = sessionContext.getUserIdString()
        
        then: "返回字符串形式的userId"
        result == "123"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试getTimeZone方法
     */
    def "getTimeZone方法测试 - useTenantTimezone=#useTenantTimezone"() {
        given:
        def sessionContext = new SessionContext()
        sessionContext.setUseTenantTimezone(useTenantTimezone)
        
        and: "模拟TimeZoneContextHolder"
        def userZoneId = ZoneId.of("America/New_York")
        def tenantZoneId = ZoneId.of("Asia/Shanghai")
        GroovyMock(TimeZoneContextHolder, global: true)
        TimeZoneContextHolder.getUserTimeZone() >> userZoneId
        TimeZoneContextHolder.getTenantTimeZone() >> tenantZoneId
        
        when:
        def result = sessionContext.getTimeZone()
        
        then:
        if (useTenantTimezone) {
            assert result == tenantZoneId
        } else {
            assert result == userZoneId
        }
        
        where:
        useTenantTimezone << [true, false]
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试privilegeCheck属性的设置和获取
     */
    def "privilegeCheck属性测试"() {
        given:
        def sessionContext = new SessionContext()
        
        when: "默认值"
        def defaultValue = sessionContext.getPrivilegeCheck()
        
        then: "默认为true"
        defaultValue == true
        
        when: "设置为false"
        sessionContext.setPrivilegeCheck(false)
        def updatedValue = sessionContext.getPrivilegeCheck()
        
        then: "值变为false"
        updatedValue == false
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试dateFormatIncludeTimezoneInfo属性的设置和获取
     */
    def "dateFormatIncludeTimezoneInfo属性测试"() {
        given:
        def sessionContext = new SessionContext()
        
        when: "默认值"
        def defaultValue = sessionContext.isDateFormatIncludeTimezoneInfo()
        
        then: "默认为false"
        defaultValue == false
        
        when: "设置为true"
        sessionContext.setDateFormatIncludeTimezoneInfo(true)
        def updatedValue = sessionContext.isDateFormatIncludeTimezoneInfo()
        
        then: "值变为true"
        updatedValue == true
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试region属性的设置和获取
     */
    def "region属性测试"() {
        given:
        def sessionContext = new SessionContext()
        
        when: "默认值"
        def defaultValue = sessionContext.getRegion()
        
        then: "默认为null"
        defaultValue == null
        
        when: "设置region"
        sessionContext.setRegion("zh_CN")
        def updatedValue = sessionContext.getRegion()
        
        then: "值更新为新设置的值"
        updatedValue == "zh_CN"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试fromQuoteField属性的设置和获取
     */
    def "fromQuoteField属性测试"() {
        given:
        def sessionContext = new SessionContext()
        
        when: "默认值"
        def defaultValue = sessionContext.isFromQuoteField()
        
        then: "默认为false"
        defaultValue == false
        
        when: "设置为true"
        sessionContext.setFromQuoteField(true)
        def updatedValue = sessionContext.isFromQuoteField()
        
        then: "值变为true"
        updatedValue == true
    }
} 