package com.facishare.crm.common.exception

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：CrmCheckedException异常类的单元测试
 */
class CrmCheckedExceptionTest extends Specification {

    def "test constructor with CRMErrorCode and message"() {
        given: "准备错误码和消息"
        def errorCode = CRMErrorCode.PARAMETER_IS_WRONG
        def message = "Test parameter error"

        when: "创建异常实例"
        def exception = new CrmCheckedException(errorCode, message)

        then: "验证异常属性"
        exception.getErrorCode() == errorCode
        exception.getMessage() == message
        exception.getCause() == null
    }

    def "test constructor with CRMErrorCode, message and cause"() {
        given: "准备错误码、消息和原因"
        def errorCode = CRMErrorCode.SYSTEM_ERROR
        def message = "Test system error"
        def cause = new RuntimeException("Root cause")

        when: "创建异常实例"
        def exception = new CrmCheckedException(errorCode, message, cause)

        then: "验证异常属性"
        exception.getErrorCode() == errorCode
        exception.getMessage() == message
        exception.getCause() == cause
    }

    def "test getIntErrorCode method"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.AUTHENTICATION_ERROR
        def exception = new CrmCheckedException(errorCode, "Auth error")

        when: "调用getIntErrorCode方法"
        def intErrorCode = exception.getIntErrorCode()

        then: "应该返回null（因为errorCode字段未设置）"
        intErrorCode == null
    }


    def "test with null message"() {
        given: "使用null消息"
        def errorCode = CRMErrorCode.PAAS_ERROR

        when: "创建异常实例"
        def exception = new CrmCheckedException(errorCode, null)

        then: "验证异常属性"
        exception.getErrorCode() == errorCode
        exception.getMessage() == null
    }

    def "test with null cause"() {
        given: "使用null原因"
        def errorCode = CRMErrorCode.PAAS_PRIVILEGE_FAILED
        def message = "Test with null cause"

        when: "创建异常实例"
        def exception = new CrmCheckedException(errorCode, message, null)

        then: "验证异常属性"
        exception.getErrorCode() == errorCode
        exception.getMessage() == message
        exception.getCause() == null
    }

    def "test exception inheritance"() {
        given: "创建异常实例"
        def exception = new CrmCheckedException(CRMErrorCode.SUCCESS, "Test")

        expect: "验证继承关系"
        exception instanceof Exception
        exception instanceof CrmErrorInter
    }

    def "test CrmErrorInter interface implementation"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.LOW_VERSION_ERROR
        def exception = new CrmCheckedException(errorCode, "Version error")

        when: "通过接口调用方法"
        CrmErrorInter errorInter = exception
        def result = errorInter.getErrorCode()

        then: "应该返回正确的错误码"
        result == errorCode
    }

    def "test with different CRMErrorCode values"() {
        given: "不同的错误码"
        def testCases = [
            CRMErrorCode.SUCCESS,
            CRMErrorCode.SYSTEM_ERROR,
            CRMErrorCode.PARAMETER_IS_WRONG,
            CRMErrorCode.AUTHENTICATION_ERROR,
            CRMErrorCode.PAAS_ERROR,
            CRMErrorCode.FS_CRM_COMMON_RUNTIME_UNKOWN,
            CRMErrorCode.FS_CRM_OPENAPI_UNSUPPORTED_OBJECT_TYPE,
            CRMErrorCode.LICENSE_ERROR
        ]

        expect: "所有错误码都能正确设置"
        testCases.each { errorCode ->
            def exception = new CrmCheckedException(errorCode, "Test message")
            assert exception.getErrorCode() == errorCode
        }
    }

    def "test exception message handling"() {
        given: "不同类型的消息"
        def errorCode = CRMErrorCode.PARAMETER_IS_WRONG
        def testMessages = [
            "Simple message",
            "Message with special characters: !@#\$%^&*()",
            "中文消息测试",
            "",
            "Very long message that contains multiple sentences and should be handled properly by the exception class without any issues."
        ]

        expect: "所有消息都能正确处理"
        testMessages.each { message ->
            def exception = new CrmCheckedException(errorCode, message)
            assert exception.getMessage() == message
        }
    }

    def "test exception cause chain"() {
        given: "创建异常链"
        def rootCause = new IllegalArgumentException("Root cause")
        def middleCause = new RuntimeException("Middle cause", rootCause)
        def errorCode = CRMErrorCode.SYSTEM_ERROR
        def message = "Top level error"

        when: "创建异常实例"
        def exception = new CrmCheckedException(errorCode, message, middleCause)

        then: "验证异常链"
        exception.getCause() == middleCause
        exception.getCause().getCause() == rootCause
        exception.getCause().getMessage() == "Middle cause"
        exception.getCause().getCause().getMessage() == "Root cause"
    }

    def "test toString method"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.AUTHENTICATION_ERROR
        def message = "Authentication failed"
        def exception = new CrmCheckedException(errorCode, message)

        when: "调用toString方法"
        def result = exception.toString()

        then: "应该包含异常信息"
        result != null
        result.contains("CrmCheckedException")
        result.contains(message)
    }

    def "test stack trace functionality"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.PAAS_ERROR
        def message = "PAAS service error"

        when: "创建并抛出异常"
        def exception = new CrmCheckedException(errorCode, message)

        then: "应该有堆栈跟踪信息"
        exception.getStackTrace() != null
        exception.getStackTrace().length > 0
    }

    def "test serialization compatibility"() {
        given: "创建异常实例"
        def errorCode = CRMErrorCode.LICENSE_ERROR
        def message = "License validation failed"
        def exception = new CrmCheckedException(errorCode, message)

        expect: "异常应该是可序列化的（Exception实现了Serializable）"
        exception instanceof java.io.Serializable
    }
}
