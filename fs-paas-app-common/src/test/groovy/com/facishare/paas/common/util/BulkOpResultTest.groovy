package com.facishare.paas.common.util

import com.facishare.paas.metadata.api.IObjectData
import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：BulkOpResult批量操作结果类的单元测试
 */
class BulkOpResultTest extends Specification {

    def "test builder pattern"() {
        given: "准备测试数据"
        def successData = Mock(IObjectData)
        successData.getId() >> "success1"
        def failData = Mock(IObjectData)
        failData.getId() >> "fail1"

        when: "使用builder创建BulkOpResult"
        def result = BulkOpResult.builder()
                .successObjectDataList([successData])
                .failObjectDataList([failData])
                .failReason("Test failure reason")
                .build()

        then: "验证所有字段正确设置"
        result.successObjectDataList.size() == 1
        result.successObjectDataList[0] == successData
        result.failObjectDataList.size() == 1
        result.failObjectDataList[0] == failData
        result.failReason == "Test failure reason"
    }

    def "test hasFailed with failed data"() {
        given: "创建包含失败数据的结果"
        def failData = Mock(IObjectData)
        def result = BulkOpResult.builder()
                .failObjectDataList([failData])
                .build()

        when: "调用hasFailed方法"
        def hasFailed = result.hasFailed()

        then: "应该返回true"
        hasFailed == true
    }

    def "test hasFailed without failed data"() {
        given: "创建不包含失败数据的结果"
        def result = BulkOpResult.builder()
                .failObjectDataList([])
                .build()

        when: "调用hasFailed方法"
        def hasFailed = result.hasFailed()

        then: "应该返回false"
        hasFailed == false
    }

    def "test hasFailed with null failed data"() {
        given: "创建失败数据为null的结果"
        def result = BulkOpResult.builder()
                .failObjectDataList(null)
                .build()

        when: "调用hasFailed方法"
        def hasFailed = result.hasFailed()

        then: "应该返回false"
        hasFailed == false
    }

    def "test merge with null opResult"() {
        given: "创建原始结果"
        def originalResult = BulkOpResult.builder()
                .successObjectDataList([])
                .failObjectDataList([])
                .failReason("original")
                .build()

        when: "合并null结果"
        originalResult.merge(null)

        then: "原始结果应该保持不变"
        originalResult.successObjectDataList.isEmpty()
        originalResult.failObjectDataList.isEmpty()
        originalResult.failReason == "original"
    }

    def "test merge with valid opResult"() {
        given: "创建原始结果和要合并的结果"
        def successData1 = Mock(IObjectData)
        successData1.getId() >> "success1"
        def failData1 = Mock(IObjectData)
        failData1.getId() >> "fail1"

        def originalResult = BulkOpResult.builder()
                .successObjectDataList([successData1])
                .failObjectDataList([failData1])
                .failReason("original reason")
                .build()

        def successData2 = Mock(IObjectData)
        successData2.getId() >> "success2"
        def failData2 = Mock(IObjectData)
        failData2.getId() >> "fail2"

        def mergeResult = BulkOpResult.builder()
                .successObjectDataList([successData2])
                .failObjectDataList([failData2])
                .failReason("merge reason")
                .build()

        when: "合并结果"
        originalResult.merge(mergeResult)

        then: "验证合并后的结果"
        originalResult.successObjectDataList.size() == 2
        originalResult.successObjectDataList.contains(successData1)
        originalResult.successObjectDataList.contains(successData2)
        originalResult.failObjectDataList.size() == 2
        originalResult.failObjectDataList.contains(failData1)
        originalResult.failObjectDataList.contains(failData2)
        originalResult.failReason == "original reason|merge reason"
    }

    def "test merge with duplicate fail data"() {
        given: "创建包含重复失败数据的结果"
        def failData = Mock(IObjectData)
        failData.getId() >> "fail1"

        def originalResult = BulkOpResult.builder()
                .failObjectDataList([failData])
                .build()

        def duplicateFailData = Mock(IObjectData)
        duplicateFailData.getId() >> "fail1"

        def mergeResult = BulkOpResult.builder()
                .failObjectDataList([duplicateFailData])
                .build()

        when: "合并包含重复ID的结果"
        originalResult.merge(mergeResult)

        then: "重复的失败数据应该被过滤掉"
        originalResult.failObjectDataList.size() == 1
        originalResult.failObjectDataList[0] == failData
    }

    def "test merge with empty fail reason"() {
        given: "创建原始结果和空失败原因的合并结果"
        def originalResult = BulkOpResult.builder()
                .failReason("original reason")
                .build()

        def mergeResult = BulkOpResult.builder()
                .failReason("")
                .build()

        when: "合并结果"
        originalResult.merge(mergeResult)

        then: "失败原因应该保持原样"
        originalResult.failReason == "original reason"
    }

    def "test merge with null fail reason in original"() {
        given: "创建失败原因为null的原始结果"
        def originalResult = BulkOpResult.builder()
                .failReason(null)
                .build()

        def mergeResult = BulkOpResult.builder()
                .failReason("merge reason")
                .build()

        when: "合并结果"
        originalResult.merge(mergeResult)

        then: "失败原因应该被设置为合并结果的原因"
        originalResult.failReason == "merge reason"
    }

    def "test successObjectDataIds with valid data"() {
        given: "创建包含成功数据的结果"
        def data1 = Mock(IObjectData)
        data1.getId() >> "id1"
        def data2 = Mock(IObjectData)
        data2.getId() >> "id2"
        def data3 = Mock(IObjectData)
        data3.getId() >> "id1" // 重复ID

        def result = BulkOpResult.builder()
                .successObjectDataList([data1, data2, data3])
                .build()

        when: "调用successObjectDataIds方法"
        def ids = result.successObjectDataIds()

        then: "应该返回去重后的ID列表"
        ids.size() == 2
        ids.contains("id1")
        ids.contains("id2")
    }

    def "test successObjectDataIds with null data"() {
        given: "创建成功数据为null的结果"
        def result = BulkOpResult.builder()
                .successObjectDataList(null)
                .build()

        when: "调用successObjectDataIds方法"
        def ids = result.successObjectDataIds()

        then: "应该返回空列表"
        ids.isEmpty()
    }

    def "test successObjectDataIds with empty data"() {
        given: "创建成功数据为空的结果"
        def result = BulkOpResult.builder()
                .successObjectDataList([])
                .build()

        when: "调用successObjectDataIds方法"
        def ids = result.successObjectDataIds()

        then: "应该返回空列表"
        ids.isEmpty()
    }

    def "test init method through merge"() {
        given: "创建空的结果"
        def result = BulkOpResult.builder().build()
        def mergeResult = BulkOpResult.builder()
                .successObjectDataList([])
                .failObjectDataList([])
                .failReason("test")
                .build()

        when: "通过merge触发init方法"
        result.merge(mergeResult)

        then: "所有字段应该被初始化"
        result.successObjectDataList != null
        result.failObjectDataList != null
        result.failReason != null
    }

    def "test merge with complex scenario"() {
        given: "创建复杂的合并场景"
        def originalSuccess = Mock(IObjectData)
        originalSuccess.getId() >> "original_success"
        def originalFail = Mock(IObjectData)
        originalFail.getId() >> "original_fail"

        def originalResult = BulkOpResult.builder()
                .successObjectDataList([originalSuccess])
                .failObjectDataList([originalFail])
                .failReason("original")
                .build()

        def mergeSuccess = Mock(IObjectData)
        mergeSuccess.getId() >> "merge_success"
        def mergeFail = Mock(IObjectData)
        mergeFail.getId() >> "merge_fail"
        def duplicateFail = Mock(IObjectData)
        duplicateFail.getId() >> "original_fail" // 与原始失败数据ID相同

        def mergeResult = BulkOpResult.builder()
                .successObjectDataList([mergeSuccess])
                .failObjectDataList([mergeFail, duplicateFail])
                .failReason("merge")
                .build()

        when: "执行复杂合并"
        originalResult.merge(mergeResult)

        then: "验证复杂合并结果"
        originalResult.successObjectDataList.size() == 2
        originalResult.successObjectDataList.any { it.getId() == "original_success" }
        originalResult.successObjectDataList.any { it.getId() == "merge_success" }
        
        originalResult.failObjectDataList.size() == 2 // 重复的不会被添加
        originalResult.failObjectDataList.any { it.getId() == "original_fail" }
        originalResult.failObjectDataList.any { it.getId() == "merge_fail" }
        
        originalResult.failReason == "original|merge"
    }

    def "test data class methods"() {
        given: "创建BulkOpResult实例"
        def data = Mock(IObjectData)
        data.getId() >> "test"
        def result = BulkOpResult.builder()
                .successObjectDataList([data])
                .failObjectDataList([])
                .failReason("test reason")
                .build()

        expect: "验证getter方法"
        result.getSuccessObjectDataList().size() == 1
        result.getFailObjectDataList().isEmpty()
        result.getFailReason() == "test reason"

        and: "验证setter方法"
        def newData = Mock(IObjectData)
        result.setSuccessObjectDataList([newData])
        result.getSuccessObjectDataList()[0] == newData

        result.setFailObjectDataList([newData])
        result.getFailObjectDataList()[0] == newData

        result.setFailReason("new reason")
        result.getFailReason() == "new reason"
    }
}
