package com.facishare.paas.appframework.common.util

import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.i18n.I18NKey
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：ObjectLockStatus枚举的单元测试
 */
class ObjectLockStatusTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试ObjectLockStatus枚举值
     */
    def "测试ObjectLockStatus枚举值"() {
        expect: "应该有两个枚举值"
        ObjectLockStatus.values().length == 2
        ObjectLockStatus.values().contains(ObjectLockStatus.LOCK)
        ObjectLockStatus.values().contains(ObjectLockStatus.UNLOCK)
        
        and: "枚举值的状态应符合预期"
        ObjectLockStatus.LOCK.getStatus() == "1"
        ObjectLockStatus.UNLOCK.getStatus() == "0"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试ObjectLockStatus的getLabel方法
     */
    def "测试ObjectLockStatus的getLabel方法"() {
        given: "模拟I18N.text方法"
        GroovyMock(I18N, global: true)
        
        when: "调用LOCK的getLabel方法"
        I18N.text(I18NKey.action_lock) >> "锁定"
        def lockLabel = ObjectLockStatus.LOCK.getLabel()
        
        then: "应返回预期的国际化文本"
        lockLabel == "锁定"
        
        when: "调用UNLOCK的getLabel方法"
        I18N.text(I18NKey.action_unlock) >> "解锁"
        def unlockLabel = ObjectLockStatus.UNLOCK.getLabel()
        
        then: "应返回预期的国际化文本"
        unlockLabel == "解锁"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试ObjectLockStatus的静态常量
     */
    def "测试ObjectLockStatus的静态常量"() {
        expect: "常量值应符合预期"
        ObjectLockStatus.LOCK_STATUS_API_NAME == "lock_status"
        ObjectLockStatus.LOCK_RULE_API_NAME == "lock_rule"
        ObjectLockStatus.LOCK_USER_API_NAME == "lock_user"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试ObjectLockStatus的valueOf方法
     */
    def "测试ObjectLockStatus的valueOf方法"() {
        expect: "应能通过名称获取对应的枚举值"
        ObjectLockStatus.valueOf("LOCK") == ObjectLockStatus.LOCK
        ObjectLockStatus.valueOf("UNLOCK") == ObjectLockStatus.UNLOCK
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试ObjectLockStatus的异常情况
     */
    def "测试ObjectLockStatus的异常情况"() {
        when: "尝试获取不存在的枚举值"
        ObjectLockStatus.valueOf("NONEXISTENT")
        
        then: "应抛出IllegalArgumentException异常"
        thrown(IllegalArgumentException)
    }
} 