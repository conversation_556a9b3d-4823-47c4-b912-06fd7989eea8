package com.facishare.paas.appframework.common.util

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.github.autoconf.helper.ConfigHelper
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：RuntimeUtils工具类的单元测试
 */
class RuntimeUtilsTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试获取运行时信息的方法
     */
    def "测试获取运行时信息方法"() {
        given: "模拟ConfigHelper.getProcessInfo"
        def processInfo = Mock(ProcessInfo)
        processInfo.getName() >> "test-app"
        processInfo.getIp() >> "127.0.0.1"
        processInfo.getProfile() >> "test"
        
        GroovyMock(ConfigHelper, global: true)
        ConfigHelper.getProcessInfo() >> processInfo
        
        // 重新初始化RuntimeUtils的静态字段
        def appNameField = RuntimeUtils.class.getDeclaredField("appName")
        appNameField.setAccessible(true)
        def modifiersField = java.lang.reflect.Field.class.getDeclaredField("modifiers")
        modifiersField.setAccessible(true)
        modifiersField.setInt(appNameField, appNameField.getModifiers() & ~java.lang.reflect.Modifier.FINAL)
        appNameField.set(null, processInfo.getName())
        
        def serverIpField = RuntimeUtils.class.getDeclaredField("serverIp")
        serverIpField.setAccessible(true)
        modifiersField.setInt(serverIpField, serverIpField.getModifiers() & ~java.lang.reflect.Modifier.FINAL)
        serverIpField.set(null, processInfo.getIp())
        
        def profileField = RuntimeUtils.class.getDeclaredField("profile")
        profileField.setAccessible(true)
        modifiersField.setInt(profileField, profileField.getModifiers() & ~java.lang.reflect.Modifier.FINAL)
        profileField.set(null, processInfo.getProfile())
        
        when: "调用获取应用名称方法"
        def appName = RuntimeUtils.getAppName()
        
        then: "应返回预期的应用名称"
        appName == "test-app"
        
        when: "调用获取服务器IP方法"
        def serverIp = RuntimeUtils.getServerIp()
        
        then: "应返回预期的服务器IP"
        serverIp == "127.0.0.1"
        
        when: "调用获取配置文件方法"
        def profile = RuntimeUtils.getProfile()
        
        then: "应返回预期的配置文件名称"
        profile == "test"
    }
} 