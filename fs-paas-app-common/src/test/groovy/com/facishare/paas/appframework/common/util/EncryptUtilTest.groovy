package com.facishare.paas.appframework.common.util

import com.fxiaoke.common.Guard
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

class EncryptUtilTest extends Specification {
    
    def setupSpec() {
        def i18nClient = Mock(com.fxiaoke.i18n.client.I18nClient)
        def i18nServiceImpl = Mock(com.fxiaoke.i18n.client.impl.I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(com.fxiaoke.i18n.client.I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    
    def setup() {
        // 确保使用默认的加密密钥
        EncryptUtil.encryptKey = "5&I(1wsJ25@zH+eU"
        def guard = new Guard(EncryptUtil.encryptKey)
        Whitebox.setInternalState(EncryptUtil.class, "encrypt", guard)
    }
    
    @Unroll
    def "测试加密解密功能，原文=#raw"() {
        when: "加密原始文本"
        def encoded = EncryptUtil.encode(raw)
        
        then: "加密结果不为空且不等于原文"
        encoded != null
        encoded != raw
        
        when: "解密加密后的文本"
        def decoded = EncryptUtil.decode(encoded)
        
        then: "解密后与原文一致"
        decoded == raw
        
        where:
        raw << ["测试文本", "123456", "!@#\$%^&*()_+", "abcDEF", "中文测试", "", "很长的测试文本" + "X" * 100]
    }
    
    def "测试不同密钥加密结果不同"() {
        given: "保存原始密钥"
        def originalKey = EncryptUtil.encryptKey
        def originalEncrypt = EncryptUtil.encrypt
        
        when: "使用新密钥"
        EncryptUtil.encryptKey = "NewKey1234567890"
        def newGuard = new Guard(EncryptUtil.encryptKey)
        Whitebox.setInternalState(EncryptUtil.class, "encrypt", newGuard)
        
        and: "加密相同的文本"
        def originalEncoded = originalEncrypt.encode("test")
        def newEncoded = EncryptUtil.encode("test")
        
        then: "加密结果不同"
        originalEncoded != newEncoded
        
        cleanup: "恢复原始密钥"
        EncryptUtil.encryptKey = originalKey
        Whitebox.setInternalState(EncryptUtil.class, "encrypt", originalEncrypt)
    }
    
    def "测试加密异常情况"() {
        given: "模拟Guard产生异常"
        def mockGuard = GroovyMock(Guard)
        mockGuard.encode(_) >> { throw new Exception("模拟加密异常") }
        def originalEncrypt = EncryptUtil.encrypt
        
        when: "注入模拟的Guard"
        Whitebox.setInternalState(EncryptUtil.class, "encrypt", mockGuard)
        
        and: "调用加密方法"
        EncryptUtil.encode("test")
        
        then: "产生RuntimeException异常"
        def e = thrown(RuntimeException)
        e.message.contains("cannot encode")
        
        cleanup: "恢复原始Guard"
        Whitebox.setInternalState(EncryptUtil.class, "encrypt", originalEncrypt)
    }
    
    def "测试解密异常情况"() {
        given: "模拟Guard产生异常"
        def mockGuard = GroovyMock(Guard)
        mockGuard.decode(_) >> { throw new Exception("模拟解密异常") }
        def originalEncrypt = EncryptUtil.encrypt
        
        when: "注入模拟的Guard"
        Whitebox.setInternalState(EncryptUtil.class, "encrypt", mockGuard)
        
        and: "调用解密方法"
        EncryptUtil.decode("invalidToken")
        
        then: "产生RuntimeException异常"
        def e = thrown(RuntimeException)
        e.message.contains("cannot decode")
        
        cleanup: "恢复原始Guard"
        Whitebox.setInternalState(EncryptUtil.class, "encrypt", originalEncrypt)
    }
} 