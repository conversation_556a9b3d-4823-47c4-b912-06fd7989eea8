package com.facishare.paas.appframework.common.util

import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.i18n.I18NKey
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.timezone.TimeZoneContextHolder
import com.facishare.paas.timezone.config.TimeZoneConfig
import org.apache.commons.lang3.StringUtils
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

/**
 * GenerateByAI
 * 测试内容描述：DateTimeUtils工具类的单元测试
 */
class DateTimeUtilsTest extends Specification {


    /**
     * GenerateByAI
     * 测试内容描述：测试解析ISO格式的日期时间字符串为时间戳
     */
    @Unroll
    def "parseISOLocateDateTime with #time and #fieldType should return correct timestamp"() {
        given:
        // 设置固定的时区
        def zoneId = ZoneId.of("Asia/Shanghai")

        when:
        // 使用预设的时区调用方法
        def result = DateTimeUtils.parseISOLocateDateTime(time, fieldType, zoneId)

        then:
        // 验证结果是否符合预期
        result == expectedTimestamp

        where:
        time                     | fieldType              | expectedTimestamp
        "12:30:45"               | IFieldType.TIME        | getTimeTimestamp("12:30:45", ZoneId.systemDefault())
        "2023-01-15"             | IFieldType.DATE        | getDateTimestamp("2023-01-15", ZoneId.systemDefault())
        "2023-01-15T12:30:45"    | IFieldType.DATE_TIME   | getDateTimeTimestamp("2023-01-15T12:30:45", ZoneId.systemDefault())
        "2023-01-15 12:30:45"    | IFieldType.DATE_TIME   | getDateTimeTimestamp("2023-01-15 12:30:45", ZoneId.systemDefault())
        "1642233645000"          | IFieldType.DATE_TIME   | 1642233645000L  // 数字格式的时间戳
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当传入不支持的字段类型时是否正确抛出异常
     */
    def "parseISOLocateDateTime should throw exception for unsupported field type"() {
        given:
        def time = "2023-01-15"
        def fieldType = "UNSUPPORTED_TYPE"
        def zoneId = ZoneId.systemDefault()

        when:
        DateTimeUtils.parseISOLocateDateTime(time, fieldType, zoneId)

        then:
        // 验证是否抛出了正确的异常
        thrown(ValidateException)
        // 不检查具体错误消息，因为它依赖于I18N配置
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试传入格式错误的日期时间字符串
     */
    def "parseISOLocateDateTime should throw exception for invalid time format"() {
        given:
        def time = "invalid-date-format"
        def fieldType = IFieldType.DATE
        def zoneId = ZoneId.systemDefault()

        when:
        DateTimeUtils.parseISOLocateDateTime(time, fieldType, zoneId)

        then:
        // 验证是否抛出了异常
        thrown(ValidateException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试智能识别字段类型的功能
     */
    def "parseISOLocateDateTime should intelligently identify date time format"() {
        given:
        def time = "2023-01-15 12:30:45"
        // 传入非DATE_TIME字段类型，但时间格式包含日期和时间
        def fieldType = IFieldType.DATE
        def zoneId = ZoneId.systemDefault()

        when:
        def result = DateTimeUtils.parseISOLocateDateTime(time, fieldType, zoneId)

        then:
        // 应转换为DATE_TIME类型并处理
        result == getDateTimeTimestamp(time, zoneId)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isGrayTimeZone方法
     */
    def "isGrayTimeZone should return correct gray status"() {
        given:
        // 模拟请求上下文
        def requestContext = Mock(RequestContext)
        requestContext.getTenantId() >> "test_tenant"

        when:
        // 当RequestContextManager返回null时
        RequestContextManager.metaClass.static.getContext = { -> null }
        def resultWhenContextNull = DateTimeUtils.isGrayTimeZone()

        then:
        // 应返回false
        !resultWhenContextNull

        when:
        // 当RequestContextManager返回有效上下文时，但无法模拟TimeZoneConfig
        // 直接测试方法的基本逻辑
        RequestContextManager.metaClass.static.getContext = { -> requestContext }
        def result = DateTimeUtils.isGrayTimeZone()

        then:
        // 由于无法模拟TimeZoneConfig，只验证方法不抛异常
        result != null

        cleanup:
        // 清理测试中使用的元编程
        RequestContextManager.metaClass = null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带有不同格式的时间字符串解析
     */
    @Unroll
    def "parseISOLocateDateTime with various time formats: #time"() {
        given:
        def fieldType = IFieldType.DATE_TIME
        def zoneId = ZoneId.systemDefault()

        when:
        def result = DateTimeUtils.parseISOLocateDateTime(time, fieldType, zoneId)

        then:
        result > 0  // 确保返回了有效的时间戳

        where:
        time << [
            "2023-01-15 12:30:45",      // 标准格式
            "2023-01-15 12:30",         // 无秒
            "2023-01-15 12:30:45:123",  // 带毫秒
            "2023-01-15T12:30:45",      // ISO 8601格式
        ]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件
     */
    def "parseISOLocateDateTime boundary cases"() {
        given:
        def zoneId = ZoneId.systemDefault()

        expect:
        // 测试年份边界 - 注意早期日期可能返回负数时间戳，这是正常的
        DateTimeUtils.parseISOLocateDateTime("1900-01-01", IFieldType.DATE, zoneId) != null
        DateTimeUtils.parseISOLocateDateTime("9999-12-31", IFieldType.DATE, zoneId) > 0

        // 测试过去和未来的时间点 - 由于时区差异，1970-01-01可能返回负数，这是正常的
        DateTimeUtils.parseISOLocateDateTime("1970-01-01T08:00:00", IFieldType.DATE_TIME, zoneId) != null
        DateTimeUtils.parseISOLocateDateTime("2100-01-01T00:00:00", IFieldType.DATE_TIME, zoneId) > 0
    }

    /**
     * 辅助方法：根据时间字符串计算TIME类型时间戳
     */
    private long getTimeTimestamp(String timeStr, ZoneId zoneId) {
        LocalTime localTime = LocalTime.parse(timeStr)
        LocalDateTime localDateTime = localTime.atDate(LocalDate.of(1970, 1, 1))
        return ZonedDateTime.of(localDateTime, zoneId).toInstant().toEpochMilli()
    }

    /**
     * 辅助方法：根据日期字符串计算DATE类型时间戳
     */
    private long getDateTimestamp(String dateStr, ZoneId zoneId) {
        LocalDate date = LocalDate.parse(dateStr)
        return date.atStartOfDay(zoneId).toEpochSecond() * 1000
    }

    /**
     * 辅助方法：根据日期时间字符串计算DATE_TIME类型时间戳
     */
    private long getDateTimeTimestamp(String dateTimeStr, ZoneId zoneId) {
        LocalDateTime dateTime
        if (dateTimeStr.contains("T")) {
            dateTime = LocalDateTime.parse(dateTimeStr)
        } else {
            // 根据格式选择合适的解析方式
            int colonCount = StringUtils.countMatches(dateTimeStr, ":")
            if (colonCount == 3) {
                dateTime = LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS"))
            } else if (colonCount == 2) {
                dateTime = LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            } else {
                dateTime = LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))
            }
        }
        return ZonedDateTime.of(dateTime, zoneId).toInstant().toEpochMilli()
    }
}