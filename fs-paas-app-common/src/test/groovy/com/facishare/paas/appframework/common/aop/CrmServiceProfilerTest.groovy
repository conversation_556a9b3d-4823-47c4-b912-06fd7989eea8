package com.facishare.paas.appframework.common.aop

import com.facishare.paas.appframework.core.exception.AppBusinessException
import com.facishare.paas.foundation.boot.exception.BadRequestException
import com.facishare.paas.metadata.exception.MetadataServiceException
import com.facishare.paas.metadata.exception.MetadataValidateException
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.sql.exception.ElasticSql2DslException
import org.powermock.reflect.Whitebox
import org.springframework.dao.DuplicateKeyException
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：CrmServiceProfiler类的单元测试
 */
class CrmServiceProfilerTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isFail方法对于不同类型异常的处理
     */
    @Unroll
    def "测试isFail方法对于#exceptionType异常返回#expectedResult"() {
        given: "创建CrmServiceProfiler实例和异常"
        def profiler = new CrmServiceProfiler()
        
        when: "调用isFail方法"
        def result = profiler.isFail(exception)
        
        then: "返回结果应符合预期"
        result == expectedResult
        
        where:
        exceptionType              | exception                          | expectedResult
        "MetadataServiceException" | new MetadataServiceException("test") | false
        "MetadataValidateException" | new MetadataValidateException("test") | false
        "BadRequestException"      | new BadRequestException("test")    | false
        "ElasticSql2DslException"  | new ElasticSql2DslException("test") | false
        "DuplicateKeyException"    | new DuplicateKeyException("test")  | false
        "RuntimeException"         | new RuntimeException("test")       | true
        "NullPointerException"     | new NullPointerException("test")   | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isFail方法对于嵌套异常的处理
     */
    def "测试isFail方法对于嵌套异常的处理"() {
        given: "创建CrmServiceProfiler实例和嵌套异常"
        def profiler = new CrmServiceProfiler()

        
        when: "异常中包含MetadataServiceException"
        def outerException2 = new RuntimeException("Outer", new MetadataServiceException("Inner"))
        def result2 = profiler.isFail(outerException2)
        
        then: "应返回false"
        !result2
        
        when: "异常中包含BadRequestException"
        def outerException3 = new RuntimeException("Outer", new BadRequestException("Inner"))
        def result3 = profiler.isFail(outerException3)
        
        then: "应返回false"
        !result3
        
        when: "异常中不包含指定类型的异常"
        def outerException4 = new RuntimeException("Outer", new IllegalArgumentException("Inner"))
        def result4 = profiler.isFail(outerException4)
        
        then: "应返回true"
        result4
    }


    /**
     * GenerateByAI
     * 测试内容描述：测试isFail方法处理null异常的情况
     */
    def "测试isFail方法处理null异常的情况"() {
        given: "创建CrmServiceProfiler实例"
        def profiler = new CrmServiceProfiler()
        
        when: "调用isFail方法传入null"
        def result = profiler.isFail(null)
        
        then: "应委托给父类处理，通常返回true"
        result == true
    }
} 