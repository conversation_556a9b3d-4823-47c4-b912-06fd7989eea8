package com.facishare.paas.appframework.common.util

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import com.google.common.collect.Lists
import spock.lang.Specification

class TreeViewUtilTest extends Specification {
    def "test getParentAndChildIds"() {
        given:
        List<IObjectData> objectDataList = Lists.newArrayList()
        def data1 = Spy(ObjectData)
        data1.setId("62bc2ab618ec4f00014ea659")
        def data2 = Spy(ObjectData)
        data2.setId("62cbd5a025b78300015b4560")
        data2.set("superior_channel", "62bc2ab618ec4f00014ea659")
        def data3 = Spy(ObjectData)
        data3.setId("62cbd61325b78300015b4b70")
        data3.set("superior_channel", "62cbd5a025b78300015b4560")
        def data4 = Spy(ObjectData)
        data4.setId("62cbd62125b78300015b4d2e")
        data4.set("superior_channel", "62cbd61325b78300015b4b70")
        objectDataList << data1
        objectDataList << data2
        objectDataList << data3
        objectDataList << data4
        when:
        AppFrameworkConfig.treeViewObjectAllowMaxHierarchy = allowLevel
        def ids = TreeViewUtil.getParentAndChildIds(objectDataList, "62bc2ab618ec4f00014ea659", "superior_channel", isThrowExp)
        then:
        ids.size() == level
        where:
        allowLevel | isThrowExp || level
        4          | false      || 4
        3          | false      || 3
    }

    def "test getParentAndChildIds validate"() {
        given:
        List<IObjectData> objectDataList = Lists.newArrayList()
        def data1 = Spy(ObjectData)
        data1.setId("62bc2ab618ec4f00014ea659")
        def data2 = Spy(ObjectData)
        data2.setId("62cbd5a025b78300015b4560")
        data2.set("superior_channel", "62bc2ab618ec4f00014ea659")
        def data3 = Spy(ObjectData)
        data3.setId("62cbd61325b78300015b4b70")
        data3.set("superior_channel", "62cbd5a025b78300015b4560")
        def data4 = Spy(ObjectData)
        data4.setId("62cbd62125b78300015b4d2e")
        data4.set("superior_channel", "62cbd61325b78300015b4b70")
        objectDataList << data1
        objectDataList << data2
        objectDataList << data3
        objectDataList << data4
        when:
        AppFrameworkConfig.treeViewObjectAllowMaxHierarchy = 3
        def ids = TreeViewUtil.getParentAndChildIds(objectDataList, "62bc2ab618ec4f00014ea659", "superior_channel", true)
        then:
        ValidateException e = thrown()
        e.getCause() == null
    }
}
