package com.facishare.paas.appframework.common.util

import com.facishare.paas.appframework.common.util.UdobjGrayUtil.Function
import com.facishare.paas.appframework.common.util.UdobjGrayUtil.GrayConfig
import com.facishare.paas.appframework.common.util.UdobjGrayUtil.Rule
import com.fxiaoke.release.GrayRule
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

class UdobjGrayUtilTest extends Specification {

    def setupSpec() {
        def i18nClient = Mock(com.fxiaoke.i18n.client.I18nClient)
        def i18nServiceImpl = Mock(com.fxiaoke.i18n.client.impl.I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(com.fxiaoke.i18n.client.I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def "测试isCustomObject方法"() {
        expect:
        UdobjGrayUtil.isCustomObject(objectApiName) == expected

        where:
        objectApiName      || expected
        "Account__c"       || true
        "AccountObj"       || false
        "Contact__c"       || true
        "Opportunity"      || false
        "udobj"            || true
        "UDOBJ"            || true
        "customUDOBJ"      || true
        null               || false
        ""                 || false
    }

    @Unroll
    def "测试isObjectAndTenantGray方法，当functionName=#functionName，tenantId=#tenantId，objectApiName=#objectApiName时，返回#expected"() {
        given:
        // 创建测试用的配置
        GrayConfig grayConfig = new GrayConfig()
        List<Function> functions = new ArrayList<>()
        
        // 创建测试function
        Function testFunction = new Function()
        testFunction.setFunctionName("testFunction")
        
        // 创建白名单规则
        List<Rule> whiteRules = new ArrayList<>()
        Rule whiteRule = new Rule()
        Set<String> whiteKeys = new HashSet<>()
        whiteKeys.add("AccountObj")
        whiteRule.setKeys(whiteKeys)
        GrayRule whiteEi = Mock(GrayRule)
        whiteEi.isAllow("tenant1") >> true
        whiteEi.isAllow("tenant2") >> false
        whiteRules.add(whiteRule)
        testFunction.setWhiteRule(whiteRules)
        
        // 创建黑名单规则
        Rule blackRule = new Rule()
        Set<String> blackKeys = new HashSet<>()
        blackKeys.add("ContactObj")
        blackRule.setKeys(blackKeys)
        GrayRule blackEi = Mock(GrayRule)
        blackEi.isAllow("tenant1") >> false
        blackEi.isAllow("tenant2") >> true
        testFunction.setBlackRule(blackRule)
        
        functions.add(testFunction)
        grayConfig.setFunctions(functions)
        
        // 注入测试配置
        Whitebox.setInternalState(UdobjGrayUtil.class, "GRAY_CONFIG", grayConfig)
        
        expect:
        UdobjGrayUtil.isObjectAndTenantGray(functionName, tenantId, objectApiName) == expected
        
        where:
        functionName     | tenantId  | objectApiName  || expected
        "testFunction"   | "tenant1" | "AccountObj"   || true    // 白名单对象，租户在灰度中
        "testFunction"   | "tenant2" | "AccountObj"   || false   // 白名单对象，租户不在灰度中
        "testFunction"   | "tenant1" | "ContactObj"   || false   // 黑名单对象，不应走黑名单规则
        "testFunction"   | "tenant2" | "OpportunityObj" || true  // 非黑名单对象，走黑名单租户规则
        "otherFunction"  | "tenant1" | "AccountObj"   || false   // 不存在的功能名
        null             | "tenant1" | "AccountObj"   || false   // 空功能名
        "testFunction"   | null      | "AccountObj"   || false   // 空租户
        "testFunction"   | "tenant1" | null           || false   // 空对象名
        "testFunction"   | "tenant1" | "Custom__c"    || true    // 自定义对象，当作udobj处理，AccountObj规则
    }

    def "测试isObjectAndTenantGray方法，当GRAY_CONFIG为null时"() {
        given:
        // 注入null配置
        Whitebox.setInternalState(UdobjGrayUtil.class, "GRAY_CONFIG", null)
        
        expect:
        !UdobjGrayUtil.isObjectAndTenantGray("testFunction", "tenant1", "AccountObj")
    }

    def "测试GrayConfig和Rule类"() {
        given:
        GrayConfig config = new GrayConfig()
        Function function = new Function()
        Rule rule = new Rule()
        
        Set<String> keys = new HashSet<>()
        keys.add("key1")
        keys.add("key2")
        
        when:
        rule.setKeys(keys)
        rule.setEi("tenant1")  // 使用字符串构造GrayRule
        
        function.setFunctionName("testFunction")
        function.setBlackRule(rule)
        
        List<Function> functions = new ArrayList<>()
        functions.add(function)
        config.setFunctions(functions)
        
        then:
        rule.getKeys() == keys
        rule.getEi() != null
        function.getFunctionName() == "testFunction"
        function.getBlackRule() == rule
        config.getFunctions() == functions
    }
} 