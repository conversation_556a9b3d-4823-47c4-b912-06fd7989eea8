package com.facishare.paas.appframework.common.util

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import org.slf4j.Logger
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：DebuggingLogger枚举的单元测试
 */
class DebuggingLoggerTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试DebuggingLogger的初始化和委托
     */
    def "测试DebuggingLogger的初始化"() {
        when: "获取DebuggingLogger实例"
        def logger = DebuggingLogger.DEBUGGING_LOGGER
        
        then: "实例应该非空"
        logger != null
        
        and: "检查委托Logger是否正确初始化"
        def delegateLogger = Whitebox.getInternalState(logger, "LOG")
        delegateLogger instanceof Logger
        delegateLogger.name == DebuggingLogger.class.name
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试DebuggingLogger的日志方法委托
     */
    def "测试DebuggingLogger的方法委托"() {
        given: "创建模拟Logger并注入到DebuggingLogger"
        def mockLogger = Mock(Logger)
        def logger = DebuggingLogger.DEBUGGING_LOGGER
        Whitebox.setInternalState(logger, "LOG", mockLogger)
        
        when: "调用info方法"
        logger.info("Test message")
        
        then: "委托给内部Logger"
        1 * mockLogger.info("Test message")
        

        when: "调用debug方法"
        logger.debug("Debug message")
        
        then: "委托给内部Logger"
        1 * mockLogger.debug("Debug message")
        
        when: "调用warn方法"
        logger.warn("Warning message")
        
        then: "委托给内部Logger"
        1 * mockLogger.warn("Warning message")
        
        when: "调用trace方法"
        logger.trace("Trace message")
        
        then: "委托给内部Logger"
        1 * mockLogger.trace("Trace message")
        
        when: "调用isDebugEnabled方法"
        mockLogger.isDebugEnabled() >> true
        def result = logger.isDebugEnabled()
        
        then: "委托给内部Logger并返回结果"
        1 * mockLogger.isDebugEnabled()
        result == true
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试DebuggingLogger的枚举值
     */
    def "测试DebuggingLogger的枚举值"() {
        expect: "应该只有一个枚举值"
        DebuggingLogger.values().length == 1
        DebuggingLogger.values()[0] == DebuggingLogger.DEBUGGING_LOGGER
        DebuggingLogger.valueOf("DEBUGGING_LOGGER") == DebuggingLogger.DEBUGGING_LOGGER
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试DebuggingLogger的线程安全性
     */
    def "测试DebuggingLogger的线程安全性"() {
        given: "多个线程同时使用同一个实例"
        def logger = DebuggingLogger.DEBUGGING_LOGGER
        def numThreads = 10
        def latch = new java.util.concurrent.CountDownLatch(numThreads)
        
        when: "多个线程同时调用实例方法"
        def threads = (1..numThreads).collect { 
            new Thread({
                try {
                    // 验证每个线程获取的都是同一个实例
                    assert DebuggingLogger.DEBUGGING_LOGGER == logger
                    // 调用一些方法，在真实环境不会出错
                    if (logger.isDebugEnabled()) {
                        logger.debug("Thread-${it} debug message")
                    }
                    logger.info("Thread-${it} info message")
                } finally {
                    latch.countDown()
                }
            })
        }
        
        threads.each { it.start() }
        latch.await()
        
        then: "不应抛出异常"
        notThrown(Exception)
    }
} 