package com.facishare.paas.appframework.common.util

import com.alibaba.druid.util.StringUtils
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.apache.commons.lang3.math.NumberUtils
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：ConvertUtils工具类的单元测试
 */
class ConvertUtilsTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试将整数集合转换为字符串集合的方法
     */
    @Unroll
    def "batchConvertIntegerToString 正常测试"() {
        given:
        def ints = input

        when:
        def result = ConvertUtils.batchConvertIntegerToString(ints)

        then:
        result == expected

        where:
        input                  | expected
        [1, 2, 3]              | ["1", "2", "3"]
        [0, -1, 100]           | ["0", "-1", "100"]
        []                     | []
        null                   | []
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试将字符串集合转换为整数集合的方法
     */
    @Unroll
    def "batchConvertStringToInteger 正常测试"() {
        given:
        def strList = input

        when:
        def result = ConvertUtils.batchConvertStringToInteger(strList)

        then:
        result == expected

        where:
        input                  | expected
        ["1", "2", "3"]        | [1, 2, 3]
        ["0", "-1", "100"]     | [0, -1, 100]
        []                     | []
        null                   | []
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试将字符串转换为整数的方法
     */
    @Unroll
    def "convertStringToInteger 正常测试"() {
        when:
        def result = ConvertUtils.convertStringToInteger(input)

        then:
        result == expected

        where:
        input    | expected
        "123"    | 123
        "0"      | 0
        "-1"     | -1
        null     | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试将字符串集合转换为整数列表，并过滤无效ID的方法
     */
    @Unroll
    def "convertStringCollectionToIntegerList 正常测试"() {
        given:
        // Mock静态方法
        def originalIsEffectiveId = ConvertUtils.metaClass.static.isEffectiveId
        ConvertUtils.metaClass.static.isEffectiveId = { String id -> 
            if (id == "0" || id == null || !NumberUtils.isDigits(id)) {
                return false
            }
            return true
        }

        when:
        def result = ConvertUtils.convertStringCollectionToIntegerList(input)

        then:
        result == expected

        cleanup:
        // 恢复原始方法
        ConvertUtils.metaClass.static.isEffectiveId = originalIsEffectiveId

        where:
        input                     | expected
        ["1", "2", "3"]           | [1, 2, 3]
        ["1", "0", "3"]           | [1, 3]
        ["1", "abc", "3"]         | [1, 3]
        []                        | []
        null                      | []
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试将整数转换为字符串的方法
     */
    @Unroll
    def "integerToString 正常测试"() {
        when:
        def result = ConvertUtils.integerToString(input)

        then:
        result == expected

        where:
        input    | expected
        123      | "123"
        0        | "0"
        -1       | "-1"
        null     | null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试判断字符串是否为有效ID的方法
     */
    @Unroll
    def "isEffectiveId 正常测试"() {
        when:
        def result = ConvertUtils.isEffectiveId(input)

        then:
        result == expected

        where:
        input    | expected
        "123"    | true
        "0"      | false
        "-1"     | false
        "abc"    | false
        null     | false
        ""       | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试移除手机号中空格的方法
     */
    @Unroll
    def "removeSpaceForPhoneNumber 正常测试"() {
        when:
        def result = ConvertUtils.removeSpaceForPhoneNumber(input)

        then:
        result == expected

        where:
        input           | expected
        "137 1234 5678" | "13712345678"
        "137"           | "137"
        ""              | ""
        null            | ""
        123456          | "123456"
    }
} 