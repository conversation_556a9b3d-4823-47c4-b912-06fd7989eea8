package com.facishare.paas.appframework.common.util

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Field

class HtmlUtilsTest extends Specification {
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    @Unroll
    def "测试parseText方法，HTML=#html，期望结果=#expected"() {
        expect: "解析HTML文本"
        HtmlUtils.parseText(html) == expected
        
        where:
        html                                        || expected
        "<p>测试文本</p>"                           || "测试文本"
        "<div><span>Hello</span> <b>World</b></div>"|| "Hello World"
        "<p>段落1</p><p>段落2</p>"                  || "段落1段落2"
        "<h1>标题</h1><div>内容</div>"              || "标题内容"
        ""                                           || ""
        "<div></div>"                                || ""
        "<div>   </div>"                             || ""
        "<p>   空格前后   </p>"                      || "空格前后"
        "<script>alert('test');</script><p>内容</p>" || "内容"
        "<div>换行\n文本</div>"                      || "换行文本"
        "<div>包含<br/>换行</div>"                   || "包含换行"
        "<table><tr><td>表格</td></tr></table>"      || "表格"
        "<ul><li>列表1</li><li>列表2</li></ul>"      || "列表1列表2"
    }
    
    @Unroll
    def "测试hasScriptTag方法，HTML=#html，期望结果=#expected"() {
        expect: "检测是否有script标签"
        HtmlUtils.hasScriptTag(html) == expected
        
        where:
        html                                             || expected
        "<script>alert('test');</script>"                 || true
        "<div><script>console.log('test');</script></div>"|| true
        "<SCRIPT>alert('test');</SCRIPT>"                 || true
        "<script></script>"                               || true
        "<div>no script</div>"                            || false
        "<p>script文本，但不是标签</p>"                   || false
        ""                                                || false
        "<div>"                                           || false
        "<style>.test{color:red;}</style>"                || false
        "<scr" + "ipt>alert('test');</scr" + "ipt>"       || true
    }
    
    def "测试doParse方法递归处理"() {
        given: "复杂的嵌套HTML"
        def html = """
            <div>
                <h1>标题</h1>
                <p>段落1<span>内嵌段落</span></p>
                <ul>
                    <li><a href="#">链接1</a></li>
                    <li><a href="#">链接2</a></li>
                </ul>
                <!-- 这是注释 -->
                <div>最后一个<b>段落</b></div>
            </div>
        """
        
        when: "解析HTML"
        def result = HtmlUtils.parseText(html)
        
        then: "正确处理所有嵌套元素"
        result.contains("标题")
        result.contains("段落1")
        result.contains("内嵌段落")
        result.contains("链接1")
        result.contains("链接2")
        result.contains("最后一个")
        result.contains("段落")
        !result.contains("这是注释")
    }
    
    def "测试空白和非文本节点处理"() {
        given: "含有空白和非文本节点的HTML"
        def html = """
            <div>
                
                <img src="test.jpg" alt="测试图片">
                <input type="text" value="输入框">
                
                <p>   有实际内容的段落   </p>
                
            </div>
        """
        
        when: "解析HTML"
        def result = HtmlUtils.parseText(html)
        
        then: "只提取文本内容"
        result == "有实际内容的段落"
        !result.contains("测试图片")  // alt属性不会被提取
        !result.contains("输入框")   // value属性不会被提取
    }
} 