package com.facishare.paas.appframework.common.util

import com.facishare.paas.I18N
import com.facishare.paas.metadata.util.GetI18nKeyUtil
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class I18NUtilsTest extends Specification {
    
    def setupSpec() {
        def i18nClient = Mock(com.fxiaoke.i18n.client.I18nClient)
        def i18nServiceImpl = Mock(com.fxiaoke.i18n.client.impl.I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(com.fxiaoke.i18n.client.I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    
    def "测试getObjectDisplayName方法，正常情况"() {
        given: "模拟GetI18nKeyUtil和I18N"
        GroovyMock(GetI18nKeyUtil, global: true)
        GroovyMock(I18N, global: true)
        
        and: "设置模拟行为"
        def apiNameList = ["AccountObj", "ContactObj", "OpportunityObj"]
        GetI18nKeyUtil.getDescribeDisplayNameKey("AccountObj") >> "account_display_name"
        GetI18nKeyUtil.getDescribeDisplayNameKey("ContactObj") >> "contact_display_name"
        GetI18nKeyUtil.getDescribeDisplayNameKey("OpportunityObj") >> "opportunity_display_name"
        
        I18N.text("account_display_name") >> "客户"
        I18N.text("contact_display_name") >> "联系人"
        I18N.text("opportunity_display_name") >> "商机"
        
        when: "调用getObjectDisplayName方法"
        def result = I18NUtils.getObjectDisplayName(apiNameList)
        
        then: "返回正确的映射"
        result.size() == 3
        result["AccountObj"] == "客户"
        result["ContactObj"] == "联系人"
        result["OpportunityObj"] == "商机"
    }
    
    def "测试getObjectDisplayName方法，传入空列表"() {
        given: "模拟GetI18nKeyUtil和I18N"
        GroovyMock(GetI18nKeyUtil, global: true)
        GroovyMock(I18N, global: true)
        
        when: "传入空列表"
        def result = I18NUtils.getObjectDisplayName([])
        
        then: "返回空映射"
        result.size() == 0
    }
    
    def "测试getObjectDisplayName方法，传入null"() {
        given: "模拟GetI18nKeyUtil和I18N"
        GroovyMock(GetI18nKeyUtil, global: true)
        GroovyMock(I18N, global: true)
        
        when: "传入null"
        def result = I18NUtils.getObjectDisplayName(null)
        
        then: "返回空映射"
        result.size() == 0
    }
    
    def "测试getObjectDisplayName方法，包含重复apiName"() {
        given: "模拟GetI18nKeyUtil和I18N"
        GroovyMock(GetI18nKeyUtil, global: true)
        GroovyMock(I18N, global: true)
        
        and: "设置模拟行为"
        def apiNameList = ["AccountObj", "AccountObj", "ContactObj"]
        GetI18nKeyUtil.getDescribeDisplayNameKey("AccountObj") >> "account_display_name"
        GetI18nKeyUtil.getDescribeDisplayNameKey("ContactObj") >> "contact_display_name"
        
        I18N.text("account_display_name") >> "客户"
        I18N.text("contact_display_name") >> "联系人"
        
        when: "调用getObjectDisplayName方法"
        def result = I18NUtils.getObjectDisplayName(apiNameList)
        
        then: "返回正确的映射，重复apiName只有一个"
        result.size() == 2
        result["AccountObj"] == "客户"
        result["ContactObj"] == "联系人"
    }
} 