package com.facishare.paas.appframework.common.util

import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：StoneCGIConfig配置类的单元测试
 */
class StoneCGIConfigTest extends Specification {

    def "test getFileShareSkey returns configured value"() {
        given: "设置测试用的sKey值"
        String testSkey = "test-skey-12345"
        Whitebox.setInternalState(StoneCGIConfig, "fileShareSkey", testSkey)

        when: "调用getFileShareSkey方法"
        def result = StoneCGIConfig.getFileShareSkey()

        then: "应该返回设置的值"
        result == testSkey
    }

    def "test getFileShareSkey returns empty string when not configured"() {
        given: "设置空的sKey值"
        Whitebox.setInternalState(StoneCGIConfig, "fileShareSkey", "")

        when: "调用getFileShareSkey方法"
        def result = StoneCGIConfig.getFileShareSkey()

        then: "应该返回空字符串"
        result == ""
    }

    def "test getFileShareSkey returns null when set to null"() {
        given: "设置null的sKey值"
        Whitebox.setInternalState(StoneCGIConfig, "fileShareSkey", null)

        when: "调用getFileShareSkey方法"
        def result = StoneCGIConfig.getFileShareSkey()

        then: "应该返回null"
        result == null
    }
}
