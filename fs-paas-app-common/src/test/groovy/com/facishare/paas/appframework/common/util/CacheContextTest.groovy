package com.facishare.paas.appframework.common.util

import com.facishare.paas.appframework.core.model.RequestContextManager
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：CacheContext工具类的单元测试
 */
class CacheContextTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        // 清空缓存上下文，确保测试互不影响
        CacheContext.clearContext()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CacheContext的实例获取与设置方法
     */
    def "测试实例获取与设置"() {
        when: "获取默认的CacheContext实例"
        def context = CacheContext.getContext()

        then: "应返回非空实例"
        context != null

        when: "设置一个新的CacheContext实例"
        def newContext = new CacheContext()
        CacheContext.setContext(newContext)

        and: "再次获取实例"
        def retrievedContext = CacheContext.getContext()

        then: "应返回新设置的实例"
        retrievedContext == newContext

        when: "清空上下文"
        CacheContext.clearContext()

        and: "再次获取实例"
        def contextAfterClear = CacheContext.getContext()

        then: "应返回新的实例，不等于之前设置的实例"
        contextAfterClear != newContext
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CacheContext的缓存操作方法
     */
    def "测试缓存操作方法"() {
        given: "准备CacheContext实例"
        def context = CacheContext.getContext()
        def key = "test_key"
        def value = "test_value"

        when: "缓存尚未设置时，检查缓存是否存在"
        def containsBeforeSet = context.containsCache(key)

        then: "应返回false"
        !containsBeforeSet

        when: "设置缓存"
        context.setCache(key, value)

        and: "再次检查缓存是否存在"
        def containsAfterSet = context.containsCache(key)

        then: "应返回true"
        containsAfterSet

        when: "获取缓存"
        def cachedValue = context.getCache(key)

        then: "应返回设置的值"
        cachedValue == value

        when: "移除缓存"
        def removedValue = context.removeCache(key)

        then: "应返回被移除的值"
        removedValue == value

        and: "缓存应被移除"
        !context.containsCache(key)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CacheContext与RequestContextManager的集成
     */
    def "测试与RequestContextManager的集成"() {
        given: "模拟RequestContextManager的contextRemoveListener"
        def listener
        GroovyMock(RequestContextManager, global: true)
        
        when: "手动触发静态初始化块，注册监听器"
        RequestContextManager.addContextRemoveListener(_) >> { args -> listener = args[0] }
        Whitebox.invokeMethod(CacheContext.class, "initializeRequestContextListener")
        
        and: "设置一个CacheContext实例和缓存"
        def context = new CacheContext()
        CacheContext.setContext(context)
        context.setCache("test_key", "test_value")
        
        and: "触发监听器"
        if (listener) {
            listener.accept(null)
        }
        
        then: "CacheContext应被清空"
        CacheContext.getContext() != context
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试多线程环境下的CacheContext
     */
    def "测试多线程环境下的CacheContext"() {
        given: "创建两个线程，每个线程使用独立的CacheContext实例"
        def thread1Result
        def thread2Result
        
        when: "线程1设置缓存"
        def thread1 = new Thread({
            def context = CacheContext.getContext()
            context.setCache("key", "value1")
            thread1Result = context
        })
        thread1.start()
        thread1.join()
        
        and: "线程2获取并修改缓存"
        def thread2 = new Thread({
            def context = CacheContext.getContext()
            context.setCache("key", "value2")
            thread2Result = context
        })
        thread2.start()
        thread2.join()
        
        then: "两个线程应使用不同的CacheContext实例"
        thread1Result != thread2Result
        
        and: "每个线程的CacheContext实例应包含不同的缓存值"
        thread1Result.getCache("key") == "value1"
        thread2Result.getCache("key") == "value2"
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试CacheContext的线程安全性
     */
    def "测试CacheContext的线程安全性"() {
        given: "准备一个共享的CacheContext实例"
        def context = new CacheContext()
        def numThreads = 10
        def numOperationsPerThread = 100
        def latch = new java.util.concurrent.CountDownLatch(numThreads)
        
        when: "多个线程同时对同一个CacheContext实例进行操作"
        (1..numThreads).collect { threadIndex ->
            new Thread({
                try {
                    (1..numOperationsPerThread).each { i ->
                        def key = "key_${threadIndex}_${i}"
                        def value = "value_${threadIndex}_${i}"
                        context.setCache(key, value)
                        assert context.getCache(key) == value
                        assert context.containsCache(key)
                        
                        // 随机移除一些缓存
                        if (i % 2 == 0) {
                            def removedValue = context.removeCache(key)
                            assert removedValue == value
                            assert !context.containsCache(key)
                        }
                    }
                } finally {
                    latch.countDown()
                }
            })
        }.each { it.start() }
        
        // 等待所有线程完成
        latch.await()
        
        then: "不应抛出并发异常"
        notThrown(Exception)
        
        and: "缓存应包含预期的键值对"
        (1..numThreads).each { threadIndex ->
            (1..numOperationsPerThread).each { i ->
                def key = "key_${threadIndex}_${i}"
                if (i % 2 != 0) {  // 奇数索引的缓存项应该保留
                    assert context.containsCache(key)
                    assert context.getCache(key) == "value_${threadIndex}_${i}"
                } else {  // 偶数索引的缓存项应该已被移除
                    assert !context.containsCache(key)
                }
            }
        }
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：手动初始化RequestContextManager监听器的方法
     */
    private static void initializeRequestContextListener() {
        // 模拟CacheContext类的静态初始化块
        RequestContextManager.addContextRemoveListener({ CacheContext.clearContext() })
    }
} 