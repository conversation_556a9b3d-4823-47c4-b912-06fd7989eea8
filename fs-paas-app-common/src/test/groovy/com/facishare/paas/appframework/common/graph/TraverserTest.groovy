package com.facishare.paas.appframework.common.graph

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：Traverser图遍历器的单元测试
 */
class TraverserTest extends Specification {

    def "test forGraph creates graph traverser"() {
        given: "创建图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.putEdge("A", "B")

        when: "创建图遍历器"
        def traverser = Traverser.forGraph(graph)

        then: "应该成功创建遍历器"
        traverser != null
    }

    def "test forTree creates tree traverser"() {
        given: "创建树结构的图"
        def tree = GraphBuilder.directed().build()
        tree.addNode("root")
        tree.addNode("left")
        tree.addNode("right")
        tree.putEdge("root", "left")
        tree.putEdge("root", "right")

        when: "创建树遍历器"
        def traverser = Traverser.forTree(tree)

        then: "应该成功创建遍历器"
        traverser != null
    }

    def "test forTree with undirected graph throws exception"() {
        given: "创建无向图"
        def undirectedGraph = GraphBuilder.undirected().build()
        undirectedGraph.addNode("A")

        when: "尝试创建树遍历器"
        Traverser.forTree(undirectedGraph)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test breadthFirst traversal on simple graph"() {
        given: "创建简单图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.addNode("D")
        graph.putEdge("A", "B")
        graph.putEdge("A", "C")
        graph.putEdge("B", "D")

        when: "执行广度优先遍历"
        def traverser = Traverser.forGraph(graph)
        def result = traverser.breadthFirst("A").toList()

        then: "应该按广度优先顺序遍历"
        result[0] == "A"
        result[1] in ["B", "C"]
        result[2] in ["B", "C"]
        result[3] == "D"
        result.size() == 4
    }

    def "test breadthFirst traversal with multiple start nodes"() {
        given: "创建图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.addNode("D")
        graph.putEdge("A", "C")
        graph.putEdge("B", "D")

        when: "从多个起始节点执行广度优先遍历"
        def traverser = Traverser.forGraph(graph)
        def result = traverser.breadthFirst(["A", "B"]).toList()

        then: "应该遍历所有可达节点"
        result.size() == 4
        result.containsAll(["A", "B", "C", "D"])
    }

    def "test breadthFirst traversal with empty start nodes"() {
        given: "创建图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")

        when: "使用空起始节点列表"
        def traverser = Traverser.forGraph(graph)
        def result = traverser.breadthFirst([]).toList()

        then: "应该返回空结果"
        result.isEmpty()
    }

    def "test depthFirstPreOrder traversal"() {
        given: "创建树状图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.addNode("D")
        graph.addNode("E")
        graph.putEdge("A", "B")
        graph.putEdge("A", "C")
        graph.putEdge("B", "D")
        graph.putEdge("B", "E")

        when: "执行深度优先前序遍历"
        def traverser = Traverser.forGraph(graph)
        def result = traverser.depthFirstPreOrder("A").toList()

        then: "应该按前序遍历顺序"
        result[0] == "A"
        result.contains("B")
        result.contains("C")
        result.contains("D")
        result.contains("E")
        result.size() == 5
    }

    def "test depthFirstPostOrder traversal"() {
        given: "创建树状图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.putEdge("A", "B")
        graph.putEdge("A", "C")

        when: "执行深度优先后序遍历"
        def traverser = Traverser.forGraph(graph)
        def result = traverser.depthFirstPostOrder("A").toList()

        then: "应该按后序遍历顺序"
        result.last() == "A" // 根节点最后访问
        result.contains("B")
        result.contains("C")
        result.size() == 3
    }

    def "test traversal with cycles"() {
        given: "创建有环图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")
        graph.putEdge("C", "A") // 形成环

        when: "执行广度优先遍历"
        def traverser = Traverser.forGraph(graph)
        def result = traverser.breadthFirst("A").toList()

        then: "应该正确处理环，每个节点只访问一次"
        result.size() == 3
        result.containsAll(["A", "B", "C"])
    }

    def "test traversal with self loop"() {
        given: "创建有自环的图"
        def graph = GraphBuilder.directed().allowsSelfLoops(true).build()
        graph.addNode("A")
        graph.addNode("B")
        graph.putEdge("A", "A") // 自环
        graph.putEdge("A", "B")

        when: "执行广度优先遍历"
        def traverser = Traverser.forGraph(graph)
        def result = traverser.breadthFirst("A").toList()

        then: "应该正确处理自环"
        result.size() == 2
        result.containsAll(["A", "B"])
    }

    def "test traversal on disconnected graph"() {
        given: "创建断开的图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.addNode("D")
        graph.putEdge("A", "B")
        graph.putEdge("C", "D") // 断开的组件

        when: "从A开始遍历"
        def traverser = Traverser.forGraph(graph)
        def result = traverser.breadthFirst("A").toList()

        then: "应该只遍历连通的部分"
        result.size() == 2
        result.containsAll(["A", "B"])
        !result.contains("C")
        !result.contains("D")
    }

    def "test tree traversal breadthFirst"() {
        given: "创建树"
        def tree = GraphBuilder.directed().build()
        tree.addNode("root")
        tree.addNode("left")
        tree.addNode("right")
        tree.addNode("leftLeft")
        tree.addNode("leftRight")
        tree.putEdge("root", "left")
        tree.putEdge("root", "right")
        tree.putEdge("left", "leftLeft")
        tree.putEdge("left", "leftRight")

        when: "使用树遍历器执行广度优先遍历"
        def traverser = Traverser.forTree(tree)
        def result = traverser.breadthFirst("root").toList()

        then: "应该按层级遍历"
        result[0] == "root"
        result[1] in ["left", "right"]
        result[2] in ["left", "right"]
        result[3] in ["leftLeft", "leftRight"]
        result[4] in ["leftLeft", "leftRight"]
        result.size() == 5
    }

    def "test traversal with single node"() {
        given: "创建单节点图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")

        when: "遍历单节点"
        def traverser = Traverser.forGraph(graph)
        def breadthResult = traverser.breadthFirst("A").toList()
        def depthPreResult = traverser.depthFirstPreOrder("A").toList()
        def depthPostResult = traverser.depthFirstPostOrder("A").toList()

        then: "所有遍历都应该返回单个节点"
        breadthResult == ["A"]
        depthPreResult == ["A"]
        depthPostResult == ["A"]
    }

    def "test traversal with non-existent start node"() {
        given: "创建图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")

        when: "从不存在的节点开始遍历"
        def traverser = Traverser.forGraph(graph)
        traverser.breadthFirst("NonExistent").toList()

        then: "应该抛出异常"
        thrown(IllegalArgumentException)
    }

    def "test null graph throws exception"() {
        when: "使用null图创建遍历器"
        Traverser.forGraph(null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test null tree throws exception"() {
        when: "使用null树创建遍历器"
        Traverser.forTree(null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test complex graph traversal"() {
        given: "创建复杂图"
        def graph = GraphBuilder.directed().build()
        ["A", "B", "C", "D", "E", "F"].each { graph.addNode(it) }
        graph.putEdge("A", "B")
        graph.putEdge("A", "C")
        graph.putEdge("B", "D")
        graph.putEdge("C", "E")
        graph.putEdge("D", "F")
        graph.putEdge("E", "F")

        when: "执行各种遍历"
        def traverser = Traverser.forGraph(graph)
        def breadthResult = traverser.breadthFirst("A").toList()
        def depthPreResult = traverser.depthFirstPreOrder("A").toList()
        def depthPostResult = traverser.depthFirstPostOrder("A").toList()

        then: "所有遍历都应该访问所有可达节点"
        breadthResult.size() == 6
        depthPreResult.size() == 6
        depthPostResult.size() == 6
        breadthResult.containsAll(["A", "B", "C", "D", "E", "F"])
        depthPreResult.containsAll(["A", "B", "C", "D", "E", "F"])
        depthPostResult.containsAll(["A", "B", "C", "D", "E", "F"])
        
        // 验证遍历顺序特性
        breadthResult[0] == "A" // 广度优先：起始节点第一个
        depthPreResult[0] == "A" // 深度优先前序：起始节点第一个
        depthPostResult.last() == "A" // 深度优先后序：起始节点最后一个
    }
}
