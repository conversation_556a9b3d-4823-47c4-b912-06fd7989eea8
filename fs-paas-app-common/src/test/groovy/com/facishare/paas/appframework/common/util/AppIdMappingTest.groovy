package com.facishare.paas.appframework.common.util

import com.google.common.collect.BiMap
import com.google.common.collect.HashBiMap
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：AppIdMapping工具类的单元测试
 */
class AppIdMappingTest extends Specification {

    def setup() {
        // 设置测试用的appIdMapping
        BiMap<String, String> testMapping = HashBiMap.create()
        testMapping.put("prm", "app001")
        testMapping.put("fxt", "app002") 
        testMapping.put("masterDataApp", "app003")
        testMapping.put("dht", "app004")
        testMapping.put("fwt", "app005")
        Whitebox.setInternalState(AppIdMapping, "appIdMapping", testMapping)
    }

    def "test constants are defined correctly"() {
        expect:
        AppIdMapping.DISPATCH_ORDERS == "DISPATCH_ORDERS"
        AppIdMapping.PRM_APP_ID == "prm"
        AppIdMapping.FXT_APP_ID == "fxt"
        AppIdMapping.MASTER_DATA_APP == "masterDataApp"
        AppIdMapping.DHT_APP_ID == "dht"
        AppIdMapping.FWT_APP_ID == "fwt"
    }

    @Unroll
    def "getNamedAppId should return correct app name for appId: #appId"() {
        when:
        def result = AppIdMapping.getNamedAppId(appId)

        then:
        result == expected

        where:
        appId    | expected
        "app001" | "prm"
        "app002" | "fxt"
        "app003" | "masterDataApp"
        "app004" | "dht"
        "app005" | "fwt"
        "app999" | null
        null     | null
    }

    @Unroll
    def "getAppIdByName should return correct appId for name: #name"() {
        when:
        def result = AppIdMapping.getAppIdByName(name)

        then:
        result == expected

        where:
        name            | expected
        "prm"           | "app001"
        "fxt"           | "app002"
        "masterDataApp" | "app003"
        "dht"           | "app004"
        "fwt"           | "app005"
        "unknown"       | null
        null            | null
    }

    @Unroll
    def "isPRM should return #expected for appId: #appId"() {
        when:
        def result = AppIdMapping.isPRM(appId)

        then:
        result == expected

        where:
        appId    | expected
        "app001" | true
        "app002" | false
        "app003" | false
        "app999" | false
        null     | false
    }

    @Unroll
    def "isFXT should return #expected for appId: #appId"() {
        when:
        def result = AppIdMapping.isFXT(appId)

        then:
        result == expected

        where:
        appId    | expected
        "app001" | false
        "app002" | true
        "app003" | false
        "app999" | false
        null     | false
    }

    @Unroll
    def "isTargetApp should return #expected for appId: #appId and appKey: #appKey"() {
        when:
        def result = AppIdMapping.isTargetApp(appId, appKey)

        then:
        result == expected

        where:
        appId    | appKey          | expected
        "app001" | "prm"           | true
        "app002" | "fxt"           | true
        "app001" | "fxt"           | false
        "app002" | "prm"           | false
        "app999" | "prm"           | false
        "app001" | "unknown"       | false
        null     | "prm"           | false
        "app001" | null            | false
        null     | null            | false
        ""       | "prm"           | false
        "app001" | ""              | false
    }
}
