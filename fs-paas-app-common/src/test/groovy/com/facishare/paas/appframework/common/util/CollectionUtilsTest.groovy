package com.facishare.paas.appframework.common.util

import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll
import java.util.Objects
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import java.util.function.BiPredicate
import java.util.function.Function
import java.util.stream.Collectors
import org.apache.commons.collections4.CollectionUtils as ApacheCollectionUtils

class CollectionUtilsTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试notEmpty方法对集合类型的判断
     */
    @Unroll
    def "notEmpty collection test with #desc"() {
        expect:
        CollectionUtils.notEmpty(collection) == result

        where:
        desc       | collection || result
        "非空集合" | [1, 2, 3]  || true
        "空集合"   | []         || false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试notEmpty方法对集合类型的null值判断
     */
    def "notEmpty collection test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("notEmpty", Collection.class)

        expect:
        method.invoke(null, [null] as Object[]) == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试empty方法对集合类型的判断
     */
    @Unroll
    def "empty collection test with #desc"() {
        expect:
        CollectionUtils.empty(collection) == result

        where:
        desc       | collection || result
        "非空集合" | [1, 2, 3]  || false
        "空集合"   | []         || true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试empty方法对集合类型的null值判断
     */
    def "empty collection test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("empty", Collection.class)

        expect:
        method.invoke(null, [null] as Object[]) == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试empty方法对Map类型的判断
     */
    @Unroll
    def "empty map test with #desc"() {
        expect:
        CollectionUtils.empty(map) == result

        where:
        desc      | map          || result
        "非空Map" | [a: 1, b: 2] || false
        "空Map"   | [:]          || true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试empty方法对Map类型的null值判断
     */
    def "empty map test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("empty", Map.class)

        expect:
        method.invoke(null, [null] as Object[]) == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试notEmpty方法对Map类型的判断
     */
    @Unroll
    def "notEmpty map test with #desc"() {
        expect:
        CollectionUtils.notEmpty(map) == result

        where:
        desc      | map          || result
        "非空Map" | [a: 1, b: 2] || true
        "空Map"   | [:]          || false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试notEmpty方法对Map类型的null值判断
     */
    def "notEmpty map test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("notEmpty", Map.class)

        expect:
        method.invoke(null, [null] as Object[]) == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对List类型的转换
     */
    def "nullToEmpty list test"() {
        given:
        List<Integer> emptyList = []
        List<Integer> normalList = [1, 2, 3]

        expect:
        // 非null值测试
        CollectionUtils.nullToEmpty(emptyList).isEmpty()
        CollectionUtils.nullToEmpty(normalList) == normalList
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对List类型的null值转换
     */
    def "nullToEmpty list test with null"() {
        expect:
        // 明确指定类型并用完全限定名称调用方法
        def result = com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty((List) null)
        result != null
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对Set类型的转换
     */
    def "nullToEmpty set test"() {
        given:
        Set<Integer> emptySet = [] as Set
        Set<Integer> normalSet = [1, 2, 3] as Set

        expect:
        // 非null值测试
        CollectionUtils.nullToEmpty(emptySet).isEmpty()
        CollectionUtils.nullToEmpty(normalSet) == normalSet
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对Set类型的null值转换
     */
    def "nullToEmpty set test with null"() {
        expect:
        // 明确指定类型并用完全限定名称调用方法
        def result = com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty((Set) null)
        result != null
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对Map类型的转换
     */
    def "nullToEmpty map test"() {
        given:
        Map<String, Integer> emptyMap = [:]
        Map<String, Integer> normalMap = [a: 1, b: 2]

        expect:
        // 非null值测试
        CollectionUtils.nullToEmpty(emptyMap).isEmpty()
        CollectionUtils.nullToEmpty(normalMap) == normalMap
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试nullToEmpty方法对Map类型的null值转换
     */
    def "nullToEmpty map test with null"() {
        expect:
        // 明确指定类型并用完全限定名称调用方法
        def result = com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty((Map) null)
        result != null
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试size方法获取集合大小
     */
    @Unroll
    def "size collection test with #desc"() {
        expect:
        CollectionUtils.size(collection) == result

        where:
        desc              | collection || result
        "空集合"          | []         || 0
        "有3个元素的集合" | [1, 2, 3]  || 3
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试size方法对Collection的null值处理
     */
    def "size collection test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("size", Collection.class)

        expect:
        method.invoke(null, [null] as Object[]) == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试size方法获取Map大小
     */
    @Unroll
    def "size map test with #desc"() {
        expect:
        CollectionUtils.size(map) == result

        where:
        desc             | map          || result
        "空Map"          | [:]          || 0
        "有2个元素的Map" | [a: 1, b: 2] || 2
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试size方法对Map的null值处理
     */
    def "size map test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("size", Map.class)

        expect:
        method.invoke(null, [null] as Object[]) == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSingleton方法判断集合是否只有一个元素
     */
    @Unroll
    def "isSingleton test with #desc"() {
        expect:
        CollectionUtils.isSingleton(collection) == result

        where:
        desc       | collection || result
        "空集合"   | []         || false
        "1个元素"  | [1]        || true
        "多个元素" | [1, 2, 3]  || false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSingleton方法处理null值
     */
    def "isSingleton test with null"() {
        given:
        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def method = utilsClass.getMethod("isSingleton", Collection.class)

        expect:
        method.invoke(null, [null] as Object[]) == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试firstNotEmpty方法获取第一个非空集合
     */
    def "firstNotEmpty test 1"() {
        given:
        def emptyList = []
        def nullList = null
        def list1 = [1, 2, 3]
        def list2 = [4, 5]

        Collection<?> emptyCollection = emptyList
        Collection<?> nullCollection = nullList
        Collection<?> collection1 = list1
        Collection<?> collection2 = list2

        expect:
        CollectionUtils.firstNotEmpty([nullCollection, emptyCollection] as Collection[]) == null
        CollectionUtils.firstNotEmpty([nullCollection, collection1, collection2] as Collection[]) == collection1
        CollectionUtils.firstNotEmpty([emptyCollection, collection2] as Collection[]) == collection2
    }


    /**
     * GenerateByAI
     * 测试内容描述：测试concatAndNotContainEmpty方法合并多个列表并去除空值
     */
    def "concatAndNotContainEmpty test"() {
        given:
        def list1 = [1, null, 3]
        def list2 = [null, 3, ""]
        def list3 = ["", 4, 5]
        def emptyList = []
        def nullList = null

        expect:
        // 合并并去除空值
        CollectionUtils.concatAndNotContainEmpty(list1, list2, list3) == [1, 3, 3, 4, 5]
        // 忽略空列表
        CollectionUtils.concatAndNotContainEmpty(list1, emptyList, list3) == [1, 3, 4, 5]
        // 忽略null
        CollectionUtils.concatAndNotContainEmpty(list1, nullList, list3) == [1, 3, 4, 5]
        // 所有都为空时返回空列表
        CollectionUtils.concatAndNotContainEmpty(emptyList, nullList) == []
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试splitMap方法在不同chunkSize下的行为
     */
    def "splitMap"() {
        expect:
        def result = CollectionUtils.splitMap(m, chunkSize)
        result.size() == expectedMapCount
        if (!result.isEmpty()) {
            result.each { chunk ->
                assert chunk.size() <= chunkSize
            }
            def mergedEntries = result.collectMany { it.entrySet() }
            assert mergedEntries.size() == (m?.size() ?: 0)
            
            // Check that all original entries are present in the chunks
            if (m != null) {
                m.each { k, v ->
                    boolean found = false
                    for (def chunk : result) {
                        if (chunk.containsKey(k) && Objects.equals(chunk.get(k), v)) {
                            found = true
                            break
                        }
                    }
                    assert found
                }
            }
        }

        where:
        m                                         | chunkSize | expectedMapCount
        null                                      | 2         | 0
        [:]                                       | 2         | 0
        [a: 1, b: 2, c: 3, d: 4, e: 5]            | 2         | 3
        [a: 1, b: 2, c: 3, d: 4, e: 5]            | 3         | 2
        [a: 1, b: 2, c: 3, d: 4, e: 5]            | 5         | 1
        [a: 1, b: 2, c: 3, d: 4, e: 5]            | 10        | 1
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试concatAndNotContainEmpty方法处理不同类型空值的行为
     */
    def "concatAndNotContainEmpty with different empty values test"() {
        given:
        def list1 = [1, "", 3]
        def list2 = [4, null, 6]
        def list3 = [7, " ", new Object()]

        expect:
        // 检查空字符串被过滤
        def result1 = CollectionUtils.concatAndNotContainEmpty(list1)
        result1 == [1, 3]

        // 检查null值被过滤
        def result2 = CollectionUtils.concatAndNotContainEmpty(list2)
        result2 == [4, 6]

        // 检查空白字符串不被过滤，只过滤null和空字符串
        def result3 = CollectionUtils.concatAndNotContainEmpty(list3)
        result3.size() == 3
        result3[1] == " "
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试sortByGivenOrder方法处理复杂排序场景
     */
    def "sortByGivenOrder with complex scenarios test"() {
        given:
        // 复杂对象列表
        def employees = [
                [id: 5, name: "Zhang", age: 30],
                [id: 2, name: "Li", age: 25],
                [id: 8, name: "Wang", age: 35],
                [id: 3, name: "Chen", age: 28]
        ]

        // 复杂排序顺序
        def customOrder = [8, 3, 2, 5, 1]

        when:
        // 根据ID排序
        def result = CollectionUtils.sortByGivenOrder(employees, customOrder, { it.id })

        then:
        // 验证排序结果符合customOrder的顺序
        result.collect { it.id } == [8, 3, 2, 5]

        when:
        // 测试orderedList有不存在于chaosList中的ID
        def customOrder2 = [1, 9, 3, 5]
        def result2 = CollectionUtils.sortByGivenOrder(employees, customOrder2, { it.id })

        then:
        // 验证不存在的ID被忽略，存在的按顺序排列，未在orderedList中的排在最后
        result2.collect { it.id } == [3, 5, 2, 8]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isEqual方法处理特殊集合类型的比较
     */
    def "isEqual with special collections test"() {
        given:
        def list = [1, 2, 3]
        def linkedList = new LinkedList(list)
        def set = [1, 2, 3] as Set
        def immutableList = Collections.unmodifiableList(new ArrayList(list))

        expect:
        // 不同类型但内容相同的集合应该相等
        CollectionUtils.isEqual(list, linkedList) == true
        CollectionUtils.isEqual(list, set) == true
        CollectionUtils.isEqual(set, linkedList) == true
        CollectionUtils.isEqual(list, immutableList) == true

        // 包含相同元素但顺序不同的Set应该相等
        def set1 = [1, 2, 3] as Set
        def set2 = [3, 1, 2] as Set
        CollectionUtils.isEqual(set1, set2) == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试concatAndDistinct方法处理带有重复值的列表
     */
    def "concatAndDistinct with duplicates test"() {
        given:
        def list1 = [1, 2, 3, 1]
        def list2 = [3, 4, 4, 5]
        def list3 = [5, 6, 1, 6]

        expect:
        // 合并多个带有重复值的列表，结果应该去重
        def result = CollectionUtils.concatAndDistinct(list1, list2, list3)
        result == [1, 2, 3, 4, 5, 6]

        // 合并相同的列表多次，结果应该与列表去重后相同
        def result2 = CollectionUtils.concatAndDistinct(list1, list1, list1)
        result2 == [1, 2, 3]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试addIfAbsent方法处理自定义相等条件
     */
    def "addIfAbsent with custom equals predicate test"() {
        given:
        // 创建两个包含复杂对象的列表
        def persons1 = [
                [id: 1, name: "张三", age: 30],
                [id: 2, name: "李四", age: 25]
        ]
        def persons2 = [
                [id: 2, name: "李四(修改)", age: 26],
                [id: 3, name: "王五", age: 28]
        ]

        when:
        // 基于id比较相等，忽略其他字段
        def equalsById = { a, b -> a.id == b.id }
        def resultById = CollectionUtils.addIfAbsent(persons1, persons2, equalsById)

        then:
        // id=2的记录已存在，只添加id=3的记录
        resultById.size() == 3
        resultById.collect { it.id } == [1, 2, 3]
        // 保留原始列表中已有的记录，不覆盖
        resultById[1].name == "李四"
        resultById[1].age == 25

        when:
        // 基于name比较相等，忽略其他字段
        def equalsByName = { a, b -> a.name == b.name }
        def resultByName = CollectionUtils.addIfAbsent(persons1, persons2, equalsByName)

        then:
        // "李四"和"李四(修改)"被视为不同的名字，都会被添加
        resultByName.size() == 4
        resultByName.collect { it.name } == ["张三", "李四", "李四(修改)", "王五"]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试集合操作方法处理大数据量的性能表现
     */
    def "performance test for large collections"() {
        given:
        // 创建大数据量的列表和集合
        def largeList = (1..10000).collect()
        def largeSet = (1..10000).toSet()
        def largeMap = (1..10000).collectEntries { [(it): it * 10] }

        expect:
        // 测试各种方法对大数据量的处理是否正常

        // nullToEmpty方法在大数据量下应正常返回原集合
        CollectionUtils.nullToEmpty(largeList).size() == 10000
        CollectionUtils.nullToEmpty(largeSet).size() == 10000
        CollectionUtils.nullToEmpty(largeMap).size() == 10000

        // size方法应正确返回大小
        CollectionUtils.size(largeList) == 10000
        CollectionUtils.size(largeMap) == 10000

        // 测试splitMap方法处理大Map
        def chunks = CollectionUtils.splitMap(largeMap, 1000)
        chunks.size() == 10
        chunks.sum { it.size() } == 10000

        // 测试更多边界情况
        CollectionUtils.splitMap(null, 1000) != null
        CollectionUtils.splitMap([:], 1000) != null
        
        when:
        def smallMap = [a: 1, b: 2, c: 3]
        def smallChunks = CollectionUtils.splitMap(smallMap, 2)
        
        then:
        smallChunks.size() == 2
        smallChunks.sum { it.size() } == 3
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试集合操作方法的健壮性和异常处理
     */
    def "robustness test for handling exceptions"() {
        given:
        // 创建可能导致异常的测试数据
        def normalList = [1, 2, 3]
        def problematicFunction = { it ->
            if (it == 2) throw new RuntimeException("测试异常")
            return it
        }

        when:
        // 测试sortByGivenOrder在函数抛出异常时的处理
        CollectionUtils.sortByGivenOrder(normalList, [3, 2, 1], problematicFunction)

        then:
        // 期望方法抛出异常
        thrown(RuntimeException)

        when:
        // 创建一个无法序列化的对象
        def nonSerializableObject = new Object() {
            @Override
            String toString() {
                return "非序列化对象"
            }
        }
        def listWithNonSerializable = [1, nonSerializableObject, 3]

        // 尝试对包含无法序列化对象的列表进行操作
        def result = CollectionUtils.concatAndDistinct(listWithNonSerializable)

        then:
        // 应该正常处理而不抛出异常
        result.size() == 3
        result[1].toString() == "非序列化对象"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试firstNotEmpty方法处理不同类型参数
     */
    def "firstNotEmpty with different parameter types test"() {
        given:
        def arrayList = new ArrayList<Integer>([1, 2, 3])
        def linkedList = new LinkedList<Integer>([4, 5, 6])
        def hashSet = new HashSet<Integer>([7, 8, 9])
        def treeSet = new TreeSet<Integer>([10, 11, 12])

        expect:
        // 测试不同类型的集合混合使用
        CollectionUtils.firstNotEmpty(arrayList, linkedList) == arrayList
        CollectionUtils.firstNotEmpty([], arrayList, linkedList) == arrayList
        CollectionUtils.firstNotEmpty([], hashSet, treeSet) == hashSet

        // 测试可变参数数量变化
        CollectionUtils.firstNotEmpty(arrayList) == arrayList
        CollectionUtils.firstNotEmpty([], [], [], arrayList, linkedList, hashSet) == arrayList

        // 测试空参数
        CollectionUtils.firstNotEmpty() == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复杂对象的集合操作
     */
    def "operations with complex objects test"() {
        given:
        // 定义一个复杂的数据结构
        def complexObjects = [
                [
                        id     : 1,
                        name   : "项目A",
                        members: [
                                [id: 101, name: "成员1"],
                                [id: 102, name: "成员2"]
                        ],
                        tags   : ["开发", "重要"] as Set
                ],
                [
                        id     : 2,
                        name   : "项目B",
                        members: [
                                [id: 201, name: "成员3"],
                                [id: 202, name: "成员4"]
                        ],
                        tags   : ["测试", "普通"] as Set
                ]
        ]

        when:
        // 使用嵌套属性作为比较条件
        def hasSameMember = { a, b ->
            if (!a.members || !b.members) return false
            return a.members.any { am ->
                b.members.any { bm -> am.id == bm.id }
            }
        }

        def compareResult = CollectionUtils.addIfAbsent(
                complexObjects,
                [
                        [
                                id     : 3,
                                name   : "项目C",
                                members: [
                                        [id: 101, name: "成员1"],  // 与项目A共享成员
                                        [id: 301, name: "成员5"]
                                ],
                                tags   : ["维护"] as Set
                        ]
                ],
                hasSameMember
        )

        then:
        // 由于项目C与项目A有共同成员，所以它不应该被添加
        compareResult.size() == 2
        compareResult.collect { it.id } == [1, 2]

        when:
        // 使用标签集合作为比较条件
        def hasCommonTag = { a, b ->
            if (!a.tags || !b.tags) return false
            return !Collections.disjoint(a.tags, b.tags)
        }

        def compareResult2 = CollectionUtils.addIfAbsent(
                complexObjects,
                [
                        [
                                id     : 4,
                                name   : "项目D",
                                members: [
                                        [id: 401, name: "成员6"]
                                ],
                                tags   : ["开发", "新增"] as Set  // 与项目A共享标签"开发"
                        ]
                ],
                hasCommonTag
        )

        then:
        // 由于项目D与项目A有共同标签，所以它不应该被添加
        compareResult2.size() == 2
        compareResult2.collect { it.id } == [1, 2]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试列表交集操作
     */
    def "intersection test"() {
        given:
        def list1 = [1, 2, 3, 4, 5]
        def list2 = [3, 4, 5, 6, 7]
        def list3 = [5, 6, 7, 8, 9]
        def emptyList = []

        expect:
        // 两个列表的交集
        def result1 = list1.intersect(list2)
        result1 == [3, 4, 5]

        // 三个列表的交集
        def result2 = list1.intersect(list2).intersect(list3)
        result2 == [5]

        // 空列表的交集
        list1.intersect(emptyList) == []

        // 与自身的交集就是自身
        list1.intersect(list1) == list1
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试列表并集操作
     */
    def "union test"() {
        given:
        def list1 = [1, 2, 3]
        def list2 = [3, 4, 5]
        def list3 = [5, 6, 7]
        def emptyList = []

        expect:
        // 两个列表的并集
        def result1 = list1 + list2
        result1.toSet() == [1, 2, 3, 4, 5] as Set

        // 三个列表的并集
        def result2 = list1 + list2 + list3
        result2.toSet() == [1, 2, 3, 4, 5, 6, 7] as Set

        // 空列表的并集
        (list1 + emptyList).toSet() == list1.toSet()

        // 与自身的并集包含重复元素
        (list1 + list1).size() == list1.size() * 2
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试列表差集操作
     */
    def "difference test"() {
        given:
        def list1 = [1, 2, 3, 4, 5]
        def list2 = [3, 4, 5, 6, 7]
        def emptyList = []

        expect:
        // list1减去list2中的元素
        list1 - list2 == [1, 2]

        // list2减去list1中的元素
        list2 - list1 == [6, 7]

        // 减去空列表，结果不变
        list1 - emptyList == list1

        // 空列表减去任何列表，结果为空
        emptyList - list1 == []

        // 列表减去自身，结果为空
        list1 - list1 == []
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试列表分组操作
     */
    def "grouping test"() {
        given:
        def persons = [
                [id: 1, age: 20, city: "北京"],
                [id: 2, age: 25, city: "上海"],
                [id: 3, age: 20, city: "北京"],
                [id: 4, age: 30, city: "广州"],
                [id: 5, age: 25, city: "上海"]
        ]

        when:
        // 按城市分组
        def groupByCity = persons.groupBy { it.city }

        then:
        groupByCity.size() == 3
        groupByCity["北京"].size() == 2
        groupByCity["上海"].size() == 2
        groupByCity["广州"].size() == 1
        groupByCity["北京"].collect { it.id } == [1, 3]

        when:
        // 按年龄分组
        def groupByAge = persons.groupBy { it.age }

        then:
        groupByAge.size() == 3
        groupByAge[20].size() == 2
        groupByAge[25].size() == 2
        groupByAge[30].size() == 1
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试列表过滤操作
     */
    def "filtering test"() {
        given:
        def numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

        expect:
        // 过滤偶数
        numbers.findAll { it % 2 == 0 } == [2, 4, 6, 8, 10]

        // 过滤奇数
        numbers.findAll { it % 2 != 0 } == [1, 3, 5, 7, 9]

        // 过滤大于5的数
        numbers.findAll { it > 5 } == [6, 7, 8, 9, 10]

        // 过滤范围内的数
        numbers.findAll { it >= 3 && it <= 7 } == [3, 4, 5, 6, 7]

        // 全部过滤掉的情况
        numbers.findAll { it > 100 } == []

        // 没有被过滤的情况
        numbers.findAll { it > 0 } == numbers
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Map的转换操作
     */
    def "map transformation test"() {
        given:
        def map = [a: 1, b: 2, c: 3, d: 4]

        expect:
        // Map的key转换
        map.collectEntries { k, v -> [(k.toUpperCase()): v] } == [A: 1, B: 2, C: 3, D: 4]

        // Map的value转换
        map.collectEntries { k, v -> [(k): v * 2] } == [a: 2, b: 4, c: 6, d: 8]

        // Map的key和value同时转换
        map.collectEntries { k, v -> [(k + "_new"): "value_${v}"] } == [a_new: "value_1", b_new: "value_2", c_new: "value_3", d_new: "value_4"]

        // 将Map转为List
        map.collect { k, v -> "${k}=${v}" } as Set == ["a=1", "b=2", "c=3", "d=4"] as Set
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试集合的Map/Reduce操作
     */
    def "map reduce operations test"() {
        given:
        def numbers = [1, 2, 3, 4, 5]

        expect:
        // Map操作 - 每个元素乘以2
        numbers.collect { it * 2 } == [2, 4, 6, 8, 10]

        // Reduce操作 - 求和
        numbers.sum() == 15

        // Reduce操作 - 自定义reduce
        numbers.inject(0) { sum, item -> sum + item } == 15

        // Reduce操作 - 求积
        numbers.inject(1) { product, item -> product * item } == 120

        // Map+Reduce - 求平方和
        numbers.collect { it * it }.sum() == 55

        // 空集合的reduce操作 - 在Groovy中空列表的sum结果是null
        [].sum() == null
        [].inject(0) { sum, item -> sum + item } == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试flatMap操作
     */
    def "flatMap test"() {
        given:
        def nestedLists = [[1, 2], [3, 4], [5, 6]]
        def emptyNestedList = [[]]
        def mixedNestedList = [[1, 2], [], [3, 4]]

        expect:
        // 嵌套列表展平
        nestedLists.flatten() == [1, 2, 3, 4, 5, 6]

        // 空嵌套列表展平
        emptyNestedList.flatten() == []

        // 混合嵌套列表展平
        mixedNestedList.flatten() == [1, 2, 3, 4]

        // 使用flatMap变换后展平
        nestedLists.collectMany { list -> list.collect { it * 2 } } == [2, 4, 6, 8, 10, 12]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复杂对象的排序操作
     */
    def "complex sorting test"() {
        given:
        def persons = [
                [id: 3, name: "张三", age: 25, city: "北京"],
                [id: 1, name: "李四", age: 30, city: "上海"],
                [id: 5, name: "王五", age: 22, city: "广州"],
                [id: 2, name: "赵六", age: 28, city: "深圳"],
                [id: 4, name: "钱七", age: 25, city: "杭州"]
        ]

        when:
        // 按ID排序
        def sortById = persons.sort { it.id }

        then:
        sortById.collect { it.id } == [1, 2, 3, 4, 5]

        when:
        // 按年龄排序（升序）
        def sortByAge = persons.sort { it.age }

        then:
        sortByAge.collect { it.age } == [22, 25, 25, 28, 30]
        // 相同年龄的保持原相对顺序
        sortByAge.findAll { it.age == 25 }.collect { it.id } == [3, 4]

        when:
        // 按年龄降序，然后按城市字母升序
        def sortByAgeAndCity = persons.sort { a, b ->
            def ageComp = b.age <=> a.age
            ageComp == 0 ? a.city <=> b.city : ageComp
        }

        then:
        // 先按年龄降序
        sortByAgeAndCity[0].age == 30
        sortByAgeAndCity[-1].age == 22
        // 相同年龄再按城市升序
        sortByAgeAndCity.findAll { it.age == 25 }.collect { it.city } == ["北京", "杭州"]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试集合的交互操作（并集、交集、补集）
     */
    def "set operations test"() {
        given:
        def set1 = [1, 2, 3, 4] as Set
        def set2 = [3, 4, 5, 6] as Set
        def set3 = [5, 6, 7, 8] as Set
        def emptySet = [] as Set

        expect:
        // 并集
        set1 + set2 == [1, 2, 3, 4, 5, 6] as Set

        // 交集
        set1.intersect(set2) == [3, 4] as Set

        // 差集
        set1 - set2 == [1, 2] as Set
        set2 - set1 == [5, 6] as Set

        // 对称差（并集减去交集）
        (set1 + set2) - set1.intersect(set2) == [1, 2, 5, 6] as Set

        // 与空集的操作
        set1 + emptySet == set1
        set1.intersect(emptySet) == emptySet
        set1 - emptySet == set1
        emptySet - set1 == emptySet

        // 多集合操作
        def commonElements = set1.intersect(set2).intersect(set3)
        commonElements == [] as Set

        def allElements = set1 + set2 + set3
        allElements == [1, 2, 3, 4, 5, 6, 7, 8] as Set
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试集合的高级过滤操作
     */
    def "advanced filtering test"() {
        given:
        def persons = [
                [id: 1, name: "张三", gender: "男", department: "技术", salary: 10000],
                [id: 2, name: "李四", gender: "女", department: "市场", salary: 8000],
                [id: 3, name: "王五", gender: "男", department: "技术", salary: 12000],
                [id: 4, name: "赵六", gender: "女", department: "财务", salary: 9000],
                [id: 5, name: "钱七", gender: "男", department: "市场", salary: 7500]
        ]

        expect:
        // 复合条件过滤：技术部门的男性
        def techMales = persons.findAll { it.department == "技术" && it.gender == "男" }
        techMales.size() == 2
        techMales.collect { it.id } == [1, 3]

        // 取最大值
        def highestPaid = persons.max { it.salary }
        highestPaid.id == 3
        highestPaid.salary == 12000

        // 取最小值
        def lowestPaid = persons.min { it.salary }
        lowestPaid.id == 5
        lowestPaid.salary == 7500

        // 按条件分组统计
        def genderCount = persons.groupBy { it.gender }.collectEntries { gender, personList ->
            [(gender): personList.size()]
        }
        genderCount == ["男": 3, "女": 2]

        // 部门平均薪资
        def avgSalaryByDept = persons.groupBy { it.department }.collectEntries { dept, personList ->
            [(dept): personList.sum { it.salary } / personList.size()]
        }
        avgSalaryByDept["技术"] == 11000
        avgSalaryByDept["市场"] == 7750
        avgSalaryByDept["财务"] == 9000
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空安全的集合操作
     */
    def "null safe collection operations test"() {
        given:
        List<Integer> nullList = null
        Set<Integer> nullSet = null
        Map<String, Integer> nullMap = null

        def emptyList = []
        def emptySet = [] as Set
        def emptyMap = [:]

        // 使用Java反射直接调用指定类型参数的方法，避免方法重载歧义
        def utilsClass = com.facishare.paas.appframework.common.util.CollectionUtils.class
        def emptyCollectionMethod = utilsClass.getMethod("empty", Collection.class)
        def nullToEmptyListMethod = utilsClass.getMethod("nullToEmpty", List.class)

        expect:
        // 安全地获取空集合的第一个元素
        emptyList.find { true } == null

        // 安全地对可能为null的集合进行操作
        nullList?.collect { it * 2 } == null
        emptyList.collect { it * 2 } == []

        // 安全地检查null集合的包含关系
        nullList?.contains(1) == null
        emptyList.contains(1) == false

        // 安全地检查null集合的大小
        nullList?.size() == null
        emptyList.size() == 0

        // 安全地处理null映射
        nullMap?.get("key") == null
        emptyMap.get("key") == null

        // 使用Elvis操作符处理null集合
        (nullList ?: []).size() == 0
        (nullMap ?: [:]).size() == 0

        // 使用CollectionUtils安全地处理null集合
        // 使用反射API明确调用Collection参数的empty方法，避免重载歧义
        // 反射调用静态方法时，第一个参数为null，第二个参数为方法参数数组
        emptyCollectionMethod.invoke(null, [nullList] as Object[]) == true

        // 使用反射API明确调用List参数的nullToEmpty方法，避免重载歧义
        def emptyListResult = nullToEmptyListMethod.invoke(null, [nullList] as Object[])
        emptyListResult.size() == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试链式集合操作
     */
    def "chain operations test"() {
        given:
        def persons = [
                [id: 1, name: "张三", age: 22, city: "北京", salary: 8000],
                [id: 2, name: "李四", age: 25, city: "上海", salary: 10000],
                [id: 3, name: "王五", age: 28, city: "广州", salary: 9000],
                [id: 4, name: "赵六", age: 30, city: "深圳", salary: 12000],
                [id: 5, name: "钱七", age: 22, city: "杭州", salary: 7500]
        ]

        when:
        // 链式操作：过滤年龄大于25的，按薪资降序排序，取前2名，返回他们的姓名
        def result = persons
                .findAll { it.age > 25 }
                .sort { a, b -> b.salary <=> a.salary }
                .take(2)
                .collect { it.name }

        then:
        result == ["赵六", "王五"]

        when:
        // 链式操作：按城市分组，计算每组的平均年龄和平均薪资，再过滤平均年龄大于25的城市
        def cityStats = persons
                .groupBy { it.city }
                .collectEntries { city, cityPersons ->
                    [
                            (city): [
                                    avgAge   : cityPersons.sum { it.age } / cityPersons.size(),
                                    avgSalary: cityPersons.sum { it.salary } / cityPersons.size()
                            ]
                    ]
                }
                .findAll { city, stats -> stats.avgAge > 25 }

        then:
        // 根据实际过滤后的结果大小和内容进行断言
        cityStats.size() == 2
        cityStats.keySet().containsAll(["广州", "深圳"])
        cityStats["深圳"].avgAge == 30
        cityStats["深圳"].avgSalary == 12000
    }

    def "empty/notEmpty"() {
        expect:
        if (c == null) {
            // 当输入为 null 时，显式指定调用方法参数类型，避免歧义
            assert com.facishare.paas.appframework.common.util.CollectionUtils.empty((Collection) c) == isEmpty
            assert com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty((Collection) c) == !isEmpty
        } else {
            assert CollectionUtils.empty(c) == isEmpty
            assert CollectionUtils.notEmpty(c) == !isEmpty
        }

        where:
        c           | isEmpty
        null        | true
        []          | true
        ['a']       | false
        [null]      | false
    }

    def "empty/notEmpty map"() {
        expect:
        if (m == null) {
            // 当输入为 null 时，显式指定调用方法参数类型，避免歧义
            assert com.facishare.paas.appframework.common.util.CollectionUtils.empty((Map) m) == isEmpty
            assert com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty((Map) m) == !isEmpty
        } else {
            assert CollectionUtils.empty(m) == isEmpty
            assert CollectionUtils.notEmpty(m) == !isEmpty
        }

        where:
        m           | isEmpty
        null        | true
        [:]         | true
        [a: 'b']    | false
        [(null): 1] | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当集合为空或者null的情况下，是否可以得到正确的大小
     */
    def "size test with null and empty collection"() {
        given:
        def emptyList = []
        // 删除未使用的变量
        // def nullList = null

        expect:
        com.facishare.paas.appframework.common.util.CollectionUtils.size(emptyList) == 0
        com.facishare.paas.appframework.common.util.CollectionUtils.size((Collection) null) == 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当Map为空或者null的情况下，是否可以得到正确的大小
     */
    def "size test with null and empty map"() {
        given:
        def emptyMap = [:]
        // 删除未使用的变量
        // def nullList = null

        expect:
        com.facishare.paas.appframework.common.util.CollectionUtils.size(emptyMap) == 0
        com.facishare.paas.appframework.common.util.CollectionUtils.size((Map) null) == 0
    }

    /**
     * 测试内容描述：测试distinctList方法对Set的处理。
     * 注意：由于CollectionUtils中似乎没有distinctList方法，此处使用Java标准库进行测试
     */
    def "set distinct test"() {
        given:
        def notEmptySet = [1, 9, 5, 7, 7] as Set

        expect:
        // 测试Set本身去重能力
        notEmptySet.size() == 4
        
        // 测试Set转为List
        new ArrayList(notEmptySet).sort() == [1, 5, 7, 9]
        
        // 测试Collection的去重能力
        [1, 2, 2, 3, 3, 3].toSet().size() == 3
    }

} 