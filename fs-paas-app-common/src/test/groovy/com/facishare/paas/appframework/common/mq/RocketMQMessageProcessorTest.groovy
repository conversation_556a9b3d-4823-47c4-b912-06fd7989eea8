package com.facishare.paas.appframework.common.mq


import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.github.autoconf.ConfigFactory
import com.github.autoconf.api.IConfig
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly
import org.apache.rocketmq.common.consumer.ConsumeFromWhere
import org.apache.rocketmq.common.message.MessageExt
import org.powermock.reflect.Whitebox
import spock.lang.Specification

import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：RocketMQMessageProcessor类的单元测试
 */
class RocketMQMessageProcessorTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        // 每个测试前的准备工作
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试初始化方法
     */
    def "测试init方法"() {
        given: "模拟ConfigFactory和其他依赖"
        def configFactory = Mock(ConfigFactory)
        def processor = new RocketMQMessageProcessor()
        def configName = "test-config"
        def config = Mock(IConfig)
        def validConfigContent = '{"nameServer":"localhost:9876","consumeGroup":"testGroup","topic":"testTopic","tags":["tag1","tag2"],"consumeBatchSize":10,"consumeFromWhere":"CONSUME_FROM_LAST_OFFSET","ordered":false}'
        
        // 模拟静态方法调用
        GroovyMock(ConfigFactory, global: true)
        ConfigFactory.getInstance() >> configFactory
        
        // 设置processor属性
        processor.configName = configName
        processor.rocketMQMessageListener = Mock(RocketMQMessageListener)
        
        when: "调用init方法"
        // 模拟getConfig行为，直接调用传入的回调函数
        configFactory.getConfig(configName, _) >> { String name, Closure callback ->
            callback.call(config)
        }
        
        // 模拟config.getContent()返回有效的配置内容
        config.getContent() >> validConfigContent.getBytes()
        
        // 模拟DefaultMQPushConsumer
        def mockConsumer = Mock(DefaultMQPushConsumer)

        processor.init()
        
        then: "应该创建并启动Consumer"
        // 验证DefaultMQPushConsumer被正确配置
        notThrown(Exception)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试并发消息监听器
     */
    def "测试并发消息监听器"() {
        given: "准备RocketMQMessageProcessor和必要的模拟对象"
        def processor = new RocketMQMessageProcessor()
        def consumer = Mock(DefaultMQPushConsumer)
        def listener = Mock(RocketMQMessageListener)
        
        // 设置processor的属性
        processor.rocketMQMessageListener = listener
        Whitebox.setInternalState(processor, "defaultMQPushConsumer", consumer)
        Whitebox.setInternalState(processor, "shutdown", false)
        
        when: "调用注册并发消息监听器的方法"
        Whitebox.invokeMethod(processor, "registerConcurrentMessageListener")
        
        then: "验证消费者注册了消息监听器"
        1 * consumer.registerMessageListener(_ as MessageListenerConcurrently)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试顺序消息监听器
     */
    def "测试顺序消息监听器"() {
        given: "准备RocketMQMessageProcessor和必要的模拟对象"
        def processor = new RocketMQMessageProcessor()
        def consumer = Mock(DefaultMQPushConsumer)
        def listener = Mock(RocketMQMessageListener)
        
        // 设置processor的属性
        processor.rocketMQMessageListener = listener
        Whitebox.setInternalState(processor, "defaultMQPushConsumer", consumer)
        Whitebox.setInternalState(processor, "shutdown", false)
        
        when: "调用注册顺序消息监听器的方法"
        Whitebox.invokeMethod(processor, "registerOrderedMessageListener")
        
        then: "验证消费者注册了消息监听器"
        1 * consumer.registerMessageListener(_ as MessageListenerOrderly)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试消息消费成功场景
     */
    def "测试并发消息消费成功"() {
        given: "准备RocketMQMessageProcessor和注册并发消息监听器"
        def processor = new RocketMQMessageProcessor()
        def consumer = Mock(DefaultMQPushConsumer)
        def listener = Mock(RocketMQMessageListener)
        def messageListenerConcurrently
        
        // 设置processor的属性
        processor.rocketMQMessageListener = listener
        Whitebox.setInternalState(processor, "defaultMQPushConsumer", consumer)
        Whitebox.setInternalState(processor, "shutdown", false)
        
        // 捕获注册的监听器
        consumer.registerMessageListener(_ as MessageListenerConcurrently) >> { args ->
            messageListenerConcurrently = args[0]
        }
        
        // 注册监听器
        Whitebox.invokeMethod(processor, "registerConcurrentMessageListener")
        
        when: "模拟消息到达"
        def messages = [Mock(MessageExt)]
        def context = null
        def result = messageListenerConcurrently.consumeMessage(messages, context)
        
        then: "应调用listener的consumeMessage方法"
        1 * listener.consumeMessage(messages)
        
        and: "应返回消费成功状态"
        result == ConsumeConcurrentlyStatus.CONSUME_SUCCESS
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试顺序消息消费成功场景
     */
    def "测试顺序消息消费成功"() {
        given: "准备RocketMQMessageProcessor和注册顺序消息监听器"
        def processor = new RocketMQMessageProcessor()
        def consumer = Mock(DefaultMQPushConsumer)
        def listener = Mock(RocketMQMessageListener)
        def messageListenerOrderly
        
        // 设置processor的属性
        processor.rocketMQMessageListener = listener
        Whitebox.setInternalState(processor, "defaultMQPushConsumer", consumer)
        Whitebox.setInternalState(processor, "shutdown", false)
        
        // 捕获注册的监听器
        consumer.registerMessageListener(_ as MessageListenerOrderly) >> { args ->
            messageListenerOrderly = args[0]
        }
        
        // 注册监听器
        Whitebox.invokeMethod(processor, "registerOrderedMessageListener")
        
        when: "模拟消息到达"
        def messages = [Mock(MessageExt)]
        def context = null
        def result = messageListenerOrderly.consumeMessage(messages, context)
        
        then: "应调用listener的consumeMessage方法"
        1 * listener.consumeMessage(messages)
        
        and: "应返回消费成功状态"
        result == ConsumeOrderlyStatus.SUCCESS
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试消息消费异常场景
     */
    def "测试消息消费异常"() {
        given: "准备RocketMQMessageProcessor和注册并发消息监听器"
        def processor = new RocketMQMessageProcessor()
        def consumer = Mock(DefaultMQPushConsumer)
        def listener = Mock(RocketMQMessageListener)
        def messageListenerConcurrently
        
        // 设置processor的属性
        processor.rocketMQMessageListener = listener
        Whitebox.setInternalState(processor, "defaultMQPushConsumer", consumer)
        Whitebox.setInternalState(processor, "shutdown", false)
        
        // 捕获注册的监听器
        consumer.registerMessageListener(_ as MessageListenerConcurrently) >> { args ->
            messageListenerConcurrently = args[0]
        }
        
        // 注册监听器
        Whitebox.invokeMethod(processor, "registerConcurrentMessageListener")
        
        when: "模拟消息消费时抛出异常"
        def messages = [Mock(MessageExt)]
        def context = null
        listener.consumeMessage(messages) >> { throw new RuntimeException("Test exception") }
        def result = messageListenerConcurrently.consumeMessage(messages, context)
        
        then: "应返回稍后重试状态"
        result == ConsumeConcurrentlyStatus.RECONSUME_LATER
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Config类
     */
    def "测试Config类属性和方法"() {
        given: "创建Config对象并设置属性"
        def config = new RocketMQMessageProcessor.Config()
        config.nameServer = "localhost:9876"
        config.consumeGroup = "testGroup"
        config.topic = "testTopic"
        config.tags = ["tag1", "tag2"]
        config.consumeBatchSize = 10
        config.consumeFromWhere = "CONSUME_FROM_LAST_OFFSET"
        config.ordered = true
        
        expect: "属性应被正确设置"
        config.nameServer == "localhost:9876"
        config.consumeGroup == "testGroup"
        config.topic == "testTopic"
        config.tags == ["tag1", "tag2"]
        config.consumeBatchSize == 10
        config.ordered
        
        and: "getTags方法应返回正确格式的Tag字符串"
        config.getTags() == "tag1||tag2"
        
        and: "getConsumeFromWhere方法应返回正确的枚举值"
        config.getConsumeFromWhere() == ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Config类的边界情况
     */
    def "测试Config类的边界情况"() {
        given: "创建Config对象但不设置某些属性"
        def config = new RocketMQMessageProcessor.Config()
        
        expect: "getConsumeBatchSize应返回默认值1"
        config.consumeBatchSize == 0
        config.getConsumeBatchSize() == 1
        
        and: "getTags方法在没有tags时应返回*"
        config.tags == null
        config.getTags() == "*"
        
        and: "getConsumeFromWhere方法在无效值时应返回默认值"
        config.consumeFromWhere == "INVALID_VALUE"
        config.getConsumeFromWhere() == ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试shutdown方法
     */
    def "测试shutdown方法"() {
        given: "准备RocketMQMessageProcessor和DefaultMQPushConsumer"
        def processor = new RocketMQMessageProcessor()
        def consumer = Mock(DefaultMQPushConsumer)
        
        // 设置processor的属性
        Whitebox.setInternalState(processor, "defaultMQPushConsumer", consumer)
        
        when: "调用shutdown方法"
        Whitebox.invokeMethod(processor, "shutdown")
        
        then: "应调用consumer的shutdown方法"
        1 * consumer.shutdown()
        
        and: "应设置shutdown标志和清空consumer引用"
        Whitebox.getInternalState(processor, "shutdown") == true
        Whitebox.getInternalState(processor, "defaultMQPushConsumer") == null
    }
} 