package com.facishare.paas.appframework.common.util

import com.facishare.paas.appframework.common.service.dto.SendMsgBySession
import com.facishare.paas.appframework.common.service.proxy.QinXinFlowMessageProxy
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.github.autoconf.api.IChangeListener
import com.github.autoconf.api.IConfig
import com.github.autoconf.helper.ConfigHelper
import org.powermock.reflect.Whitebox
import org.redisson.api.RLock
import org.redisson.api.RedissonClient
import org.springframework.context.ApplicationContext
import spock.lang.Specification
import java.lang.reflect.Field

import java.util.concurrent.TimeUnit

/**
 * GenerateByAI
 * 测试内容描述：ChangeListenerHolder工具类的单元测试
 */
class ChangeListenerHolderTest extends Specification {

    def changeListenerHolder = new ChangeListenerHolder()
    def mockApplicationContext = Mock(ApplicationContext)
    def mockQinXinFlowMessageProxy = Mock(QinXinFlowMessageProxy)
    def mockRedissonClient = Mock(RedissonClient)
    def mockRLock = Mock(RLock)

    def setup() {
        // Mock静态依赖
        RestUtils.metaClass.static.buildFlowHeaders = { User user -> ["X-FS-TENANT-ID": "1"] }
        AppFrameworkConfig.metaClass.static.getLoadConfigFailNotifySessionId = { -> null }
        
        // 设置静态字段
        Whitebox.setInternalState(ChangeListenerHolder, "qinXinFlowMessageProxy", mockQinXinFlowMessageProxy)
        Whitebox.setInternalState(ChangeListenerHolder, "redissonClient", mockRedissonClient)
    }

    def "test setApplicationContext success"() {
        given: "准备ApplicationContext"
        mockApplicationContext.getBean("qinXinFlowMessageProxy", QinXinFlowMessageProxy.class) >> mockQinXinFlowMessageProxy
        mockApplicationContext.getBean("redissonClient", RedissonClient.class) >> mockRedissonClient

        when: "调用setApplicationContext方法"
        changeListenerHolder.setApplicationContext(mockApplicationContext)

        then: "应该成功设置bean"
        noExceptionThrown()
    }

    def "test setApplicationContext with exception"() {
        given: "准备会抛出异常的ApplicationContext"
        mockApplicationContext.getBean("qinXinFlowMessageProxy", QinXinFlowMessageProxy.class) >> { throw new RuntimeException("Bean not found") }

        when: "调用setApplicationContext方法"
        changeListenerHolder.setApplicationContext(mockApplicationContext)

        then: "应该抛出IllegalStateException"
        thrown(IllegalStateException)
    }

    def "test create ChangeListener"() {
        given: "准备ChangeHandler"
        def mockConfig = Mock(IConfig)
        mockConfig.getName() >> "test-config"
        mockConfig.getString() >> "test-content"
        
        def handlerCalled = false
        def handler = { IConfig config ->
            handlerCalled = true
            assert config.getName() == "test-config"
        }

        when: "创建ChangeListener并调用changed方法"
        def listener = ChangeListenerHolder.create(handler)
        listener.changed(mockConfig)

        then: "应该调用handler"
        handlerCalled == true
    }

    def "test ChangeListener onException"() {
        given: "准备ChangeHandler和异常"
        def handler = { IConfig config -> }
        def exception = new RuntimeException("Test exception")
        
        // Mock ParallelUtils
        ParallelUtils.metaClass.static.createBackgroundTask = { ->
            return [
                submit: { Runnable task ->
                    return [run: { -> task.run() }]
                }
            ]
        }

        when: "创建ChangeListener并调用onException方法"
        def listener = ChangeListenerHolder.create(handler)
        listener.onException(exception)

        then: "应该处理异常而不抛出"
        noExceptionThrown()
    }

    def "test needSendMessage with successful lock"() {
        given: "准备锁相关的mock"
        def key = "test-lock-key"
        mockRedissonClient.getLock(key) >> mockRLock
        mockRLock.tryLock(0, 60, TimeUnit.SECONDS) >> true

        when: "调用needSendMessage方法"
        def result = ChangeListenerHolder.needSendMessage(key)

        then: "应该返回true"
        result == true
    }

    def "test needSendMessage with failed lock"() {
        given: "准备锁相关的mock"
        def key = "test-lock-key"
        mockRedissonClient.getLock(key) >> mockRLock
        mockRLock.tryLock(0, 60, TimeUnit.SECONDS) >> false

        when: "调用needSendMessage方法"
        def result = ChangeListenerHolder.needSendMessage(key)

        then: "应该返回false"
        result == false
    }

    def "test needSendMessage with InterruptedException"() {
        given: "准备会抛出InterruptedException的锁"
        def key = "test-lock-key"
        mockRedissonClient.getLock(key) >> mockRLock
        mockRLock.tryLock(0, 60, TimeUnit.SECONDS) >> { throw new InterruptedException("Interrupted") }

        when: "调用needSendMessage方法"
        def result = ChangeListenerHolder.needSendMessage(key)

        then: "应该返回true并设置中断标志"
        result == true
        Thread.currentThread().isInterrupted()
    }

    def "test needSendMessage with null redissonClient"() {
        given: "设置redissonClient为null"
        Whitebox.setInternalState(ChangeListenerHolder, "redissonClient", null)

        when: "调用needSendMessage方法"
        def result = ChangeListenerHolder.needSendMessage("test-key")

        then: "应该返回true"
        result == true
    }

    def "test getLock"() {
        given: "准备锁key"
        def key = "test-lock-key"
        mockRedissonClient.getLock(key) >> mockRLock

        when: "调用getLock方法"
        def result = ChangeListenerHolder.getLock(key)

        then: "应该返回正确的锁"
        result == mockRLock
    }

    def "test ConfigMessage creation and methods"() {
        given: "创建ConfigMessage"
        def configName = "test-config"
        def message = "test message"
        def changeType = ChangeListenerHolder.ConfigChangeType.FAIL

        when: "创建ConfigMessage实例"
        def configMessage = ChangeListenerHolder.ConfigMessage.of(configName, message, changeType)

        then: "验证ConfigMessage属性"
        configMessage.getConfigName() == configName
        configMessage.getMessage() == message
        configMessage.getChangeType() == changeType
        configMessage.getAppName() != null
    }

    def "test ConfigMessage fail message"() {
        given: "创建失败类型的ConfigMessage"
        def configMessage = ChangeListenerHolder.ConfigMessage.of("test-config", "error message", ChangeListenerHolder.ConfigChangeType.FAIL)

        when: "调用message方法"
        def result = configMessage.message()

        then: "应该返回失败消息格式"
        result.contains("Config loading failed!")
        result.contains("test-config")
        result.contains("error message")
    }

    def "test ConfigMessage recover message"() {
        given: "创建恢复类型的ConfigMessage"
        def configMessage = ChangeListenerHolder.ConfigMessage.of("test-config", null, ChangeListenerHolder.ConfigChangeType.RECOVER)

        when: "调用message方法"
        def result = configMessage.message()

        then: "应该返回恢复消息格式"
        result.contains("Config loading recovered!")
        result.contains("test-config")
    }

    def "test ConfigMessage lockKey for fail type"() {
        given: "创建失败类型的ConfigMessage"
        def configMessage = ChangeListenerHolder.ConfigMessage.of("test-config", "error", ChangeListenerHolder.ConfigChangeType.FAIL)

        when: "调用lockKey方法"
        def result = configMessage.lockKey()

        then: "应该返回失败锁key格式"
        result.startsWith("paas_config_lock_fail_")
        result.contains("test-config")
    }

    def "test ConfigMessage lockKey for recover type"() {
        given: "创建恢复类型的ConfigMessage"
        def configMessage = ChangeListenerHolder.ConfigMessage.of("test-config", null, ChangeListenerHolder.ConfigChangeType.RECOVER)

        when: "调用lockKey方法"
        def result = configMessage.lockKey()

        then: "应该返回恢复锁key格式"
        result.startsWith("paas_config_lock_recover_")
        result.contains("test-config")
    }

    def "test sendMsg with null proxy"() {
        given: "设置proxy为null"
        Whitebox.setInternalState(ChangeListenerHolder, "qinXinFlowMessageProxy", null)

        when: "调用sendMsg方法"
        Whitebox.invokeMethod(ChangeListenerHolder, "sendMsg", "test message", "session123")

        then: "应该不抛出异常"
        noExceptionThrown()
    }

    def "test sendMsg success"() {
        given: "准备消息和sessionId"
        def message = "test message"
        def sessionId = "session123"

        when: "调用sendMsg方法"
        Whitebox.invokeMethod(ChangeListenerHolder, "sendMsg", message, sessionId)

        then: "应该调用proxy发送消息"
        1 * mockQinXinFlowMessageProxy.sendMsgBySession(_, _) >> { SendMsgBySession.Arg arg, Map headers ->
            assert arg.getContent() == message
            assert arg.getSessionId() == sessionId
            assert arg.getSenderId() == "E.fs.7576"
            assert headers["X-FS-TENANT-ID"] == "1"
        }
    }

    def "test sendMsg with exception"() {
        given: "准备会抛出异常的proxy"
        def message = "test message"
        def sessionId = "session123"

        when: "调用sendMsg方法"
        Whitebox.invokeMethod(ChangeListenerHolder, "sendMsg", message, sessionId)

        then: "应该捕获异常而不抛出"
        1 * mockQinXinFlowMessageProxy.sendMsgBySession(_, _) >> { throw new RuntimeException("Send failed") }
        noExceptionThrown()
    }

    def "test getNotifySessionId with custom config"() {
        given: "设置自定义sessionId"
        AppFrameworkConfig.metaClass.static.getLoadConfigFailNotifySessionId = { -> "custom-session-id" }

        when: "调用getNotifySessionId方法"
        def result = Whitebox.invokeMethod(ChangeListenerHolder, "getNotifySessionId")

        then: "应该返回自定义sessionId"
        result == "custom-session-id"
    }

    def "test getNotifySessionId with default"() {
        given: "设置默认sessionId"
        AppFrameworkConfig.metaClass.static.getLoadConfigFailNotifySessionId = { -> null }

        when: "调用getNotifySessionId方法"
        def result = Whitebox.invokeMethod(ChangeListenerHolder, "getNotifySessionId")

        then: "应该返回默认sessionId"
        result == ChangeListenerHolder.CONFIG_NOTIFY_DEFAULT_SESSION_ID
    }

    def "test constants"() {
        expect: "验证常量值"
        ChangeListenerHolder.CONFIG_NOTIFY_DEFAULT_SESSION_ID == "8e96b84c295a4f9a8f75b59b92d0efc4"
        ChangeListenerHolder.APP_NAME != null
    }

    def cleanup() {
        // 清理元类修改
        RestUtils.metaClass = null
        AppFrameworkConfig.metaClass = null
        ParallelUtils.metaClass = null
        
        // 重置静态字段
        Whitebox.setInternalState(ChangeListenerHolder, "qinXinFlowMessageProxy", null)
        Whitebox.setInternalState(ChangeListenerHolder, "redissonClient", null)
        
        // 清理线程中断状态
        Thread.interrupted()
    }
}
