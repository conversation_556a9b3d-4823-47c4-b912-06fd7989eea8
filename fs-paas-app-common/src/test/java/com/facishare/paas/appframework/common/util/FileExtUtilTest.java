package com.facishare.paas.appframework.common.util;

import org.junit.Assert;
import org.junit.Test;

/**
 * FileExtUtil 单元测试
 * 主要测试URL编码方案的转义和反转义功能
 */
public class FileExtUtilTest {

    @Test
    public void testEscapeAndUnescapeFileName() {
        // 测试基本转义和反转义
        String original1 = "test#file.jpg";
        String escaped1 = FileExtUtil.escapeFileName(original1);
        String unescaped1 = FileExtUtil.unescapeFileName(escaped1);
        Assert.assertEquals("基本#号转义测试", original1, unescaped1);
        
        // 测试多个#号
        String original2 = "test#file#name.pdf";
        String escaped2 = FileExtUtil.escapeFileName(original2);
        String unescaped2 = FileExtUtil.unescapeFileName(escaped2);
        Assert.assertEquals("多个#号转义测试", original2, unescaped2);
        
        // 测试边界情况
        Assert.assertEquals("空字符串测试", "", FileExtUtil.escapeFileName(""));
        Assert.assertEquals("null测试", null, FileExtUtil.escapeFileName(null));
        Assert.assertEquals("普通文件名测试", "normalfile.txt", FileExtUtil.unescapeFileName("normalfile.txt"));
    }

    @Test
    public void testConflictingCharacters() {
        // 测试包含%23的文件名（关键测试）
        String original1 = "report%23data.pdf";
        String escaped1 = FileExtUtil.escapeFileName(original1);
        String unescaped1 = FileExtUtil.unescapeFileName(escaped1);
        Assert.assertEquals("包含%23的文件名测试", original1, unescaped1);
        
        // 测试包含%和#的文件名
        String original2 = "file%name#test.doc";
        String escaped2 = FileExtUtil.escapeFileName(original2);
        String unescaped2 = FileExtUtil.unescapeFileName(escaped2);
        Assert.assertEquals("包含%和#的文件名测试", original2, unescaped2);
        
        // 测试复杂的特殊字符组合
        String original3 = "测试文件 #%&+.txt";
        String escaped3 = FileExtUtil.escapeFileName(original3);
        String unescaped3 = FileExtUtil.unescapeFileName(escaped3);
        Assert.assertEquals("复杂特殊字符测试", original3, unescaped3);
    }

    @Test
    public void testBasicWorkflow() {
        // 基本工作流测试：简单的#转义
        String originalFileName = "test#file.pdf";
        
        // 1. 转义文件名
        String escapedFileName = FileExtUtil.escapeFileName(originalFileName);
        Assert.assertNotEquals("转义后应该不同", originalFileName, escapedFileName);
        
        // 2. 反转义文件名
        String unescapedFileName = FileExtUtil.unescapeFileName(escapedFileName);
        Assert.assertEquals("反转义后应该恢复原始", originalFileName, unescapedFileName);
    }
}
