package com.facishare.paas.appframework.common.util;

import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * XmlUtil 单元测试
 */
public class XmlUtilTest {

  @Test
  public void testCreateExportFile() {
    Map<String, String> pathAndName = new HashMap<>();
    pathAndName.put("N111111", "export1");
    pathAndName.put("N222222", "export2");
    
    String fileName = "main_export.xlsx";
    
    String xml = XmlUtil.createExportFile(pathAndName, fileName);
    
    Assert.assertNotNull(xml);
    Assert.assertTrue(xml.contains("<Document>"));
    Assert.assertTrue(xml.contains("</Document>"));
    Assert.assertTrue(xml.contains(fileName));
    Assert.assertTrue(xml.contains("export1"));
    Assert.assertTrue(xml.contains("export2"));
    Assert.assertTrue(xml.contains("N111111"));
    Assert.assertTrue(xml.contains("N222222"));
  }

  @Test
  public void testHasFileWithoutFiles() throws Exception {
    String xmlWithoutFiles = "<Document><D N=\"测试目录\"></D></Document>";
    Assert.assertFalse(XmlUtil.hasFile(xmlWithoutFiles));
  }

  @Test
  public void testHasFileEmptyDocument() throws Exception {
    String emptyXml = "<Document></Document>";
    Assert.assertFalse(XmlUtil.hasFile(emptyXml));
  }

  @Test
  public void testHasFileWithFiles() throws Exception {
    String xmlWithFiles = "<Document><F N=\"test.jpg\" NP=\"N123456\"/></Document>";
    Assert.assertTrue(XmlUtil.hasFile(xmlWithFiles));
  }

  @Test(expected = org.dom4j.DocumentException.class)
  public void testHasFileInvalidXml() throws Exception {
    String invalidXml = "<Document><D N=\"测试";
    XmlUtil.hasFile(invalidXml);
  }

  @Test
  public void testCreateExportFileWithEmptyMap() {
    Map<String, String> emptyMap = new HashMap<>();
    String fileName = "test.xlsx";
    
    String xml = XmlUtil.createExportFile(emptyMap, fileName);
    
    Assert.assertNotNull(xml);
    Assert.assertTrue(xml.contains("<Document>"));
    Assert.assertTrue(xml.contains("</Document>"));
    Assert.assertTrue(xml.contains(fileName));
  }

  @Test
  public void testCreateExportFileWithNullFileName() {
    Map<String, String> pathAndName = new HashMap<>();
    pathAndName.put("N111111", "export1");
    
    String xml = XmlUtil.createExportFile(pathAndName, null);
    
    Assert.assertNotNull(xml);
    Assert.assertTrue(xml.contains("<Document>"));
    Assert.assertTrue(xml.contains("</Document>"));
    Assert.assertTrue(xml.contains("export1"));
    Assert.assertTrue(xml.contains("N111111"));
  }

  @Test
  public void testCreateExportFileWithSpecialCharacters() {
    Map<String, String> pathAndName = new HashMap<>();
    pathAndName.put("N111111", "测试文件");
    
    String fileName = "主文件.xlsx";
    
    String xml = XmlUtil.createExportFile(pathAndName, fileName);
    
    Assert.assertNotNull(xml);
    Assert.assertTrue(xml.contains("<Document>"));
    Assert.assertTrue(xml.contains("</Document>"));
    Assert.assertTrue(xml.contains(fileName));
    Assert.assertTrue(xml.contains("测试文件"));
    Assert.assertTrue(xml.contains("N111111"));
  }
}
