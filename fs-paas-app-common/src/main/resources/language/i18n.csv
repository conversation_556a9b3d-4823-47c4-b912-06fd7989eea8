key,zh-C<PERSON>,zh-TW,en
paas.udobj.code_abnormal,编码异常,编码异常,编码异常
paas.udobj.no_exist_tenant,无企业信息,无企业信息,无企业信息
paas.udobj.apprflow.trigger_abnormal,触发审批流异常{0},触发审批流异常{0},触发审批流异常{0}
paas.udobj.apprflow.trigger_success,审批流触发成功,审批流触发成功,审批流触发成功
paas.udobj.apprflow.exist_processing,已存在进行中的审批,已存在进行中的审批,已存在进行中的审批
paas.udobj.apprflow.trigger_fail,触发审批流失败,触发审批流失败,触发审批流失败
paas.udobj.apprflow.no_exist,没有匹配的审批流,没有匹配的审批流,没有匹配的审批流
paas.udobj.apprflow.filter_abnormal,流程过滤器异常触发审批失败请联系管理员更改流程配置,流程过滤器异常触发审批失败请联系管理员更改流程配置,流程过滤器异常触发审批失败请联系管理员更改流程配置
paas.udobj.apprflow.restart,触发审批流失败请编辑重新发起,触发审批流失败请编辑重新发起,触发审批流失败请编辑重新发起
paas.udobj.apprflow.dept_higher,审批提交人的所在部门高于流程定义的部门,审批提交人的所在部门高于流程定义的部门,审批提交人的所在部门高于流程定义的部门
paas.udobj.action.view_feed_card,查看销售记录,查看销售记录,查看销售记录
paas.udobj.action.unknow_action,未知操作,未知操作,未知操作
paas.udobj.action.add_spec,增加规格,增加规格,增加规格
paas.udobj.action.return,退回,退回,退回
paas.udobj.action.move,转移,转移,转移
paas.udobj.action.choose,领取,领取,领取
paas.udobj.action.allocate,分配,分配,分配
paas.udobj.action.take_back,收回,收回,收回
paas.udobj.action.update_deal_status,更改成交状态,更改成交状态,更改成交状态
paas.udobj.action.save_to_phone,保存到手机,保存到手机,保存到手机
paas.udobj.action.change_sale_action,更换销售流程,更换销售流程,更换销售流程
paas.udobj.action.collect,收款,收款,收款
paas.udobj.action.deal,转换,转换,转换
paas.udobj.action.close,无效,无效,无效
paas.udobj.action.follow_up,跟进中,跟进中,跟进中
paas.udobj.action.recall,撤回,撤回,Withdraw
paas.udobj.action.view_logistics,查看物流,查看物流,查看物流
paas.udobj.action.confirm_receipt,确认收货,确认收货,确认收货
paas.udobj.action.confirm_delivery,确认发货,确认发货,确认发货
paas.udobj.action.add_delivery_not,创建发货单,创建发货单,创建发货单
paas.udobj.action.confirm_inbound,确认入库,确认入库,确认入库
paas.udobj.action.status_off,下架,下架,下架
paas.udobj.action.status_on,上架,上架,上架
paas.udobj.action.view_before_sale_action,查看售前流程,查看售前流程,查看售前流程
paas.udobj.action.view_after_sale_action,查看售后流程,查看售后流程,查看售后流程
paas.udobj.action.confirm,确认,确认,确认
paas.udobj.action.reject,驳回,驳回,驳回
paas.udobj.action.change_confirmor,更换确认人,更换确认人,更换确认人
paas.udobj.action.scan_card,扫名片,扫名片,扫名片
paas.udobj.action.import_from_address_book,通讯录导入,通讯录导入,通讯录导入
paas.udobj.unsupport_date_type,不支持的日期类型{0},不支持的日期类型{0},不支持的日期类型{0}
paas.udobj.datetime_convert_abnormal,日期时间转换异常{0},日期时间转换异常{0},日期时间转换异常{0}
paas.udobj.choose_param_method,请选择带参数的方法,请选择带参数的方法,请选择带参数的方法
paas.udobj.constant.dept,所属部门,所属部门,所属部门
paas.udobj.constant.owner_dept,负责人所在部门,负责人所在部门,负责人所在部门
paas.udobj.constant.relevant_team,相关团队,相关团队,相关团队
paas.udobj.constant.team_member_role,成员员工
paas.udobj.constant.team_member,成员角色
paas.udobj.constant.team_member_permission,成员权限类型
paas.udobj.constant.lock_rule,锁定规则,锁定规则,锁定规则
paas.udobj.constant.lock_status,锁定状态,锁定状态,锁定状态
paas.udobj.constant.lock_user,加锁人,加锁人,加锁人
paas.udobj.constant.life_status,生命状态,生命状态,生命状态
paas.udobj.constant.before_invalid_status,作废前生命状态,作废前生命状态,作废前生命状态
paas.udobj.constant.status_ineffective,未生效,未生效,未生效
paas.udobj.constant.status_under_review,审核中,审核中,审核中
paas.udobj.constant.status_normal,正常,正常,正常
paas.udobj.constant.status_in_change,变更中,变更中,变更中
paas.udobj.constant.status_invalid,作废,作废,作废
paas.udobj.action.delete,删除,删除,删除
paas.udobj.action.view_detail,查看详情,查看详情,查看详情
paas.udobj.action.view_list,查看列表,查看列表,查看列表
paas.udobj.action.add,新建,新建,新建
paas.udobj.action.edit,编辑,编辑,编辑
paas.udobj.action.import,导入,导入,导入
paas.udobj.action.export,导出,导出,导出
paas.udobj.export_file,导出图片/附件,導出圖片/附件,
paas.udobj.action.recover,恢复,恢复,恢复
paas.udobj.action.print,打印,,
paas.udobj.action.change_owner,更换负责人,
paas.udobj.action.add_team_member,添加团队成员,添加团队成员,添加团队成员
paas.udobj.action.edit_team_member,编辑团队成员,编辑团队成员,编辑团队成员
paas.udobj.action.delete_team_member,删除团队成员,删除团队成员,删除团队成员
paas.udobj.action.relate,关联,关联,关联
paas.udobj.action.bulk_disrelate,解除关联,解除关联,解除关联
paas.udobj.action.start_bpm,发起流程,发起流程,发起流程
paas.udobj.action.view_whole_bpm,查看完整流程,查看完整流程,查看完整流程
paas.udobj.action.stop_bpm,终止业务流程,终止业务流程,终止业务流程
paas.udobj.action.change_bpm_approver,更换流程处理人,更换流程处理人,更换流程处理人
paas.udobj.action.add_event,新建销售记录,新建销售记录,新建销售记录
paas.udobj.action.sign_in,签到,签到,签到
paas.udobj.action.sign_out,签退,签退,签退
paas.udobj.action.pay,待收款,待收款,待收款
paas.udobj.action.intelligent_form,智能表单,智能表單,智能表单
paas.udobj.action.lock,锁定,锁定,锁定
paas.udobj.action.not_lock,未锁定,,
paas.udobj.action.unlock,解锁,解锁,解锁
paas.udobj.action.modifylog_recover,修改记录恢复,修改记录恢复,修改记录恢复
paas.udobj.action.sale_record,销售记录,销售记录,销售记录
paas.udobj.action.dial,打电话,打电话,打电话
paas.udobj.action.send_mail,发邮件,发邮件,发邮件
paas.udobj.action.discuss,转发,转发,转发ddkjk
paas.udobj.action.schedule,日程,日程,日程
paas.udobj.action.remind,提醒,提醒,提醒
paas.udobj.one_to_one,一对一,一对一,一对一
paas.udobj.one_to_many,一对多,一对多,一对多
paas.udobj.many_to_one,多对一,多对一,多对一
paas.udobj.many_to_many,多对多,多对多,多对多
paas.udobj.constant.read_only,只读,只读,只读
paas.udobj.constant.private,私有,私有,私有
paas.udobj.constant.public_read,公开只读,公开只读,公开只读
paas.udobj.constant.public_read_write,公开读写删,公开读写删,公开读写删
paas.udobj.constant.not_exist,不在其中,不在其中,不在其中
ProductObj.attribute.self.display_name,产品,产品,产品
SalesOrderObj.attribute.self.display_name,销售订单,销售订单,销售订单
ContactObj.attribute.self.display_name,联系人,联系人,联系人
LeadsObj.attribute.self.display_name,销售线索,销售线索,销售线索
AccountObj.attribute.self.display_name,客户,客户,客户
RefundObj.attribute.self.display_name,退款,退款,退款
ContractObj.attribute.self.display_name,合同,合同,合同
OpportunityObj.attribute.self.display_name,商机,商机,商机
paas.udobj.errorcode.success,成功,成功,成功
paas.udobj.errorcode.paas_permission_error,权限接口出错,权限接口出错,权限接口出错
paas.udobj.errorcode.mini_version,您是版无法请求该方法,您是版无法请求该方法,您是版无法请求该方法
paas.udobj.errorcode.version_error,版本错误,版本错误,版本错误
paas.udobj.errorcode.max_role,您购买的版本自定义角色已达到上限,您购买的版本自定义角色已达到上限,您购买的版本自定义角色已达到上限
paas.udobj.errorcode.basic_version,您使用的是基础版不支持自定义角色需要升级至标准版或企业版方可使用,您使用的是基础版不支持自定义角色需要升级至标准版或企业版方可使用,您使用的是基础版不支持自定义角色需要升级至标准版或企业版方可使用
paas.udobj.errorcode.unknow_error,未知系统异常,未知系统异常,未知系统异常
paas.udobj.errorcode.config_file_fail,从配置中心获取配置文件有误,从配置中心获取配置文件有误,从配置中心获取配置文件有误
paas.udobj.errorcode.user_id_empty,用户为空,用户为空,用户为空
paas.udobj.errorcode.ei_empty,企业为空,企业为空,企业为空
paas.udobj.errorcode.fail_convert_apiname,不支持的类型无法转换成,不支持的类型无法转换成,不支持的类型无法转换成
paas.udobj.errorcode.on_sale_fail,外部服务调用异常上下架接口异常,外部服务调用异常上下架接口异常,外部服务调用异常上下架接口异常
paas.udobj.errorcode.change_owner_fail,外部服务调用异常更换负责人接口异常,外部服务调用异常更换负责人接口异常,外部服务调用异常更换负责人接口异常
paas.udobj.errorcode.field_update_fail,由于的业务限制某些对象的字段不允许被更新,由于的业务限制某些对象的字段不允许被更新,由于的业务限制某些对象的字段不允许被更新
paas.udobj.errorcode.data_create_fail,由于的业务限制某些对象的字段不允许在创建时指定,由于的业务限制某些对象的字段不允许在创建时指定,由于的业务限制某些对象的字段不允许在创建时指定
paas.udobj.errorcode.rest_result_error,外部服务返回数据解析后有误,外部服务返回数据解析后有误,外部服务返回数据解析后有误
paas.udobj.errorcode.http_204,外部服务返回状态码为,外部服务返回状态码为,外部服务返回状态码为
paas.udobj.errorcode.rest_result_empty,外部服务返回数据为空,外部服务返回数据为空,外部服务返回数据为空
paas.udobj.errorcode.no_reference,未发现两个对象间存在关联关系,未发现两个对象间存在关联关系,未发现两个对象间存在关联关系
paas.udobj.errorcode.layout_not_init,此对象的未初始化或初始化有误请联系管理员,此对象的未初始化或初始化有误请联系管理员,此对象的未初始化或初始化有误请联系管理员
paas.udobj.errorcode.can_not_disrelate,对象上的查找类型字段为必填所以无法解除关联,对象上的查找类型字段为必填所以无法解除关联,对象上的查找类型字段为必填所以无法解除关联
paas.udobj.errorcode.data_no_reference,未发现两个对象数据之间存在关联关系,未发现两个对象数据之间存在关联关系,未发现两个对象数据之间存在关联关系
paas.udobj.errorcode.data_deleted,数据被作废或已被删除,数据被作废或已被删除,数据被作废或已被删除
paas.udobj.errorcode.index_name_empty,字段索引为空,字段索引为空,字段索引为空
paas.udobj.errorcode.param_wrong,操作的传入参数有误,操作的传入参数有误,操作的传入参数有误
paas.udobj.errorcode.unsupport_action,不支持此自定义操作,不支持此自定义操作,不支持此自定义操作
paas.udobj.errorcode.metadata_error,元数据报错,元数据报错,元数据报错
paas.udobj.errorcode.layout_compoennt_error,此对象的的结构有误,此对象的的结构有误,此对象的的结构有误
paas.udobj.errorcode.can_not_ref_self,查找数据不能关联自己,查找数据不能关联自己,查找数据不能关联自己
paas.udobj.errorcode.object_name_duplicate,已经存在同名的对象请更换名字,已经存在同名的对象请更换名字,已经存在同名的对象请更换名字
paas.udobj.errorcode.layout_name_duplicate,已经存在同名的请更换名字,已经存在同名的请更换名字,已经存在同名的请更换名字
paas.udobj.errorcode.max_object_count,对象数量超过了企业可以购买的最大限制,对象数量超过了企业可以购买的最大限制,对象数量超过了企业可以购买的最大限制
paas.udobj.errorcode.unsupport_udobj,您所在企业目前不支持自定义对象如果想获得支持请联系纷享客服,您所在企业目前不支持自定义对象如果想获得支持请联系纷享客服,您所在企业目前不支持自定义对象如果想获得支持请联系纷享客服
paas.udobj.errorcode.max_layout_count,布局数量超过了企业可以购买的最大限制,布局数量超过了企业可以购买的最大限制,布局数量超过了企业可以购买的最大限制
paas.udobj.errorcode.max_record_type_count,业务类型数量超过了企业可以购买的最大限制,业务类型数量超过了企业可以购买的最大限制,业务类型数量超过了企业可以购买的最大限制
paas.udobj.errorcode.apprflow_processing,审批流状态为进行中,审批流状态为进行中,审批流状态为进行中
paas.udobj.errorcode.max_field_count,自定义字段数量超出限制,自定义字段数量超出限制,自定义字段数量超出限制
paas.udobj.errorcode.layout_not_exist,布局不存在或者已经被删除,布局不存在或者已经被删除,布局不存在或者已经被删除
paas.udobj.errorcode.default_layout_unsupport,默认布局不能进行当前操作,默认布局不能进行当前操作,默认布局不能进行当前操作
paas.udobj.errorcode.max_name_length,主属性长度超过,主属性长度超过,主属性长度超过
paas.udobj.errorcode.udobj_new_project_error,自定义对象新项目异常,自定义对象新项目异常,自定义对象新项目异常
paas.udobj.errorcode.max_print_template,打印模板数量超过上限,打印模板数量超过上限,打印模板数量超过上限
paas.udobj.errorcode.template_name_duplicate,模板名字重复,模板名字重复,模板名字重复
paas.udobj.errorcode.paas_db_error,数据库出错,数据库出错,数据库出错
paas.udobj.errorcode.auth_cal_fail,权限模块后台未知异常需要联系技术支持人员查看日志,权限模块后台未知异常需要联系技术支持人员查看日志,权限模块后台未知异常需要联系技术支持人员查看日志
paas.udobj.errorcode.rest_call_fail,调用接口失败,调用接口失败,调用接口失败
paas.udobj.errorcode.no_data_permission,您没有这条数据的数据权限,您没有这条数据的数据权限,您没有这条数据的数据权限
paas.udobj.errorcode.action_no_permission,无权进行此操作,无权进行此操作,无权进行此操作
paas.udobj.errorcode.main_role_not_config,该人员没有设置主角色请设置主角色,该人员没有设置主角色请设置主角色,该人员没有设置主角色请设置主角色
paas.udobj.errorcode.relevant_team_unsupport,不可对从对象的相关团队进行操作,不可对从对象的相关团队进行操作,不可对从对象的相关团队进行操作
paas.udobj.func_fail,执行函数失败,执行函数失败,执行函数失败
paas.udobj.verify_func_fail,执行验证函数失败,执行验证函数失败,执行验证函数失败
paas.udobj.team_member,团队成员,团队成员,团队成员
paas.udobj.responsible_superior,数据负责人上级,数据负责人上级,数据负责人上级
paas.udobj.data_team_member_superior,数据团队成员上级,数据团队成员上级,数据团队成员上级
paas.udobj.data_main_depart_responsible,数据负责人所在主部门负责人,数据负责人所在主部门负责人,数据负责人所在主部门负责人
paas.udobj.data_team_depart_responsible,数据相关团队成员所在主部门负责人,数据相关团队成员所在主部门负责人,数据相关团队成员所在主部门负责人
paas.udobj.data_responsible,数据负责人,数据负责人,数据负责人
paas.udobj.extension_field,扩展字段,扩展字段,扩展字段
paas.udobj.department,部门,部门,部门
paas.udobj.user_group,用户组,用户组,用户组
paas.udobj.user,用户,用户,用户
paas.udobj.user_id,用户id,用户id,用户id
paas.udobj.department_resiponsible,部门负责人,部门负责人,部门负责人
paas.udobj.role,角色,角色,Role
paas.udobj.role_id,角色id,角色id,角色id
paas.udobj.select_one_not_empty,单选其他选项不能为空,单选其他选项不能为空,单选其他选项不能为空
paas.udobj.select_many_not_empty,多选其他选项不能为空,多选其他选项不能为空,多选其他选项不能为空
paas.udobj.field_read_only_not_operate,"字段[{0}]是只读字段，您无权限操作。","字段[{0}]是只读字段，您无权限操作。","字段[{0}]是只读字段，您无权限操作。"
paas.udobj.operate_fail,操作失败,操作失败,操作失败
paas.udobj.mapping_rule_not_exist,验证函数执行失败,验证函数执行失败,验证函数执行失败
paas.udobj.field_ust_fill,字段{0}为必填字段,字段{0}为必填字段,字段{0}为必填字段
paas.udobj.finish,完成,完成,Finish
paas.udobj.init,初始化,初始化,初始化
paas.udobj_rest_response_error,外部REST服务返回数据解析后有误,外部REST服务返回数据解析后有误,外部REST服务返回数据解析后有误
paas.udobj.no_permission,无权限,无权限,无权限
paas.udobj.read_write,读写,读写,读写
paas.udobj.responsible,负责人,负责人,负责人
paas.udobj.normal_member,普通成员,普通成员,普通成员
paas.udobj.intelliform_note,智能表单的数据提交者，系统将该角色赋予下游用户后，下游用户即可提交表单数据。该角色为系统自动创建，不可更改或删除。,智能表单的数据提交者，系统将该角色赋予下游用户后，下游用户即可提交表单数据。该角色为系统自动创建，不可更改或删除。,智能表单的数据提交者，系统将该角色赋予下游用户后，下游用户即可提交表单数据。该角色为系统自动创建，不可更改或删除。
paas.udobj.intelliform_role,智能表单角色,智能表单角色,智能表单角色
paas.udobj.figure,盘点,盤點,Count
paas.udobj.user_define_object,自定义对象,自定义对象,自定义对象
paas.udobj.tenant_id_empty,企业ID(Enterprise ID)为空,企业ID(Enterprise ID)为空,企业ID(Enterprise ID)为空
paas.udobj.user_id_empty,用户ID(User ID)为空,用户ID(User ID)为空,用户ID(User ID)为空
paas.udobj.rest_invoke_response_error,外部REST服务调用异常-返回结果为空,外部REST服务调用异常-返回结果为空,外部REST服务调用异常-返回结果为空
paas.udobj.rest_invoke_resolve_error,外部REST服务返回数据解析后为空,外部REST服务返回数据解析后为空,外部REST服务返回数据解析后为空
paas.udobj.deleted,被删除,被删除,被删除
paas.udobj.increased,被增加,被增加,被增加
paas.udobj.resetted,被重置,被重置,被重置
paas.udobj.privilege,权限,权限,权限
paas.udobj.user_defined_role_quota_error,"您所在企业购买的{0}支持{1}个自定义角色","您所在企业购买的{0}支持{1}个自定义角色","您所在企业购买的{0}支持{1}个自定义角色"
paas.udobj.stuff_role_log,"员工 {0},角色 {1}","员工 {0},角色 {1}","员工 {0},角色 {1}"
paas.udobj.visit_admin,访问管理员,访问管理员,访问管理员
paas.udobj.not_crm_admin,您不是CRM管理员,您不是CRM管理员,您不是CRM管理员
paas.udobj.allow_repeat_tool,允许使用查重工具,允许使用查重工具,允许使用查重工具
paas.udobj.user_define_role,自定义角色,自定义角色,自定义角色
paas.udobj.param_empty,参数为空,参数为空,参数为空
paas.udobj.server_error,后台服务异常,后台服务异常,后台服务异常
paas.udobj.quota_lack,配额不足,配额不足,配额不足
paas.udobj.quota_fail,开通配额失败,开通配额失败,开通配额失败
paas.udobj.user_role,"员工{0}，角色{1}","员工{0}，角色{1}","员工{0}，角色{1}"
paas.udobj.visible_scope_fail,开通可见范围失败,开通可见范围失败,开通可见范围失败
paas.udobj.order_pass,定货通,定货通,定货通
paas.udobj.search_note_short,"搜索内容太短,请输入长度大于两个字符的搜索内容。","搜索内容太短,请输入长度大于两个字符的搜索内容。","搜索内容太短,请输入长度大于两个字符的搜索内容。"
paas.udobj.group_member1,"用户组 {0}，原用户组成员 {1} ；现用户组成员 {2}","用户组 {0}，原用户组成员 {1} ；现用户组成员 {2}","用户组 {0}，原用户组成员 {1} ；现用户组成员 {2}"
paas.udobj.group_member2,"用户组{0}，用户组成员{1}","用户组{0}，用户组成员{1}","用户组{0}，用户组成员{1}"
paas.udobj.repetition_checker,查重工具,查重工具,查重工具
paas.udobj.target_value,目标值,目标值,目标值
paas.udobj.role_func_privilege,"角色 {0} 针对对象 {1} 下的功能权限","角色 {0} 针对对象 {1} 下的功能权限","角色 {0} 针对对象 {1} 下的功能权限"
paas.udobj.role_field_privilege,"角色 {0} 针对对象 {1} 下的字段权限","角色 {0} 针对对象 {1} 下的字段权限","角色 {0} 针对对象 {1} 下的字段权限"
paas.udobj.role_name,角色 {0},角色 {0},角色 {0}
paas.udobj.open_crm_fail,开通crm可见失败。,开通crm可见失败。,开通crm可见失败。
paas.udobj.crm_allowance_lack,您的CRM配额不足。,您的CRM配额不足。,您的CRM配额不足。
paas.udobj.remove_crm.fail,移除crm可见失败,移除crm可见失败,移除crm可见失败
paas.udobj.invoke_by_edition,分版调用,分版调用,分版调用
paas.udobj.base_data_privilege_invoke,基础数据权限调用 Facishare PaaS DataRights,基础数据权限调用 Facishare PaaS DataRights,基础数据权限调用 Facishare PaaS DataRights
paas.udobj.get_version,获得版本号,获得版本号,获得版本号
paas.udobj.edition_service,分版服务,分版服务,分版服务
paas.udobj.user_define_object_unsupport,您所在企业目前不支持自定义对象，如果想获得支持，请联系纷享客服。,您所在企业目前不支持自定义对象，如果想获得支持，请联系纷享客服。,您所在企业目前不支持自定义对象，如果想获得支持，请联系纷享客服。
paas.udobj.support_user_define_object,您所在企业目前支持{0}个自定义对象，如果想获得更多支持，请联系纷享客服申请资源扩展包。,您所在企业目前支持{0}个自定义对象，如果想获得更多支持，请联系纷享客服申请资源扩展包。,您所在企业目前支持{0}个自定义对象，如果想获得更多支持，请联系纷享客服申请资源扩展包。
paas.udobj.support_layout,您所在企业目前支持{0}个页面布局，如果想获得更多支持，请联系纷享客服申请资源扩展包。,您所在企业目前支持{0}个页面布局，如果想获得更多支持，请联系纷享客服申请资源扩展包。,您所在企业目前支持{0}个页面布局，如果想获得更多支持，请联系纷享客服申请资源扩展包。
paas.udobj.support_service_type,您所在企业目前支持{0}个多业务类型，如果想获得更多支持，请联系纷享客服申请资源扩展包。,您所在企业目前支持{0}个多业务类型，如果想获得更多支持，请联系纷享客服申请资源扩展包。,您所在企业目前支持{0}个多业务类型，如果想获得更多支持，请联系纷享客服申请资源扩展包。
paas.udobj.support_data_share,您所在企业目前支持{0}条数据共享。,您所在企业目前支持{0}条数据共享。,您所在企业目前支持{0}条数据共享。
paas.udobj.share_rule_max,预设对象和自定义对象条件共享规则一共最多设置{0}条。,预设对象和自定义对象条件共享规则一共最多设置{0}条。,预设对象和自定义对象条件共享规则一共最多设置{0}条。
paas.udobj.function_upper_limit,您所在企业自定义函数数量已达上限，最多{0}个。,您所在企业自定义函数数量已达上限，最多{0}个。,您所在企业自定义函数数量已达上限，最多{0}个。
paas.udobj.function_not_allow,您没有自定义函数权限,您没有自定义函数权限,您没有自定义函数权限
paas.udobj.name_length_exceed,主属性长度超过{0},主属性长度超过{0},主属性长度超过{0}
paas.udobj.create_save,新建保存
paas.udobj.edit_save,编辑保存
paas.udobj.company_message_group,企信群,企信群,企信群
paas.udobj.whole,全部,全部,All
paas.udobj.my_response,我负责的,我负责的,我负责的
paas.udobj.my_part,我参与的,我参与的,我参与的
paas.udobj.my_response_depart,我负责部门的,我负责部门的,我负责部门的
paas.udobj.my_subordinate_response,我下属负责的,我下属负责的,我下属负责的
paas.udobj.my_subordinate_part,我下属参与的,我下属参与的,我下属参与的
paas.udobj.share_to_me,共享给我的,共享给我的,共享给我的
paas.udobj.object_mapping,对象映射,对象映射,对象映射
paas.udobj.field_update,字段更新,字段更新,字段更新
paas.udobj.send_remind,发送提醒,发送提醒,发送提醒
paas.udobj.user_define_func,自定义函数,自定义函数,自定义函数
paas.udobj.unknow_life_status,未知的生命状态：{0},未知的生命状态：{0},未知的生命状态：{0}
paas.udobj.receipt,收款记录,收款记录,收款记录
paas.udobj.related_object,关联业务对象,关联业务对象,关联业务对象
paas.udobj.field_label_duplicate,字段名称[{0}]重复,字段名称[{0}]重复,字段名称[{0}]重复
paas.udobj.sign_in_info,签到信息,签到信息,签到信息
paas.udobj.time,时间,时间,时间
paas.udobj.location,定位,定位,Locate
paas.udobj.sign_in_option,已签到,已签到,已签到
paas.udobj.sign_out_option,已签退,已签退,已签退
paas.udobj.biz_type,业务类型,业务类型,业务类型
pass.udobj.web_end,web端,web端,web端
paas.udobj.sign_in_device,签到设备号,签到设备号,签到设备号
paas.udobj.system_risk,系统风险,系统风险,系统风险
paas.udobj.default_lock_rule,默认锁定规则,默认锁定规则,默认锁定规则
paas.udobj.select_one_hide_forbid,单选字段: {0}不能隐藏,单选字段: {0}不能隐藏,单选字段: {0}不能隐藏
paas.udobj.field_circle_forbid,字段[{0}]存在成环设置，请重新设置公式,字段[{0}]存在成环设置，请重新设置公式,字段[{0}]存在成环设置，请重新设置公式
paas.udobj.detail_page,详情页,详情页,详情页
paas.udobj.create_page,新建页,新建页,新建页
paas.udobj.edit_page,编辑页,编辑页,编辑页
paas.udobj.detail_info,详细信息,详细信息,详细信息
paas.udobj.receipt_order_no,收款订单号,收款订单号,收款订单号
paas.udobj.receipt_amount,收款金额,收款金额,收款金额
paas.udobj.service_fee,手续费,手续费,手续费
paas.udobj.pay_type,支付方式,支付方式,支付方式
paas.udobj.pay_finish_time,支付完成时间,支付完成时间,支付完成时间
paas.udobj.transaction_time,交易时间,交易时间,交易时间
paas.udobj.pay_enterprise_name,付款方名称,付款方名称,付款方名称
paas.udobj.unit_price,销售单价(元),销售单价(元),销售单价(元)
paas.udobj.return_unit_price,退货单价元,退货单价元,退货单价元
paas.udobj.quantity,数量,数量,数量
paas.udobj.subtotal,小计,小計,Subtotal
paas.udobj.modify_log,修改记录,修改记录,修改记录
paas.udobj.field_disabled,字段:{0}已被禁用/删除,字段:{0}已被禁用/删除,字段:{0}已被禁用/删除
paas.udobj.related_field_disabled,【{0}】引用的字段【{1}】已被禁用/删除,【{0}】引用的字段【{1}】已被禁用/删除,【{0}】引用的字段【{1}】已被禁用/删除
paas.udobj.not_exist,不存在,不存在,不存在
paas.udobj.create,新增,新增,	Add
paas.udobj.query_name_failed,查找主属性列表失败,查找主属性列表失败,查找主属性列表失败
paas.udobj.replace_object_describe_failed,替换对象失败,替换对象失败,替换对象失败
paas.udobj.data_empty,数据为空,数据为空,数据为空
paas.udobj.message_format_error,消息体格式有误。,消息体格式有误。,消息体格式有误。
paas.udobj.data_changed_by_score_rule_log,触发{0} 评分规则，变更 {1},触发{0} 评分规则，变更 {1},触发{0} 评分规则，变更 {1}
paas.udobj.receipt_message_content,"对象名称：{0},对象主属性：{1}, 金额：{2}, 支付时间：{3}","对象名称：{0},对象主属性：{1}, 金额：{2}, 支付时间：{3}","对象名称：{0},对象主属性：{1}, 金额：{2}, 支付时间：{3}"
paas.udobj.receipt_message_title,收款成功通知,收款成功通知,收款成功通知
paas.udobj.id_list_invalid,不合法,不合法,不合法
paas.udobj.system_error,系统异常请联系客服人员,系统异常请联系客服人员,系统异常请联系客服人员
paas.udobj.remove_sale,移除相关团队成员,移除相关团队成员,移除相关团队成员
paas.udobj.reusing_add,重用新建,重用新建,重用新建
paas.udobj.recovery_by_modify,通过修改记录恢复,通过修改记录恢复,通过修改记录恢复
paas.udobj.resuing,重用,重用,重用
paas.udobj.assist_visit,设置协访,设置协访,设置协访
paas.udobj.remove_customer_from_hs,转移出公海,转移出公海,转移出公海
paas.udobj.add_employee,添加相关团队成员,添加相关团队成员,添加相关团队成员
paas.udobj.enable,启用,启用,启用
paas.udobj.disable,停用,停用,	Disable
paas.udobj.modify_sale,编辑相关团队成员,编辑相关团队成员,编辑相关团队成员
paas.udobj.add_address,添加地址,添加地址,添加地址
paas.udobj.modify_address,编辑地址,编辑地址,编辑地址
paas.udobj.delete_address,删除地址,删除地址,删除地址
paas.udobj.add_finance,添加财务信息,添加财务信息,添加财务信息
paas.udobj.modify_finance,编辑财务信息,编辑财务信息,编辑财务信息
paas.udobj.delete_finance,删除财务信息,删除财务信息,删除财务信息
paas.udobj.auto_take_back,自动收回,自动收回,自动收回
paas.udobj.cancel_assist_visit,取消协访,取消协访,取消协访
paas.udobj.handle,处理,處理,Process
paas.udobj.reset,重置,重置,Reset
paas.udobj.work_flow_start,审批提交,审批提交,审批提交
paas.udobj.work_flow_take_back,审批撤回,审批撤回,审批撤回
paas.udobj.work_flow_cancel,取消操作,取消操作,取消操作
paas.udobj.work_flow_complete,审批确认,审批确认,审批确认
paas.udobj.work_flow_reject,审批驳回,审批驳回,审批驳回
paas.udobj.auto_assigned,自动分配,自动分配,自动分配
paas.udobj.create_layout,新建布局,新建布局,新建布局
paas.udobj.update_layout,编辑布局,编辑布局,编辑布局
paas.udobj.delete_layout,删除布局,删除布局,删除布局
paas.udobj.create_object,新建对象,新建对象,新建对象
paas.udobj.update_object,编辑对象,编辑对象,编辑对象
paas.udobj.delete_object,删除对象,删除对象,删除对象
paas.udobj.disable_object,停用对象,停用对象,停用对象
paas.udobj.enable_object,启用对象,启用对象,启用对象
paas.udobj.create_field,新建字段,新建字段,新建字段
paas.udobj.update_field,编辑字段,编辑字段,编辑字段
paas.udobj.delete_field,删除字段,删除字段,删除字段
paas.udobj.disable_field,停用字段,停用字段,停用字段
paas.udobj.enable_field,启用字段,启用字段,启用字段
paas.udobj.update_record_type,编辑类型,编辑类型,编辑类型
paas.udobj.create_record_type,新建类型,新建类型,新建类型
paas.udobj.enable_record_type,启用类型,启用类型,启用类型
paas.udobj.diable_record_type,停用类型,停用类型,停用类型
paas.udobj.delete_record_type,删除类型,删除类型,删除类型
paas.udobj.lock,加锁,加鎖,Lock
paas.udobj.create_buttion,创建按钮,创建按钮,创建按钮
paas.udobj.update_button,更新按钮,更新按钮,更新按钮
paas.udobj.delete_button,删除按钮,删除按钮,删除按钮
paas.udobj.update_import,更新导入,更新导入,更新导入
paas.udobj.change_button_status,启用禁用按钮,启用禁用按钮,启用禁用按钮
paas.udobj.change_partner,更换合作伙伴
paas.udobj.change_partner_owner,更换外部负责人,更换外部负责人,更换外部负责人
paas.udobj.delete_partner,移除合作伙伴,移除合作伙伴,移除合作伙伴
paas.udobj.change_deal_status,修改成交状态,修改成交状态,修改成交状态
paas.udobj.transfer_processing_task,迁移待办,迁移待办,迁移待办
paas.udobj.add_team_member_log,"在{0} 中添加{1},权限{2},团队角色{3}","在{0} 中添加{1},权限{2},团队角色{3}","在{0} 中添加{1},权限{2},团队角色{3}"
paas.udobj.edit_team_member_log,编辑{0}相关团队成员{1}原权限{2}原团队角色{3}现权限{4}现团队角色{5},编辑{0}相关团队成员{1}原权限{2}原团队角色{3}现权限{4}现团队角色{5},编辑{0}相关团队成员{1}原权限{2}原团队角色{3}现权限{4}现团队角色{5}
paas.udobj.remove_team_member_log,在{0}中移除{1},在{0}中移除{1},在{0}中移除{1}
paas.udobj.edit_owner_log_transfer_to_team_member,"{0}, 原负责人 {1} , 转为团队成员{2}; 新负责人: {3}","{0}, 原负责人 {1} , 转为团队成员{2}; 新负责人: {3}","{0}, 原负责人 {1} , 转为团队成员{2}; 新负责人: {3}"
paas.udobj.edit_owner_log_remove_from_team_member,"{0}, 原负责人 {1} , 移除相关团队; 新负责人: {3}","{0}, 原负责人 {1} , 移除相关团队; 新负责人: {3}","{0}, 原负责人 {1} , 移除相关团队; 新负责人: {3}"
paas.udobj.edit_owner_log_change_owner,，原负责人{0}，{1}；新负责人: {2},，原负责人{0}，{1}；新负责人: {2},，原负责人{0}，{1}；新负责人: {2}
paas.udobj.edit_owner_log_transfer_team_member,转换团队成员【{0}】,转换团队成员【{0}】,转换团队成员【{0}】
paas.udobj.edit_owner_log_remove_team_member,移除相关团队,移除相关团队,移除相关团队
paas.udobj.permission_error,无认证用户,无认证用户,无认证用户
paas.udobj.system,系统,系统,系统
paas.udobj.object_describe_diabled,对象已经被禁用不可进行当前操作,对象已经被禁用不可进行当前操作,对象已经被禁用不可进行当前操作
paas.udobj.unsupport_operation,{0}，不能进行[{1}]操作,{0}，不能进行[{1}]操作,{0}，不能进行[{1}]操作
paas.udobj.data_to_invalid_format_error,作废数据解析失败,作废数据解析失败,作废数据解析失败
paas.udobj.data_to_invalid_empty,没有选择需要作废的数据,没有选择需要作废的数据,没有选择需要作废的数据
paas.udobj.data_id_or_api_name_empty,没有找到数据 id 或对象描述名称,没有找到数据 id 或对象描述名称,没有找到数据 id 或对象描述名称
paas.udobj.object_describe_not_found,未找到名称为 {0} 的描述数据,未找到名称为 {0} 的描述数据,未找到名称为 {0} 的描述数据
paas.udobj.related_object_describe_not_found,{0}引用的对象{1}已删除/禁用,{0}引用的对象{1}已删除/禁用,{0}引用的对象{1}已删除/禁用
paas.udobj.outer_owner_empty,外部负责人不能为空,外部负责人不能为空,外部负责人不能为空
paas.udobj.partner_not_exist,数据不存在合伙伙伴，不能添加外部负责人,数据不存在合伙伙伴，不能添加外部负责人,数据不存在合伙伙伴，不能添加外部负责人
paas.udobj.outer_owner,外部负责人,外部负责人,外部负责人
paas.udobj.orig_outer_owner,原外部负责人,原外部负责人,原外部负责人
paas.udobj.new_outer_owner,新外部负责人,新外部负责人,新外部负责人
paas.udobj.change_partner_success,更换合作伙伴成功,更换合作伙伴成功,更换合作伙伴成功
paas.udobj.data_id_empty,数据id不能为空,数据id不能为空,数据id不能为空
paas.udobj.parnter_id_empty,合作伙伴id不能为空,合作伙伴id不能为空,合作伙伴id不能为空
paas.udobj.object_data_not_found,数据不存在,数据不存在,数据不存在
paas.udobj.data_invalid,{0}已作废，无法执行此操作,{0}已作废，无法执行此操作,{0}已作废，无法执行此操作
paas.udobj.orig_partner,原合作伙伴,原合作伙伴,原合作伙伴
paas.udobj.new_partner,新合作伙伴,新合作伙伴,新合作伙伴
paas.udobj.related_muster_unlike,关联的主对象数据不一致,关联的主对象数据不一致,关联的主对象数据不一致
paas.udobj.data_locked,数据已锁定,数据已锁定,数据已锁定
paas.udobj.user_no_data_privilege,当前导入人员对该数据无写入权限,当前导入人员对该数据无写入权限,当前导入人员对该数据无写入权限
paas.udobj.duplicate_search_data_timeout,复制查询数据超时,复制查询数据超时,复制查询数据超时
paas.udobj.not_find_object,未找到为{0}的对象,未找到为{0}的对象,未找到为{0}的对象
paas.udobj.not_sign_out,未启用签退，不能执行签退操作,未启用签退，不能执行签退操作,未启用签退，不能执行签退操作
paas.udobj.must_fill_in,（必填）,（必填）,（必填）
paas.udobj.team_member_change,团队成员变更,团队成员变更,团队成员变更
paas.udobj.user_no_import_privilege,当前操作人没有[{0}]的导入权限,当前操作人没有[{0}]的导入权限,当前操作人没有[{0}]的导入权限
paas.udobj.no_privilege_import_fields,没有有权限导入的字段,没有有权限导入的字段,没有有权限导入的字段
paas.udobj.excel_column_and_template_different,Excel中列标题与导入模板不一致,Excel中列标题与导入模板不一致,Excel中列标题与导入模板不一致
paas.udobj.ios_prison_break,IOS越狱,IOS越狱,IOS越狱
paas.udobj.android_cheat_software,Android作弊软件,Android作弊软件,Android作弊软件
paas.udobj.android_ip_address_forgery,Android伪造地址,Android伪造地址,Android伪造地址
paas.udobj.simulator,模拟器,模拟器,模拟器
paas.udobj.data_not_used,数据已作废或已删除,數據已作廢或已刪除,Data has been deleted or invalided
paas.udobj.not_find_sign_in,在自定义对象中未找到签到类型组件,在自定义对象中未找到签到类型组件,在自定义对象中未找到签到类型组件
paas.udobj.cancelled_role_identity,您被取消[{0}][{1}]的“{2}'{'角色'}'”身份,您被取消[{0}][{1}]的“{2}'{'角色'}'”身份,您被取消[{0}][{1}]的“{2}'{'角色'}'”身份
paas.udobj.owner_can_not_delete,操作\{0}\失败原因负责人不能删除,操作\{0}\失败原因负责人不能删除,操作\{0}\失败原因负责人不能删除
paas.udobj.do_not_input_duplicate_product,请勿输入重复产品,请勿输入重复产品,请勿输入重复产品
paas.udobj.unknown_exception,未知异常,未知异常,未知异常
paas.udobj.department_not_find,{0} 部门不存在或已停用！,{0} 部门不存在或已停用！,{0} 部门不存在或已停用！
paas.udobj.member_not_find,{0}人员不存在或已停用！,{0}人员不存在或已停用！,{0}人员不存在或已停用！
paas.udobj.content_not_unique,{0}内容不唯一！,{0}内容不唯一！,{0}内容不唯一！
paas.udobj.do_not_add_duplicate_product_in_single_price_book,{0}行产品重复同一个价目表下不能导入相同的产品,{0}行产品重复同一个价目表下不能导入相同的产品,{0}行产品重复同一个价目表下不能导入相同的产品
paas.udobj.row_value_duplicate,行{0}中[{1}]的值重复!,行{0}中[{1}]的值重复!,行{0}中[{1}]的值重复!
paas.udobj.related_object_data_delete_or_no_privilege,{0}关联的对象数据不存在或没有权限！,{0}关联的对象数据不存在或没有权限！,{0}关联的对象数据不存在或没有权限！
paas.udobj.unknown_error,未知错误,未知错误,未知错误
paas.udobj.option_fall_short_select_ont_rule,{0}，选项不符合级联单选规则,{0}，选项不符合级联单选规则,{0}，选项不符合级联单选规则
paas.udobj.please_input,请填写{0}！,请填写{0}！,请填写{0}！
paas.udobj.country,国家,国家,国家
paas.udobj.province,省,省,省
paas.udobj.city,市,市,市
paas.udobj.district,区,区,区
paas.udobj.no_this,没有{0}这个{1},没有{0}这个{1},没有{0}这个{1}
paas.udobj.must_within_limits,{0}，必须在{1}范围内,{0}，必须在{1}范围内,{0}，必须在{1}范围内
paas.udobj.option_not_used_can_not_import,{0}选项未启用，不能导入,{0}选项未启用，不能导入,{0}选项未启用，不能导入
paas.udobj.other_and_symbol,(其他[:：])+?,(其他[:：])+?,(其他[:：])+?
paas.udobj.other,其他,其他,其他
paas.udobj.other_option_can_not_is_null,{0}{1}选项必填,{0}{1}选项必填,{0}{1}选项必填
paas.udobj.other_option_only_exist_one,{0}{1}选项只能存在一个,{0}{1}选项只能存在一个,{0}{1}选项只能存在一个
paas.udobj.must_is_one_of,{0}，必须是{1}其中之一,{0}，必须是{1}其中之一,{0}，必须是{1}其中之一
paas.udobj.content_too_long,{0}内容过长！最多可输入{1}个字符！,{0}内容过长！最多可输入{1}个字符！,{0}内容过长！最多可输入{1}个字符！
paas.udobj.date_time_format_error,{0}格式错误！正确格式：2000-01-01 00:00。,{0}格式错误！正确格式：2000-01-01 00:00。,{0}格式错误！正确格式：2000-01-01 00:00。
paas.udobj.date_format_error,{0}格式错误！正确格式：2000-01-01。,{0}格式错误！正确格式：2000-01-01。,{0}格式错误！正确格式：2000-01-01。
paas.udobj.format_error_please_input_amount,{0}格式错误！请填写金额（整数部分不大于{1}位，小数部分不大于{2}位）。,{0}格式错误！请填写金额（整数部分不大于{1}位，小数部分不大于{2}位）。,{0}格式错误！请填写金额（整数部分不大于{1}位，小数部分不大于{2}位）。
paas.udobj.format_error_please_input_integer,{0}格式错误！请填写整数（不大于{1}位）。,{0}格式错误！请填写整数（不大于{1}位）。,{0}格式错误！请填写整数（不大于{1}位）。
paas.udobj.format_error_please_input_decimal,{0}格式错误！请填写小数（整数部分不大于{1}位，小数部分不大于{2}位。,{0}格式错误！请填写小数（整数部分不大于{1}位，小数部分不大于{2}位。,{0}格式错误！请填写小数（整数部分不大于{1}位，小数部分不大于{2}位。
paas.udobj.true,是,是,是
paas.udobj.false,否,否,否
paas.udobj.format_error_only_input_true_or_false_or_option,{0}格式错误！只能填是或者否，或者选项值,{0}格式错误！只能填是或者否，或者选项值,{0}格式错误！只能填是或者否，或者选项值
paas.udobj.format_error,{0}格式错误！,{0}格式错误！,{0}格式错误！
paas.udobj.time_format_error,{0}格式错误！正确格式：10:30:42,{0}格式错误！正确格式：10:30:42,{0}格式错误！正确格式：10:30:42
paas.udobj.format_error_only_input_number_and_sign_less_than_100_byte,"{0}格式错误！只允许输入“0~9”数字和“-,;+”符号，最多支持100个字符。","{0}格式错误！只允许输入“0~9”数字和“-,;+”符号，最多支持100个字符。","{0}格式错误！只允许输入“0~9”数字和“-,;+”符号，最多支持100个字符。"
paas.udobj.object_muster_object_not_unique,该对象主属性不唯一不支持更新导入,该对象主属性不唯一不支持更新导入,该对象主属性不唯一不支持更新导入
paas.udobj.number_already_exist_please_import_again,{0}编号已存在，请重新导入！,{0}编号已存在，请重新导入！,{0}编号已存在，请重新导入！
paas.udobj.operater_no_used_record_type,"{0}，当前操作人没有可用的业务类型","{0}，当前操作人没有可用的业务类型","{0}，当前操作人没有可用的业务类型"
paas.udobj.record_type_is_delete_or_operator_no_privilege_creat_this_record_type,"{0}，业务类型不存在或当前操作人无权限创建该业务类型","{0}，业务类型不存在或当前操作人无权限创建该业务类型","{0}，业务类型不存在或当前操作人无权限创建该业务类型"
paas.udobj.can_not_only_change_detail_object_owner,不可单独更换从对象的负责人,不可单独更换从对象的负责人,不可单独更换从对象的负责人
paas.udobj.already_exist_approval_flow,"{0}:存在正在进行中的审批流，不能进行当前操作","{0}:存在正在进行中的审批流，不能进行当前操作","{0}:存在正在进行中的审批流，不能进行当前操作"
paas.udobj.owner_is_null,{0}id的负责人为空,{0}id的负责人为空,{0}id的负责人为空
paas.udobj.id_is_not_find_can_not_change_owner,"{0}id不存在，无法更换负责人","{0}id不存在，无法更换负责人","{0}id不存在，无法更换负责人"
paas.udobj.is_deleted_can_not_change_owner,"{0}已被删除，无法更换负责人","{0}已被删除，无法更换负责人","{0}已被删除，无法更换负责人"
paas.udobj.change_owner_success,更换负责人成功,更换负责人成功,更换负责人成功
paas.udobj.change_owner_error,更换负责人出现错误:{0},更换负责人出现错误:{0},更换负责人出现错误:{0}
paas.udobj.master_detail_operation_failed,级联操作[{0}]失败{1},级联操作[{0}]失败{1},级联操作[{0}]失败{1}
paas.udobj.master_detail_operation_failed_system_exception,级联操作[{0}]失败系统异常,级联操作[{0}]失败系统异常,级联操作[{0}]失败系统异常
paas.udobj.master_object_is_lock_can_not_unlock_operation_data,"主对象处于锁定状态，不允许对数据[{0}]进行[解锁]操作","主对象处于锁定状态，不允许对数据[{0}]进行[解锁]操作","主对象处于锁定状态，不允许对数据[{0}]进行[解锁]操作"
paas.udobj.edit_or_custom_button_approval_flow_can_not_operation_data_should_unlock_operation,"编辑/自定义按钮触发的审批,不允许对数据[{0}]进行[解锁]操作","编辑/自定义按钮触发的审批,不允许对数据[{0}]进行[解锁]操作","编辑/自定义按钮触发的审批,不允许对数据[{0}]进行[解锁]操作"
paas.udobj.remove_partner_success,移除合作伙伴成功,移除合作伙伴成功,移除合作伙伴成功
paas.udobj.is_invalid_can_not_operation,{0}已作废无法执行此操作,{0}已作废无法执行此操作,{0}已作废无法执行此操作
paas.udobj.is_lock_can_not_operation,{0}已锁定无法执行此操作,{0}已锁定无法执行此操作,{0}已锁定无法执行此操作
paas.udobj.in,，在{0}，,，在{0}，,，在{0}，
paas.udobj.remove,中移除{0}，,中移除{0}，,中移除{0}，
paas.udobj.only_demo_format_equle_can_import_else_other_format_can_not_import_,{0} -{1}-{2}(只有与示例中的格式一致时才能进行数据导入，否则其他格式无法导入),{0} -{1}-{2}(只有与示例中的格式一致时才能进行数据导入，否则其他格式无法导入),{0} -{1}-{2}(只有与示例中的格式一致时才能进行数据导入，否则其他格式无法导入)
paas.udobj.only_demo_format_equle_can_import,{0}-{1}-{2} 10:21(只有与示例中的格式一致时才能进行数据导入，否则其他格式无法导入),{0}-{1}-{2} 10:21(只有与示例中的格式一致时才能进行数据导入，否则其他格式无法导入),{0}-{1}-{2} 10:21(只有与示例中的格式一致时才能进行数据导入，否则其他格式无法导入)
paas.udobj.sample_text,示例文本,示例文本,示例文本
paas.udobj.option_one_option_two,选项一|选项二,选项一|选项二,选项一|选项二
paas.udobj.import_path_sample,路径一|路径二|路径三
paas.udobj.personnel_name,人员姓名,人员姓名,人员姓名
paas.udobj.department_name,部门名称,部门名称,部门名称
paas.udobj.related_object_name,关联对象主属性,关联对象主属性,关联对象主属性
paas.udobj.master_name,主对象主属性,主对象主属性,主对象主属性
paas.udobj.default_record_type,预设业务类型,预设业务类型,预设业务类型
paas.udobj.primary_option_or_secondary_option,一级选项/二级选项,一级选项/二级选项,一级选项/二级选项
paas.udobj.location_information,定位信息,定位信息,定位信息
paas.udobj.china,中国,中国,中国
paas.udobj.bei_jing,北京市,北京市,北京市
paas.udobj.hai_dian,海淀区,海淀区,海淀区
paas.udobj.object_exist_MD_relation_can_not_batch_creat,"该对象存在 MD 关系字段，暂不支持批量创建","该对象存在 MD 关系字段，暂不支持批量创建","该对象存在 MD 关系字段，暂不支持批量创建"
paas.udobj.data_validate_failed_please_again,"数据验证失败，请稍后重试","数据验证失败，请稍后重试","数据验证失败，请稍后重试"
paas.udobj.data_init_excpration_please_again,"数据初始化异常，请稍后重试","数据初始化异常，请稍后重试","数据初始化异常，请稍后重试"
paas.udobj.delete_data_can_not_is_null,删除数据不能为空,删除数据不能为空,删除数据不能为空
paas.udobj.object_id_already_exist_approval_flow,对象 id {0} 存在正在进行中的审批流，不能进行当前操作,对象 id {0} 存在正在进行中的审批流，不能进行当前操作,对象 id {0} 存在正在进行中的审批流，不能进行当前操作
paas.udobj.now_only_export_hundred_thousand_data_by_once,目前只支持一次性导出10万条数据，超出建议您对数据做过滤后分批导出,目前只支持一次性导出10万条数据，超出建议您对数据做过滤后分批导出,目前只支持一次性导出10万条数据，超出建议您对数据做过滤后分批导出
paas.udobj.front_validate_function_failed,前置验证函数失败:{0},前置验证函数失败:{0},前置验证函数失败:{0}
paas.udobj.record_type_is_disabled,业务类型[{0}]已被禁用,业务类型[{0}]已被禁用,业务类型[{0}]已被禁用
paas.udobj.detail_data_number_more,"从数据数量为{0}个,超过{1}的最大限制","从数据数量为{0}个,超过{1}的最大限制","从数据数量为{0}个,超过{1}的最大限制"
paas.udobj.creat_master_data_detail_data_can_not_is_null,创建主数据时，从数据:{0}不能为空,创建主数据时，从数据:{0}不能为空,创建主数据时，从数据:{0}不能为空
paas.udobj.detail_object_is_not_find_MD_field,在从对象中并未找到MD字段,在从对象中并未找到MD字段,在从对象中并未找到MD字段
paas.udobj.MD_invalid_detail_data_time_out,级联作废从对象数据超时,级联作废从对象数据超时,级联作废从对象数据超时
paas.udobj.data_invalid_failed,数据作废失败{0},数据作废失败{0},数据作废失败{0}
paas.udobj.ongoing_approval_flow_and_no_current_operation_possible,对象{0}存在正在进行中的审批流，不能进行当前操作,对象{0}存在正在进行中的审批流，不能进行当前操作,对象{0}存在正在进行中的审批流，不能进行当前操作
paas.udobj.object_approval_flow_failed,对象触发审批流失败,对象触发审批流失败,对象触发审批流失败
paas.udobj.system_exception,系统异常,系统异常,系统异常
paas.udobj.only_insert_ordinary_team_member_role,只能添加普通团队成员角色,只能添加普通团队成员角色,只能添加普通团队成员角色
paas.udobj.personnel_privilege_deploy_error,人员权限配置错误,人员权限配置错误,人员权限配置错误
paas.udobj.data_not_find_or_delete,数据不存在或已删除,数据不存在或已删除,数据不存在或已删除
paas.udobj.operation_failed,"操作{0}失败,原因:{1}","操作{0}失败,原因:{1}","操作{0}失败,原因:{1}"
paas.udobj.failed_data,以下部分数据执行失败:{0},以下部分数据执行失败:{0},以下部分数据执行失败:{0}
paas.udobj.add_role,"您被添加为[{0}][{1}]的“{2}”","您被添加为[{0}][{1}]的“{2}”","您被添加为[{0}][{1}]的“{2}”"
paas.udobj.operater_failed_reason_con_not_add_owner,"操作{0}失败,原因:不能新增负责人","操作{0}失败,原因:不能新增负责人","操作{0}失败,原因:不能新增负责人"
paas.udobj.master_is_invalid_please_recover_master_object,"当前数据所属主对象已作废，请先恢复主对象","当前数据所属主对象已作废，请先恢复主对象","当前数据所属主对象已作废，请先恢复主对象"
paas.udobj.MD_recover_detail_data_time_out,级联恢复从对象数据超时,级联恢复从对象数据超时,级联恢复从对象数据超时
paas.udobj.goods_received_note_goods_received_note_product,入库单-入库单产品
paas.udobj.delivery_note_delivery_note_product,发货单-发货单产品
paas.udobj.operation_param_and_function_param_is_different,操作参数和函数声明参数不一致,操作参数和函数声明参数不一致,操作参数和函数声明参数不一致
paas.udobj.operation_param_and_function_param_is_different_function_param,操作参数和函数声明参数不一致,函数声明参数:{0}操作参数:{1},操作参数和函数声明参数不一致,函数声明参数:{0}操作参数:{1},操作参数和函数声明参数不一致,函数声明参数:{0}操作参数:{1}
paas.udobj.button_is_disable_or_delete,按钮不存在或者已经被删除,按钮不存在或者已经被删除,按钮不存在或者已经被删除
paas.udobj.button_name_duplicate,按钮名称重复,按钮名称重复,按钮名称重复
paas.udobj.function_already_used,该函数已被使用,该函数已被使用,该函数已被使用
paas.udobj.assign,"{0} 给您分配了 {1}：{2}，请跟进。","{0} 给您分配了 {1}：{2}，请跟进。","{0} 给您分配了 {1}：{2}，请跟进。"
paas.udobj.unassign,"{0} 取消了您对 {1}：{2} 的跟进，请知悉。","{0} 取消了您对 {1}：{2} 的跟进，请知悉。","{0} 取消了您对 {1}：{2} 的跟进，请知悉。"
paas.udobj.validity_can_not_is_null,有效期不能为空,有效期不能为空,有效期不能为空
paas.udobj.verify_object_exist_and_save_seccess_end_case_add_calc_formula,请确认在对象已经存在并保存成功后的情况下添加计算公式。,请确认在对象已经存在并保存成功后的情况下添加计算公式。,请确认在对象已经存在并保存成功后的情况下添加计算公式。
paas.udobj.formula_syntax_error,公式语法错误,公式语法错误,公式语法错误
paas.udobj.param_error_object_api_name_is_null,"参数错误, 对象apiName为空","参数错误, 对象apiName为空","参数错误, 对象apiName为空"
paas.udobj.object_data_can_not_is_null,objectData不能为空,objectData不能为空,objectData不能为空
paas.udobj.no_count_field_need_deal,没有统计字段需要处理(对于预设对象，本接口只支持统计自定义对象),没有统计字段需要处理(对于预设对象，本接口只支持统计自定义对象),没有统计字段需要处理(对于预设对象，本接口只支持统计自定义对象)
paas.udobj.object_api_name_can_not_is_null,objectApiName不能为空,objectApiName不能为空,objectApiName不能为空
paas.udobj.field_api_name_can_not_is_null,fieldApiNames不能为空,fieldApiNames不能为空,fieldApiNames不能为空
paas.udobj.no_field_need_deal,没有字段需要处理,没有字段需要处理,没有字段需要处理
paas.udobj.data_not_satisfied_creat_condition,当前数据不满足新建{0}的条件,当前数据不满足新建{0}的条件,当前数据不满足新建{0}的条件
paas.udobj.param_error,参数错误,参数错误,参数错误
paas.udobj.field_depend_related_please_add_depend_field,当前字段存在依赖关系，请先添加该字段的父级依赖字段{0}，在{1}中显示,当前字段存在依赖关系，请先添加该字段的父级依赖字段{0}，在{1}中显示,当前字段存在依赖关系，请先添加该字段的父级依赖字段{0}，在{1}中显示
paas.udobj.leads,线索,線索,Lead
paas.udobj.returned_good_invoice,退货单,退货单,退货单
paas.udobj.payment,回款,回款,回款
paas.udobj.invoice_application,开票申请,开票申请,开票申请
paas.udobj.visiting,拜访,拜访,拜访
paas.udobj.marketing_event,市场活动,市场活动,市场活动
paas.udobj.management,管理,管理,Management
paas.udobj.sale_flow_setting,销售流程设置,销售流程设置,销售流程设置
paas.udobj.high_seas,公海,公海,	Highseas
paas.udobj.sales_clue_pool,线索池,线索池,线索池
paas.udobj.field_management,字段管理,字段管理,字段管理
paas.udobj.product_sort,产品分类,产品分类,产品分类
paas.udobj.function_privilege_management,功能权限管理,功能权限管理,功能权限管理
paas.udobj.data_privilege_management,数据权限管理,数据权限管理,数据权限管理
paas.udobj.rule_setting,规则设置,规则设置,规则设置
paas.udobj.data_board_setting,数据看板设置,数据看板设置,数据看板设置
paas.udobj.sale_report_setting,销售简报设置,销售简报设置,销售简报设置
paas.udobj.approval_flow_manager,审批流程管理,审批流程管理,审批流程管理
paas.udobj.work_flow_manager,工作流程管理,工作流程管理,工作流程管理
paas.udobj.backlog_removal_tool,待办迁移工具,待办迁移工具,待办迁移工具
paas.udobj.custom_object_manager,自定义对象管理,自定义对象管理,自定义对象管理
paas.udobj.conversion_rule_generation_error,转换规则生成出错,转换规则生成出错,转换规则生成出错
paas.udobj.conversion_rule_corresponding_button_generation_error,转换规则对应按钮生成出错,转换规则对应按钮生成出错,转换规则对应按钮生成出错
paas.udobj.button_info_not_exist,按钮信息不存在,按钮信息不存在,按钮信息不存在
paas.udobj.button_name_cannot_empty,按钮名称不能为空,按钮名称不能为空,按钮名称不能为空
paas.udobj.mapping_rule_corresponding_button_does_not_exist,映射规则对应按钮不存在,映射规则对应按钮不存在,映射规则对应按钮不存在
paas.udobj.completed_payment,已完成收款,已完成收款,已完成收款
paas.udobj.collection_amount_is_empty,收款金额为空,收款金额为空,收款金额为空
paas.udobj.collection_amount_is_illegal,收款金额不合法,收款金额不合法,收款金额不合法
paas.udobj.data_life_status_is_not_in_effect,数据生命状态为未生效,数据生命状态为未生效,数据生命状态为未生效
paas.udobj.data_has_been_locked,数据已被锁定,数据已被锁定,数据已被锁定
paas.udobj.data_is_not_locked,数据未被锁定,数据未被锁定,数据未被锁定
paas.udobj.record_type_not_exist,该业务类型不存在,该业务类型不存在,该业务类型不存在
paas.udobj.object_describe_is_empty,对象描述为空,对象描述为空,对象描述为空
paas.udobj.record_type_is_empty,业务类型为空,业务类型为空,业务类型为空
paas.udobj.global_variable_unmodify,系统全局变量不可以修改,系统全局变量不可以修改,系统全局变量不可以修改
paas.udobj.global_variable_not_exist_or_deleted,该全局变量不存在或者已删除,该全局变量不存在或者已删除,该全局变量不存在或者已删除
paas.udobj.current_time,当前时间,当前时间,当前时间
paas.udobj.current_date,当前日期,当前日期,当前日期
paas.udobj.current_date_time,当前日期时间,当前日期时间,当前日期时间
paas.udobj.system_creation,系统创建,系统创建,系统创建
paas.udobj.import_template,导入模版,导入模版,导入模版
paas.udobj.button_object,按钮对象,按钮对象,按钮对象
paas.udobj.custom_button,自定义按钮,自定义按钮,自定义按钮
paas.udobj.button_para_cannot_exceed,按钮参数不可超过个,按钮参数不可超过个,按钮参数不可超过个
paas.udobj.copy,复制,复制,复制
paas.udobj.status,状态,状态,状态
paas.udobj.remark,备注,备注,备注
paas.udobj.top_info,顶部信息,顶部信息,顶部信息
paas.udobj.related,相关,相关,相关
paas.udobj.summary,摘要,摘要,Summary
paas.udobj.service_record,服务记录,服务记录,服务记录
paas.udobj.process_list,流程列表,流程列表,流程列表
paas.udobj.approvalflow,审批流程,审批流程,审批流程
paas.udobj.annex,附件,附件,Attachment
paas.udobj.image,图片,图片,图片
paas.udobj.charge,费用,费用,费用
paas.udobj.mail,邮件,邮件,邮件
paas.udobj.name_position,姓名职位,姓名职位,姓名职位
paas.udobj.unsupported_count_type,不支持的统计类型,不支持的统计类型,不支持的统计类型
paas.udobj.recalculation_condition_is_illegal,重计条件不合法,重计条件不合法,重计条件不合法
paas.udobj.prefix_is_too_long,前缀过长,前缀过长,前缀过长
paas.udobj.suffix_is_too_long,后缀过长,后缀过长,后缀过长
paas.udobj.autonumber_prefix_length_check_failed,自动编号类型前缀长度校验失败,自动编号类型前缀长度校验失败,自动编号类型前缀长度校验失败
paas.udobj.autonumber_suffix_length_check_failed,自动编号类型后缀长度校验失败,自动编号类型后缀长度校验失败,自动编号类型后缀长度校验失败
paas.udobj.autonumber_serial_number_length_check_failed,自动编号类型序列号长度校验失败,自动编号类型序列号长度校验失败,自动编号类型序列号长度校验失败
paas.udobj.autonumber_condition_check_failed,自动编号类型重计条件校验失败,自动编号类型重计条件校验失败,自动编号类型重计条件校验失败
paas.udobj.upload_file_failed,上传文件失败,上传文件失败,上传文件失败
paas.udobj.save_file_failed,保存文件失败,保存文件失败,保存文件失败
paas.udobj.save_image_failed,保存图片失败,保存图片失败,保存图片失败
paas.udobj.default_layout_cannot_be_deleted,默认布局不能删除,默认布局不能删除,默认布局不能删除
paas.udobj.of_not_empy,的不能为空,的不能为空,的不能为空
paas.udobj.layout_object,布局对象,布局对象,布局对象
paas.udobj.default_layout_not_exist,默认布局不存在,默认布局不存在,默认布局不存在
paas.udobj.other_info,其他信息,其他信息,其他信息
paas.udobj.data_permission_without_this_operation,无此操作的数据权限,无此操作的数据权限,无此操作的数据权限
paas.udobj.unsatisfied_condition,不满足范围条件,不满足范围条件,不满足范围条件
paas.udobj.export_data,导出数据,导出数据,导出数据
paas.udobj.basic_info,基本信息,基本信息,基本信息
paas.udobj.list_specify,{0}列表,{0}列表,{0}列表
paas.udobj.object,对象,对象,对象
paas.udobj.object_specify,对象{0},对象{0},对象{0}
paas.udobj.field_object,字段 {0}，对象 {1},字段 {0}，对象 {1},字段 {0}，对象 {1}
paas.udobj.data_export_recommendations,目前只支持一次性导出万条数据超出建议您对数据做过滤后分批导出,目前只支持一次性导出万条数据超出建议您对数据做过滤后分批导出,目前只支持一次性导出万条数据超出建议您对数据做过滤后分批导出
paas.udobj.count_field,统计字段,统计字段,统计字段
paas.udobj.data,数据,数据,数据
paas.udobj.execute_result_fail,执行失败原因,执行失败原因,执行失败原因
paas.udobj.execute_result,执行成功{0}条，失败{1}条。,执行成功{0}条，失败{1}条。,执行成功{0}条，失败{1}条。
paas.udobj.execute_result_detail,失败明细如下,失败明细如下,失败明细如下
paas.udobj.delete_object_fail,删除对象失败原因,删除对象失败原因,删除对象失败原因
paas.udobj.relieve_associated_objects,解除关联对象,解除关联对象,解除关联对象
paas.udobj.operation_fail_reasion,操作失败可能原因如下:1.{0}对象上已不存在与{1}对象的相关对象related_list为{2}的字段关联关系;2.您并没有{1}对象的相关对象related_list为{2}字段的字段权限。,操作失败可能原因如下:1.{0}对象上已不存在与{1}对象的相关对象related_list为{2}的字段关联关系;2.您并没有{1}对象的相关对象related_list为{2}字段的字段权限。,操作失败可能原因如下:1.{0}对象上已不存在与{1}对象的相关对象related_list为{2}的字段关联关系;2.您并没有{1}对象的相关对象related_list为{2}字段的字段权限。
paas.udobj.unable_to_remove_association,查找字段为必填所以无法解除关联,查找字段为必填所以无法解除关联,查找字段为必填所以无法解除关联
paas.udobj.unsupport_invoking,只有联系人支持此调用,只有联系人支持此调用,只有联系人支持此调用
paas.udobj.request_param_is_null,请求参数为空,请求参数为空,请求参数为空
paas.udobj.sort_number_cannot_be_null,排序号不能为空,排序号不能为空,排序号不能为空
paas.udobj.is_prefabrication_menu,是预制CRM菜单,是预制CRM菜单,是预制CRM菜单
paas.udobj.whether_system_menu,是否系统CRM菜单,是否系统CRM菜单,是否系统CRM菜单
paas.udobj.enabled_state,启用状态,启用状态,启用状态
paas.udobj.belong_application_module,所属应用模块,所属应用模块,所属应用模块
paas.udobj.crm_home_page_module,首页模块,首页模块,首页模块
paas.udobj.end_using,禁用,禁用,禁用
paas.udobj.sort_number,排序号,排序号,排序号
paas.udobj.presupposition_objects,预设对象,预设对象,预设对象
paas.udobj.is_not_object,非对象,非对象 ,非对象
paas.udobj.icon_index,图标索引,图标索引,图标索引
paas.udobj.icon_path,图标路径,图标路径,图标路径
paas.udobj.define_type,定义类型,定义类型,定义类型
paas.udobj.whether_hidden,是否隐藏,是否隐藏,是否隐藏
paas.udobj.parent_id,父id,父id,父id
paas.udobj.type,类型,	類型,Type
paas.udobj.show_name,显示名称,显示名称,显示名称
paas.udobj.grouping,分组,分組,Group
paas.udobj.menu,菜单,菜单,菜单
paas.udobj.resource_id,资源id,资源id,资源id
paas.udobj.resource_type,资源类型,资源类型,资源类型
paas.udobj.display_name,显示名称,显示名称,显示名称
paas.udobj.menu_id,菜单项id,菜单项id,菜单项id
paas.udobj.business_type,业务类型,业务类型,业务类型
paas.udobj.sort,排序,排序,Sort
paas.udobj.associated_objects,关联的对象,关联的对象,关联的对象
paas.udobj.tenant_id,租户id,租户id,租户id
paas.udobj.search_data_occur_exception,查询数据发生异常,查询数据发生异常,查询数据发生异常
paas.udobj.object_not_exist,对象{0}不存在,对象{0}不存在,对象{0}不存在
paas.udobj.object_cannot_be_find,对象未找到,对象未找到,对象未找到
paas.udobj.object_cannot_be_find2,对象未找到{0},对象未找到{0},对象未找到{0}
paas.udobj.field_already_deleted,字段已被删除,字段已被删除,字段已被删除
paas.udobj.object_in_unexist_or_delete,对象{0}不存在或者已删除,对象{0}不存在或者已删除,对象{0}不存在或者已删除
paas.udobj.slave_objects_be_peak_value,"{0}对象已经配置了{1}个从对象,最多仅支持5个,请重新配置","{0}对象已经配置了{1}个从对象,最多仅支持5个,请重新配置","{0}对象已经配置了{1}个从对象,最多仅支持5个,请重新配置"
paas.udobj.cannot_add_masterdetail_field,当前对象已做为主对象被关联，不可再添加主从关系字段,当前对象已做为主对象被关联，不可再添加主从关系字段,当前对象已做为主对象被关联，不可再添加主从关系字段
paas.udobj.cannot_add_requiredl_field,预置对象{0}不能增加必填字段,预置对象{0}不能增加必填字段,预置对象{0}不能增加必填字段
paas.udobj.field_cannot_hidden_in_layout,字段{0}不能在布局{1}中隐藏,字段{0}不能在布局{1}中隐藏,字段{0}不能在布局{1}中隐藏
paas.udobj.default_business_type,预设业务类型,预设业务类型,预设业务类型
paas.udobj.custom_field_beyond_max_limit,自定义字段数量超出最大限制：{0},自定义字段数量超出最大限制：{0},自定义字段数量超出最大限制：{0}
paas.udobj.image_field_beyond_max_limit,图片类型的自定义字段超出最大限制：{0},图片类型的自定义字段超出最大限制：{0},图片类型的自定义字段超出最大限制：{0}
paas.udobj.enclosure_field_beyond_max_limit,附件类型的自定义字段超出最大限制：{0},附件类型的自定义字段超出最大限制：{0},附件类型的自定义字段超出最大限制
paas.udobj.formula_field_beyond_max_limit,计算型类型的自定义字段超出最大限制：{0},计算型类型的自定义字段超出最大限制：{0},计算型类型的自定义字段超出最大限制
paas.udobj.relation_field_beyond_max_limit,关联类型的自定义字段超出最大限制：{0},关联类型的自定义字段超出最大限制：{0},关联类型的自定义字段超出最大限制
paas.udobj.increasing_coding_field_beyond_max_limit,自增编码类型的自定义字段超出最大限制：{0},自增编码类型的自定义字段超出最大限制：{0},自增编码类型的自定义字段超出最大限制
paas.udobj.layout_already_exist,layout的apiName:{0}已经存在,layout的apiName:{0}已经存在,layout的apiName:{0}已经存在
paas.udobj.create_user,创建人,创建人,创建人
paas.udobj.last_modify_user,最后修改人,最后修改人,最后修改人
paas.udobj.create_time,创建时间,创建时间,创建时间
paas.udobj.last_modify_time,最后修改时间,最后修改时间,最后修改时间
paas.udobj.whether_invalid,是否作废,是否作废,是否作废
paas.udobj.apiname_length_beyond_max_limit,{0}名称超过个字符,{0}名称超过个字符,{0}名称超过个字符
paas.udobj.name_already_exist,{0}名称已经存在,{0}名称已经存在,{0}名称已经存在
paas.udobj.associated_objects_unexist_or_deleted,关联的对象不存在或已被删除,关联的对象不存在或已被删除,关联的对象不存在或已被删除
paas.udobj.filter_condition_quote_field_unexist_or_deleted,关联的对象的过滤条件中引用的字段不存在或已被删除,关联的对象的过滤条件中引用的字段不存在或已被删除,关联的对象的过滤条件中引用的字段不存在或已被删除
paas.udobj.filter_condition_single_field_unexist_or_deleted,关联的对象的过滤条件中引用的单选选项已删除,关联的对象的过滤条件中引用的单选选项已删除,关联的对象的过滤条件中引用的单选选项已删除
paas.udobj.filter_condition_multi_field_unexist_or_deleted,关联的对象的过滤条件中引用的多选选项已删除,关联的对象的过滤条件中引用的多选选项已删除,关联的对象的过滤条件中引用的多选选项已删除
paas.udobj.filed_modify_validate_exception,您已配置【{0}】，系统暂不支持【{1}】（->代表引用）,您已配置【{0}】，系统暂不支持【{1}】（->代表引用）,您已配置【{0}】，系统暂不支持【{1}】（->代表引用）
paas.udobj.field_exist_ring_forming,对象{0}的字段{1}存在成环设置请重新设置公式,对象{0}的字段{1}存在成环设置请重新设置公式,对象{0}的字段{1}存在成环设置请重新设置公式
paas.udobj.field,字段,字段,字段
paas.udobj.field_specify,字段{0},字段{0},字段{0}
paas.udobj.validation_rule,验证规则{0},验证规则{0},验证规则{0}
paas.udobj.formula_field_exist_ring_forming,当前设置的计算公式与字段{0}存在成环设置请重新设置公式,当前设置的计算公式与字段{0}存在成环设置请重新设置公式,当前设置的计算公式与字段{0}存在成环设置请重新设置公式
paas.udobj.object_field_ring_forming,对象[{0}]字段[{1}]存在成环设置请重新设置公式,对象[{0}]字段[{1}]存在成环设置请重新设置公式,对象[{0}]字段[{1}]存在成环设置请重新设置公式
paas.udobj.quote_global_variable_disabled_or_deleted,{0}引用的全局变量:{1}已被禁用删除,{0}引用的全局变量:{1}已被禁用删除,{0}引用的全局变量:{1}已被禁用删除
paas.udobj.quote_objecte_disabl,{0}引用的对象{1}已被禁用/删除,{0}引用的对象{1}已被禁用/删除,{0}引用的对象{1}已被禁用/删除
paas.udobj.quote_field_isnot_role_type,{0}引用的字段{1}不是LookUp、MD或人员类型
paas.udobj.quote_form_variable_disabled_or_deleted,{0}引用的表单变量:{1}已被禁用删除,{0}引用的表单变量:{1}已被禁用删除,{0}引用的表单变量:{1}已被禁用删除
paas.udobj.unsupport_comparison_operator,不支持的比较运算符：{0},不支持的比较运算符：{0},不支持的比较运算符：{0}
paas.udobj.target_object_unexist_or_disabled,目的对象不存在或已禁用不能启用规则,目的对象不存在或已禁用不能启用规则,目的对象不存在或已禁用不能启用规则
paas.udobj.source_object_unexist_or_disabled,源对象不存在或已禁用不能启用规则,源对象不存在或已禁用不能启用规则,源对象不存在或已禁用不能启用规则
paas.udobj.delete_mapping_rule_exception,删除从对象映射规则出错,删除从对象映射规则出错,删除从对象映射规则出错
paas.udobj.object_desc_unexist,对象描述不存在,对象描述不存在,对象描述不存在
paas.udobj.mapping_rule_disabled_or_deleted,该验证规则不存在或者已被删除,该验证规则不存在或者已被删除,该验证规则不存在或者已被删除
paas.udobj.layout_rule_ring_forming,布局规则设置成环,布局规则设置成环,布局规则设置成环
paas.udobj.field_deleted_in_layout,布局规则[{0}]的触发操作中的字段已被删除,布局规则[{0}]的触发操作中的字段已被删除,布局规则[{0}]的触发操作中的字段已被删除
paas.udobj.field_invalid_in_layout,布局规则的触发操作中的字段已被作废,布局规则的触发操作中的字段已被作废,布局规则的触发操作中的字段已被作废
paas.udobj.need_deal_with,待办,待办,待办
paas.udobj.have_done,已办,已办,已办
paas.udobj.unsolved_work_order,未解决的工单,未解决的工单,未解决的工单
paas.udobj.urgent_work_order,紧急的工单,紧急的工单,紧急的工单
paas.udobj.closed_work_order,已关闭的工单,已关闭的工单,已关闭的工单
paas.udobj.already_terminated,已终止,已终止,已终止
paas.udobj.exception,异常,异常,异常
paas.udobj.under_way,进行中,进行中,进行中
paas.udobj.completed,已完成,已完成,已完成
paas.udobj.terminated,终止,终止,终止
paas.udobj.approval_flow,审批流,审批流,审批流
paas.udobj.business_flow,业务流,业务流,业务流
paas.udobj.work_flow,工作流,工作流,工作流
paas.udobj.data_permission_power,数据权限计算进度,数据权限计算进度,数据权限计算进度
paas.udobj.presupposition_field_personnel_power,外勤人员预设了使用外勤功能的权限,外勤人员预设了使用外勤功能的权限,外勤人员预设了使用外勤功能的权限
paas.udobj.unload,上传,上传,上传
paas.udobj.no_permission_in_operate,无操作的功能权限,无操作的功能权限,无操作的功能权限
paas.udobj.init_permission_fail,初始化功能权限失败,初始化功能权限失败,初始化功能权限失败
paas.udobj.get_function_permission_fail,获取功能权限失败,获取功能权限失败,获取功能权限失败
paas.udobj.get_role_list_fail,获取角色列表失败,获取角色列表失败,获取角色列表失败
paas.udobj.batch_delete_permission_fail,批量删除功能权限失败,批量删除功能权限失败,批量删除功能权限失败
paas.udobj.init_permission_fail_reason,初始化功能权限失败原因：{0},初始化功能权限失败原因：{0},初始化功能权限失败原因：{0}
paas.udobj.batch_add_role_permission_fail_reason,批量角色增加权限失败原因：{0},批量角色增加权限失败原因：{0},批量角色增加权限失败原因：{0}
paas.udobj.batch_add_role_permission_fail_reason,批量角色增加权限失败原因：{0},批量角色增加权限失败原因：{0},批量角色增加权限失败原因：{0}
paas.udobj.update_role_permission_fail_reason,更新角色的权限失败原因：{0},更新角色的权限失败原因：{0},更新角色的权限失败原因：{0}
paas.udobj.get_role_list_fail_in_permission,获取具有功能权限的角色列表失败,获取具有功能权限的角色列表失败,获取具有功能权限的角色列表失败
paas.udobj.delete_permission_fail,删除权限失败,删除权限失败,删除权限失败
paas.udobj.create_role_fail_reason,创建角色失败原因：{0},创建角色失败原因：{0},创建角色失败原因：{0}
paas.udobj.get_out_role_fail_reason,获取外部角色失败原因：{0},获取外部角色失败原因：{0},获取外部角色失败原因：{0}
paas.udobj.get_role_info_list_fail,获取角色信息列表失败原因,获取角色信息列表失败原因,获取角色信息列表失败原因
paas.udobj.update_role_list_permission_fail_reason,更新权限角色列表失败,更新权限角色列表失败,更新权限角色列表失败
paas.udobj.update_custom_button_fail,更新自定义按钮名称失败,更新自定义按钮名称失败,更新自定义按钮名称失败
paas.udobj.base_data_permission_service,基础数据权限服务,基础数据权限服务,基础数据权限服务
paas.udobj.channel_manager,渠道经理,渠道经理,渠道经理
paas.udobj.channel_manager_company,渠道经理专门管理合作伙伴的人员,渠道经理专门管理合作伙伴的人员,渠道经理专门管理合作伙伴的人员
paas.udobj.agents,代理商人员,代理商人员,代理商人员
paas.udobj.down_prm_agents,下游代理商使用PRM的人员,下游代理商使用PRM的人员,下游代理商使用PRM的人员
paas.udobj.customer_transaction_operator,客户成交操作员,客户成交操作员,客户成交操作员
paas.udobj.member_manager,会员管理员
paas.udobj.member_manager_description,会员管理员
paas.udobj.distribution_customer_transaction_permission,拥有更改客户成交状态的权限请谨慎分配,拥有更改客户成交状态的权限请谨慎分配,拥有更改客户成交状态的权限请谨慎分配
paas.udobj.name,姓名,姓名,	Name
paas.udobj.main_department,主属部门,主属部门,主属部门
paas.udobj.position,职位,职位,职位
paas.udobj.master_role,主角色,主角色,主角色
paas.udobj.staff_role_distribution,员工角色分配,员工角色分配,员工角色分配
paas.udobj.staff_role,员工角色,员工角色,员工角色
paas.udobj.business_process_instance,业务流程实例,业务流程实例,业务流程实例
paas.udobj.approval_instance,审批流实例,审批流实例
paas.udobj.cancelled_identity,您被取消{0}{1}的“{2}｛角色｝”身份,,
paas.udobj.added_identity,您被添加为{0}{1}的“{2}｛角色｝”,,
pass.udobj.expression,公式,公式,公式
paas.udobj.add_sale,添加相关团队,添加相关团队,添加相关团队
paas.udobj.create_or_update_duplicate_rule,新建或更新查重规则,新建或更新查重规则,新建或更新查重规则
paas.udobj.update_duplicate_rule,更新查重规则,更新查重规则,更新查重规则
paas.udobj.yes,是,是,是
paas.udobj.no,否,否,否
pass.udobj.oper_fail_maybe_reason,"操作失败!可能原因如下:1.{0}对象上已不存在与{1}对象的相关对象related_list为{2}的字段关联关系  2.您并没有{3}对象的相关对象related_list为{4}字段的字段权限","操作失败!可能原因如下:1.{0}对象上已不存在与{1}对象的相关对象related_list为{2}的字段关联关系  2.您并没有{3}对象的相关对象related_list为{4}字段的字段权限","操作失败!可能原因如下:1.{0}对象上已不存在与{1}对象的相关对象related_list为{2}的字段关联关系  2.您并没有{3}对象的相关对象related_list为{4}字段的字段权"
paas.udobj.constant.owner,负责人,负责人,负责人
paas.udobj.constant.normal_staff,普通成员,普通成员,普通成员
paas.udobj.related_team,相关团队,相关团队,相关团队
paas.udobj.data_list_page,数据列表页,数据列表页,数据列表页
paas.udobj.related_list_page,相关对象列表页,相关对象列表页,相关对象列表页
paas.udobj.detail_cannot_set_validate_rule,从对象验证规则不可配置阻断保存动作,从对象验证规则不可配置阻断保存动作,从对象验证规则不可配置阻断保存动作
paas.udobj.button_cannot_create,自定义按钮个数已达上限，无法新建,自定义按钮个数已达上限，无法新建,自定义按钮个数已达上限，无法新建
paas.udobj.detail_button_use_page,对象详情页已设置10个按钮,对象详情页已设置10个按钮，对象详情页已设置10个按钮
paas.udobj.list_related_button_use_page,对象列表页和相关对象列表页已设置3个按钮,对象列表页和相关对象列表页已设置3个按钮,对象列表页和相关对象列表页已设置3个按钮
paas.udobj.list_button_use_page,对象列表页已设置3个按钮,对象列表页已设置3个按钮,对象列表页已设置3个按钮
paas.udobj.related_button_use_page,相关列表页按钮不能超过3个,相关列表页按钮不能超过3个,相关列表页按钮不能超过3个
paas.udobj.cannot_create,不可继续添加,不可继续添加,不可继续添加
paas.udobj.duplicate_search_rule_active,已有查重规则正在生效中，不允许设置,已有查重规则正在生效中，不允许设置,已有查重规则正在生效中，不允许设置
paas.udobj.stage_change,阶段变更,阶段变更,阶段变更
paas.udobj.field_in_calculate,字段:{0} 正在执行计算，请稍后再编辑字段。,字段:{0} 正在执行计算，请稍后再编辑字段。,字段:{0} 正在执行计算，请稍后再编辑字段。
paas.udobj.action.complete_settlement,完成结算,完成结算,完成结算
paas.udobj.action.bulk_invalid,作废,作废,作废
paas.udobj.image_exceed_max_count,导入图片数量不能超过{0}张。
paas.udobj.attach_exceed_max_count,导入附件数量不能超过{0}个。
paas.udobj.duplicate_effective,查重生效,查重生效,查重生效
paas.udobj.sale_record,销售记录,销售记录,销售记录
paas.udobj.unexist_or_deleted,临时权限配置已被删除或不存在,临时权限配置已被删除或不存在,临时权限配置已被删除或不存在
paas.udobj.enabled_not_be_deleted,启用状态的临时权限配置不能被删除,启用状态的临时权限配置不能被删除,启用状态的临时权限配置不能被删除
paas.udobj.duplicate_success,【{0}】的新查重设置生效,【{0}】的新查重设置生效,【{0}】的新查重设置生效
paas.udobj.action.view,查看,查看,View
paas.udobj.action.contact_admin,企信联系管理员,企信联系管理员,企信联系管理员
paas.udobj.action.contact_owner,企信联系负责人,企信联系负责人,企信联系负责人
paas.udobj.import_file_not_exist,导入的{0}不存在。,导入的{0}不存在。,导入的{0}不存在。
paas.udobj.import_file_partial_not_exist,部分文件不存在: ,部分文件不存在: ,部分文件不存在:
paas.udobj.image_max_size,单个图片不得超过20M。
paas.udobj.attach_max_size,单个文件不得超过100M。
paas.udobj.no_permission_view,无权限查看！
paas.udobj.is_disabled,{0}(已停用)
paas.udobj.interface_upgrade,接口升级，建议到【我的】->【设置】中升级最新版本,接口升级，建议到【我的】->【设置】中升级最新版本,接口升级，建议到【我的】->【设置】中升级最新版本
paas.udobj.AddTrade,新建订单
paas.udobj.approval,审批
paas.udobj.delay,延期
paas.udobj.add_server,新建服务记录
paas.udobj.check_record,查看所有类型的销售记录
paas.udobj.next_phase_fill_feedback,进入下一阶段/填写反馈信息
paas.udobj.add_2,添加
paas.udobj.modify_custom_name,修改客户名称
paas.udobj.delete_combine_sale,删除联合跟进人&售后人员
paas.udobj.combine,合并
paas.udobj.modify,修改,修改,Modify
paas.udobj.add_combine_saler,添加联合跟进人,添加聯合跟進人,Add sales assistant
paas.udobj.change_auditor,更换审核人,更換審核人,Replace Approver
paas.udobj.share,共享,共用,Share
paas.udobj.dis_connect,解除关联客户,解除關聯客戶,Disassociated Customer
paas.udopaas.udobj.repetition_checkerbj.recover_modify_log,恢复修改记录,恢復修改記錄,Restore Modify Log
paas.udobj.modify_config,修改配置,修改配置,Modify Configuration
paas.udobj.delete_config,删除配置,刪除配置,Delete configuration
paas.udobj.submit,提交,提交,Submit
paas.udobj.change_owner,变更负责人,變更負責人,Change Owner
paas.udobj.delete_combine_sale_only,删除联合跟进人,刪除聯合跟進人,Delete joint followers
paas.udobj.add_after_sales,添加售后人员,添加售後人員,Add After-sales
paas.udobj.move_next,进入下一步,進入下一步,Move Next
paas.udobj.start_after_sale,是否开启售后,	是否開啟售後,Whether to open the after-sales
paas.udobj.move_forward,向前变更阶段,	向前變更階段,Forward change phase
paas.udobj.change_stage,阶段变更,	階段變更,Stage Change
paas.udobj.finish_visit,完成拜访,	完成拜訪,Completed Visit
paas.udobj.set_assist_visit,设置协访人员,	設置協訪人員,Setting 	Visit coordinators
paas.udobj.choose_location,选择定位,	選擇定位,Select locating
paas.udobj.reuse_route,路线重用,	路線重用,Route Reuse
paas.udobj.visit_summary,拜访统计,	拜訪統計,Visit Statistics
paas.udobj.resubmit,重新提交,	重新提交,	Re-submit
paas.udobj.import_water_print_error,"该图片字段配置了水印,不支持进行导入"
paas.udobj.data_id,唯一性ID
pass.udobj.import_verify_duplicate,"Excel中有名称重复的列。"
paas.udobj.related_object_data_duplicated,字段{0}关联数据主属性重复，请使用数据ID方式导入。
paas.udobj.data_id_must_fill,字段{0}的数据ID没有填写
pass.udobj.import_name_duplicate,主属性字段{0}的值{1}具有重复，无法进行匹配导入。
paas.udobj.related_mark,关联标识