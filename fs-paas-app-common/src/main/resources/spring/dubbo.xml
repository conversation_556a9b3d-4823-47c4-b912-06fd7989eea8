<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <!-- dubbo config -->
    <dubbo:application name="fs-paas-appframework"/>

    <dubbo:registry address="${dubbo.registry.address}"/>

    <dubbo:consumer retries="0" timeout="5000" lazy="true" check="false" filter="tracerpc"/>

    <!--通讯录-->
<!--    <dubbo:reference id="employeeProviderService" interface="com.facishare.organization.api.service.EmployeeProviderService" protocol="dubbo" version="5.7"/>-->
<!--    <dubbo:reference id="departmentProviderService" interface="com.facishare.organization.api.service.DepartmentProviderService" protocol="dubbo" version="5.7"/>-->


    <!--  文件图片打包接口 -->
    <dubbo:reference id="filePackedService" interface="com.facishare.warehouse.api.dubbo.FilePackedService"
                     version="1.0" retries="0" timeout="15000" lazy="true" check="false"/>
</beans>