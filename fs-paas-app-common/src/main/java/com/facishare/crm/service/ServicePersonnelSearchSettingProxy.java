package com.facishare.crm.service;

import com.facishare.crm.service.dto.ServicePersonnelSearchSettingModel;
import com.facishare.rest.core.annotation.*;
/**
 * Created by luxin on 2018/4/10.
 */
@RestResource(value = "CRM_SFA", desc = "服务人员查询设置信息", contentType = "application/json")
public interface ServicePersonnelSearchSettingProxy {
    @GET(value = "/crm/basicsetting/getconfigvalue/9", desc = "获取服务人员查询设置信息")
    ServicePersonnelSearchSettingModel.Result getSearchSettingInfo(@HeaderParam("x-fs-ei") String tenantId, @HeaderParam("x-fs-userInfo") String userId);
}
