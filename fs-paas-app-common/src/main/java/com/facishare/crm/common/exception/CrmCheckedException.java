package com.facishare.crm.common.exception;

/**
 * Created by yusb on 12/23/16.
 */
public class CrmCheckedException extends Exception implements CrmErrorInter {
  private CRMErrorCode crmErrorCode;
  private Integer errorCode;


  public CRMErrorCode getErrorCode() {
    return crmErrorCode;
  }

  public Integer getIntErrorCode(){
    return errorCode;
  }



  public CrmCheckedException(CRMErrorCode crmErrorCode, String message, Throwable cause) {
    super(message,cause);
    this.crmErrorCode=crmErrorCode;
  }
  public CrmCheckedException(CRMErrorCode crmErrorCode, String message ) {
    super(message);
    this.crmErrorCode=crmErrorCode;
  }
  public CrmCheckedException(CRMErrorCode crmErrorCode, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
  }

  public CrmCheckedException(Integer errorCode, String message){
    super(message);
    this.errorCode = errorCode;
  }

}
