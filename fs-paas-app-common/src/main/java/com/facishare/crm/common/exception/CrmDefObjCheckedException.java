package com.facishare.crm.common.exception;

/**
 * Created by yusb on 17/2/24.
 */
public class CrmDefObjCheckedException extends CrmCheckedException {

  public CrmDefObjCheckedException(CRMErrorCode crmErrorCode, String message, Throwable cause) {
    super(crmErrorCode, message, cause);
  }

  public CrmDefObjCheckedException(CRMErrorCode crmErrorCode, String message) {
    super(crmErrorCode, message);
  }

  public CrmDefObjCheckedException(CRMErrorCode crmErrorCode, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
    super(crmErrorCode, message, cause, enableSuppression, writableStackTrace);
  }
}
