package com.facishare.crm.userdefobj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.metadata.api.GroupField;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.util.Constant;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by yusb on 17/3/2.
 */
@Slf4j
public class DefObjConstants {

    /**
     * appID,packageName
     */
    public static final String PACKAGE_NAME_CRM = "CRM";


    public static final String BASE_FIELD_SECTION_API_NAME = "base_field_section__c";

    public static final String AREA_LOCATION_FIELD_SECTION_API_NAME = "area_location";

    /**
     * 功能操作的常数
     */

    //超级用户的userId,不过滤任何权限的
    public static final String SUPER_PRIVILEGE_USER_ID = "-10000";

    //定义元数据action的几种常量
    public static final String ACTION_PRIVILEGE_SPLIT = "||";

    public static final String ALL_FIELD_TYPE = "AllFieldType";

    //数据权限的常量：1 只读 2 读写
    public enum DATA_PRIVILEGE_PERMISSION {
        NO_PERMISSION("0", I18NKey.CONSTANT_NO_PERMISSION),
        READONLY("1", I18NKey.constant_read_only),
        READANDWRITE("2", I18NKey.constant_read_write);
        private String value;
        private String label;

        DATA_PRIVILEGE_PERMISSION(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }

        public String getLabel() {
            return I18N.text(label);
        }

        public String getI18NLabel() {
            return label;
        }

        public static String getLabelByValue(String value) {
            for (DATA_PRIVILEGE_PERMISSION permission : DATA_PRIVILEGE_PERMISSION.values()) {
                if (StringUtils.equals(value, permission.value)) {
                    return permission.getLabel();
                }
            }
            return "";
        }

        public static DATA_PRIVILEGE_PERMISSION getEnumByValue(String value) {
            if (value == null) return NO_PERMISSION;
            for (DATA_PRIVILEGE_PERMISSION permission : DATA_PRIVILEGE_PERMISSION.values()) {
                if (StringUtils.equals(value, permission.value)) {
                    return permission;
                }
            }
            return NO_PERMISSION;
        }

        public static boolean hasPermission(String value) {
            return !Objects.equals(getEnumByValue(value), NO_PERMISSION);
        }
    }


    public enum DATA_PRIVILEGE_OBJECTDATA_PERMISSION {
        NO_PERMISSION("0", I18NKey.CONSTANT_NO_PERMISSION, "无权限"),// ignoreI18n
        PRIVATE("1", I18NKey.constant_private, "私有"),// ignoreI18n
        PUBLIC_READONLY("2", I18NKey.constant_public_read, "公开只读"),// ignoreI18n
        PUBLIC_READ_WRITE_DELETE("3", I18NKey.constant_public_read_write, "公开读写删"),// ignoreI18n
        ;
        private String value;
        private String labelKey;
        private String defaultLabel;

        DATA_PRIVILEGE_OBJECTDATA_PERMISSION(String value, String labelKey, String defaultLabel) {
            this.value = value;
            this.labelKey = labelKey;
            this.defaultLabel = defaultLabel;
        }

        public String getValue() {
            return value;
        }

        public String getLabel() {
            return I18NExt.getOrDefault(labelKey, defaultLabel);
        }

        private static Map<String, DATA_PRIVILEGE_OBJECTDATA_PERMISSION> permissionMap;

        static {
            permissionMap = Stream.of(values()).collect(Collectors.toMap(DATA_PRIVILEGE_OBJECTDATA_PERMISSION::getValue, x -> x));
        }

        public static DATA_PRIVILEGE_OBJECTDATA_PERMISSION getByValue(String value) {
            return permissionMap.getOrDefault(value, NO_PERMISSION);
        }
    }

    /**
     * 数据权限相关枚举值
     */

    //数据权限角色的常量：根据.net侧定义：负责人=1;普通成员=4;联合跟进=2;服务=3
    public enum DATA_PRIVILEGE_ROLE_TYPE {
        NOT_EXIST("0", I18NKey.constant_not_exist),
        OWNER("1", I18NKey.constant_owner),
        FOLLOWER("2", I18NKey.constant_follower),
        SERVICE_STAFF("3", I18NKey.constant_service_staff),
        NORMAL_STAFF("4", I18NKey.constant_normal_staff);
        private String value;
        private String label;

        DATA_PRIVILEGE_ROLE_TYPE(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return value;
        }

        public String getLabel() {
            return I18N.text(label);
        }

        public static String getLabelByValue(String value) {
            for (DATA_PRIVILEGE_ROLE_TYPE role : DATA_PRIVILEGE_ROLE_TYPE.values()) {
                if (StringUtils.equals(value, role.value)) {
                    return role.getLabel();
                }
            }
            return "";
        }

        public String getI18NLabel() {
            return label;
        }

        public static String getI18NLabelByValue(String value) {
            for (DATA_PRIVILEGE_ROLE_TYPE role : DATA_PRIVILEGE_ROLE_TYPE.values()) {
                if (StringUtils.equals(value, role.value)) {
                    return role.getI18NLabel();
                }
            }
            return "";
        }


        public static DATA_PRIVILEGE_ROLE_TYPE getEnumByValue(String value) {
            for (DATA_PRIVILEGE_ROLE_TYPE role_type : DATA_PRIVILEGE_ROLE_TYPE.values()) {
                if (StringUtils.equals(value, role_type.value)) {
                    return role_type;
                }
            }
            return null;
        }
    }

    static {

    }

    //定义一个特殊含义的对象名称:
    public static final String UDOBJ = "UDObj";

    //用于匹配所有的对象，包括所有自定义对象和所有预制对象
    public static final String ALLOBJ = "*Obj";

    //系统字段、不可见字段、摘要字段的map集(key:describeApiName,value:fieldApiNames)、若是自定义对象:则apiName为变量:UDOBJ
    public static Map<String, Set<String>> invisibleFieldTypeList4AddOrUpdateLayoutFormObjectMap = Maps.newHashMap();
    public static Map<String, Set<String>> systemFieldListFormObjectMap = Maps.newHashMap();
    public static Map<String, Set<String>> invisibleFieldListFormObjectMap = Maps.newHashMap();
    public static Map<String, Set<String>> briefFieldListFormObjectMap = Maps.newHashMap();
    public static Map<String, Set<String>> changeToEmployeeRenderTypeListFormObjectMap = Maps.newHashMap();

    public static Map<String, Set<String>> invisibleFieldNameListForAddLayout = Maps.newHashMap();
    private static Map<String, Set<String>> invisibleFieldNameListForAddLayout_775 = Maps.newHashMap();
    public static Map<String, Set<String>> invisibleFieldNameListForEditLayout = Maps.newHashMap();
    public static Map<String, Set<String>> invisibleFieldNameListForDetailLayout = Maps.newHashMap();
    public static Map<String, List<String>> invisibleAddButtonMap = Maps.newHashMap();
    public static Map<String, List<String>> invisibleReferenceMap = Maps.newHashMap();
    public static Map<String, List<String>> grayInvisibleReferenceMap = Maps.newHashMap();
    //对象详情页不展示的组件(key：对象apiName，value：不展示的组件的apiName列表)
    public static Map<String, List<String>> invisibleComponentMap = Maps.newHashMap();
    public static Map<String, List<String>> invisibleRelationButtonMap = Maps.newHashMap();

    public static Map<String, Set<String>> baseFieldListForObjectMap = Maps.newHashMap();

    /**
     * list中,不应该显示的字段
     */
    public static Map<String, Set<String>> invisibleFieldNameListForListLayout = Maps.newHashMap();

    // 流程待办列表，表头需要隐藏的字段
    public static Map<String, Set<String>> flowInvisibleFieldNameMap = Maps.newHashMap();
    // 流程待办、移动端默认布局
    public static Map<String, List<String>> FLOW_LIST_LAYOUT_FIELD = Maps.newHashMap();
    // 流程待办、移动端默认布局字段（ApprovalTask）
    public static Map<String, List<String>> APPROVAL_TASK_FLOW_LAYOUT_FIELD = Maps.newHashMap();
    public static Map<String, List<String>> APPROVAL_TASK_FLOW_LAYOUT_FIELD_GRAY = Maps.newHashMap();
    //新详情页字段类型列表
    public static Map<String, List<String>> WEB_DETAIL_LAYOUT_FIELDS = Maps.newHashMap();
    public static Map<String, List<String>> WEB_DETAIL_LAYOUT_FIELDS_GRAY = Maps.newHashMap();
    private static Map<String, List<String>> UN_SUPPORT_FIELD_TYPES_IF_OUT_USER = Maps.newHashMap();
    public static Map<String, Set<String>> internalObjShowFieldsMap = Maps.newHashMap();

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-java-special-field-gray", config -> {
            try {
                String SystemFieldListString = getConfigStr(config, "SystemFieldList");
                String BriefFieldListString = getConfigStr(config, "BriefFieldList");
                String InvisibleFieldListString = getConfigStr(config, "InvisibleFieldList");
                String InvisibleFieldTypeList4AddOrUpdateLayoutString = getConfigStr(config, "InvisibleFieldTypeList4AddOrUpdateLayout");
                String ChangeToEmployeeRenderTypeListString = getConfigStr(config, "ChangeToEmployeeRenderTypeList");
                String InvisibleFieldNameListForAddLayout_775 = getConfigStr(config, "InvisibleFieldNameListForAddLayout_775");
                String InvisibleFieldNameListForAddLayout = getConfigStr(config, "InvisibleFieldNameListForAddLayout");
                String InvisibleFieldNameListForEditLayout = getConfigStr(config, "InvisibleFieldNameListForEditLayout");
                String invisibleAddButtonApiName = getConfigStr(config, "invisibleAddButtonApiName");
                String invisibleReferenceApiName = getConfigStr(config, "invisibleReferenceApiName");
                String invisibleRelationButtonApiName = getConfigStr(config, "invisibleRelationButtonApiName");
                String InvisibleFieldNameListForListLayout = getConfigStr(config, "InvisibleFieldNameListForListLayout");
                String flowInvisibleFieldApiName = getConfigStr(config, "flowInvisibleFieldApiName");
                String flowListLayoutField = getConfigStr(config, "flowListLayoutField");
                String approvalTaskFlowListLayoutField = getConfigStr(config, "approvalTaskFlowListLayoutField");
                String approvalTaskFlowListLayoutFieldGray = getConfigStr(config, "approvalTaskFlowListLayoutFieldGray");
                String webDetailLayoutFields = getConfigStr(config, "webDetailLayoutField");
                String webDetailLayoutFieldsGray = getConfigStr(config, "webDetailLayoutFieldGray");
                String unSupportFieldTypesIfOutUser = getConfigStr(config, "unSupportFieldTypesIfOutUser");
                String internalObjShowFields = getConfigStr(config, "internalObjShowFields");

                // SystemFieldList代表初始化layout的时候放在详细信息的系统分组中的字段
                systemFieldListFormObjectMap = generateFieldListMapFromConfigStr(SystemFieldListString);
                // BriefFieldList代码初始化layout的时候放在SimpleGroupComponent中的字段
                briefFieldListFormObjectMap = generateFieldListMapFromConfigStr(BriefFieldListString);
                // InvisibleFieldList代表在各种场景的Layout下都不应该显示的字段
                invisibleFieldListFormObjectMap = generateFieldListMapFromConfigStr(InvisibleFieldListString);
                // InvisibleFieldTypeList4AddOrUpdateLayout表示在create和edit的layout中,不应该显示的字段类型
                invisibleFieldTypeList4AddOrUpdateLayoutFormObjectMap = generateFieldListMapFromConfigStr(InvisibleFieldTypeList4AddOrUpdateLayoutString);
                // InvisibleFieldTypeList4AddOrUpdateLayout表示在create和edit的layout中,不应该显示的字段名字
                invisibleFieldNameListForAddLayout = generateFieldListMapFromConfigStr(InvisibleFieldNameListForAddLayout);
                invisibleFieldNameListForAddLayout_775 = generateFieldListMapFromConfigStr(InvisibleFieldNameListForAddLayout_775);
                // InvisibleFieldTypeList4AddOrUpdateLayout表示在create和edit的layout中,不应该显示的字段名字
                invisibleFieldNameListForEditLayout = generateFieldListMapFromConfigStr(InvisibleFieldNameListForEditLayout);
                // ChangeRenderTypeList表示在初始化layout的时候，要特殊处理成employee类型的字段
                changeToEmployeeRenderTypeListFormObjectMap = generateFieldListMapFromConfigStr(ChangeToEmployeeRenderTypeListString);
                invisibleAddButtonMap = (Map<String, List<String>>) JSONObject.parse(invisibleAddButtonApiName);
                invisibleReferenceMap = (Map<String, List<String>>) JSONObject.parse(invisibleReferenceApiName);
                grayInvisibleReferenceMap = parseMapFromConfig(config, "invisibleReferenceApiName_gray");
                invisibleComponentMap = parseMapFromConfig(config, "invisibleComponentMap");
                invisibleRelationButtonMap = (Map<String, List<String>>) JSONObject.parse(invisibleRelationButtonApiName);
                // InvisibleFieldTypeList4ListLayout表示在list的layout中,不应该显示的字段名字
                invisibleFieldNameListForListLayout = generateFieldListMapFromConfigStr(InvisibleFieldNameListForListLayout);
                flowInvisibleFieldNameMap = generateFieldListMapFromConfigStr(flowInvisibleFieldApiName);
                FLOW_LIST_LAYOUT_FIELD = (Map<String, List<String>>) JSONObject.parse(flowListLayoutField);
                APPROVAL_TASK_FLOW_LAYOUT_FIELD = (Map<String, List<String>>) JSONObject.parse(approvalTaskFlowListLayoutField);
                APPROVAL_TASK_FLOW_LAYOUT_FIELD_GRAY = (Map<String, List<String>>) JSONObject.parse(approvalTaskFlowListLayoutFieldGray);
                WEB_DETAIL_LAYOUT_FIELDS = (Map<String, List<String>>) JSONObject.parse(webDetailLayoutFields);
                WEB_DETAIL_LAYOUT_FIELDS_GRAY = (Map<String, List<String>>) JSONObject.parse(webDetailLayoutFieldsGray);
                UN_SUPPORT_FIELD_TYPES_IF_OUT_USER = (Map<String, List<String>>) JSONObject.parse(unSupportFieldTypesIfOutUser);
                internalObjShowFieldsMap = generateFieldListMapFromConfigStr(internalObjShowFields);
            } catch (Exception e) {
                log.error("fs-crm-java-special-field config has error", e);
            }
        });


        baseFieldListForObjectMap.put("OpportunityObj", Sets.newHashSet("expected_deal_amount"));
        baseFieldListForObjectMap.put("ReturnedGoodsInvoiceObj", Sets.newHashSet("returned_goods_time"));
        baseFieldListForObjectMap.put("LeadsObj", Sets.newHashSet("company", "source"));
        baseFieldListForObjectMap.put("ReturnedGoodsInvoiceObj", Sets.newHashSet("account_id", "is_unread", "order_id"));
        baseFieldListForObjectMap.put("SalesOrderObj", Sets.newHashSet("account_id", "order_time", "receipt_type", "owner"));

//        flowInvisibleFieldNameMap.put(Utils.BPM_TASK_API_NAME, Sets.newHashSet("processorIds"));
    }

    private static <T> Map<String, T> parseMapFromConfig(IConfig config, String key) {
        String data = config.get(key);
        if (Strings.isNullOrEmpty(data)) {
            return Maps.newHashMap();
        }
        return JSON.parseObject(data, Map.class);
    }

    private static String getConfigStr(IConfig config, String key) {
        return StringUtils.trim(config.get(key));
    }

    static private Map<String, Set<String>> generateFieldListMapFromConfigStr(String configStr) {
        if (StringUtils.isBlank(configStr)) {
            return Maps.newHashMap();
        }
        // s的example:
        // AccountObj:create_time,created_by,Owner,Status;ProductObj:create_time
        Map<String, Set<String>> systemFieldListFormObject = Maps.newHashMap();
        for (String eachStr : configStr.split(";")) {
            try {
                Set<String> fields = Sets.newLinkedHashSet();
                Collections.addAll(fields, eachStr.split(":")[1].split(","));
                systemFieldListFormObject.put(eachStr.split(":")[0], fields);
            } catch (Exception e) {
                log.error("fs-crm-java-special-field-gray config has error, key:{}", eachStr, e);
            }
        }
        return systemFieldListFormObject;
    }


    //// TODO: 2017/8/1 下面这三个配置,在fs-crm-udobj项目中也配置了,以后可以干掉了。
    //CRM中定义的Action和元数据的Action的code转换map
    public static HashMap<String, String> CRM_ACTION_CODE_FROM_METADATA_ACTION_CODE_MAP = Maps.newHashMap();

    static {
        //元数据的action名字和CRM中的action名字的映射关系
        CRM_ACTION_CODE_FROM_METADATA_ACTION_CODE_MAP.put(Constant.ACTION_CODE_DELETE, CrmActionEnum.DELETE.getActionCode());
        CRM_ACTION_CODE_FROM_METADATA_ACTION_CODE_MAP.put(Constant.ACTION_CODE_VIEW_DETAIL, CrmActionEnum.VIEW_DETAIL.getActionCode());
        CRM_ACTION_CODE_FROM_METADATA_ACTION_CODE_MAP.put(Constant.ACTION_CODE_VIEW_LIST, CrmActionEnum.VIEW_LIST.getActionCode());
        CRM_ACTION_CODE_FROM_METADATA_ACTION_CODE_MAP.put(Constant.ACTION_CODE_CREATE, CrmActionEnum.CREATE.getActionCode());
        CRM_ACTION_CODE_FROM_METADATA_ACTION_CODE_MAP.put(Constant.ACTION_CODE_UPDATE, CrmActionEnum.UPDATE.getActionCode());
        CRM_ACTION_CODE_FROM_METADATA_ACTION_CODE_MAP.put(Constant.ACTION_CODE_BULK_IMPORT, CrmActionEnum.BATCH_IMPORT.getActionCode());
        CRM_ACTION_CODE_FROM_METADATA_ACTION_CODE_MAP.put(Constant.ACTION_CODE_BULK_EXPORT, CrmActionEnum.BATCH_EXPORT.getActionCode());
        CRM_ACTION_CODE_FROM_METADATA_ACTION_CODE_MAP.put(Constant.ACTION_CODE_INVALID, CrmActionEnum.INVALID.getActionCode());
        CRM_ACTION_CODE_FROM_METADATA_ACTION_CODE_MAP.put(Constant.ACTION_CODE_RECOVER, CrmActionEnum.RECOVER.getActionCode());
    }

    public static List<String> getFieldTypeList(String tenantId, String describeApiName, boolean isBigObject) {
        if (isBigObject) {
            return AppFrameworkConfig.getBigObjectFieldTypeList();
        }
        Map<String, List<String>> webDetailLayoutFieldGray = DefObjConstants.WEB_DETAIL_LAYOUT_FIELDS;
        String apiNameKey = webDetailLayoutFieldGray.containsKey(describeApiName) ? describeApiName : ALL_FIELD_TYPE;
        List<String> fieldTypes = Lists.newArrayList(webDetailLayoutFieldGray.get(apiNameKey));
        if (CollectionUtils.empty(fieldTypes)) {
            return fieldTypes;
        }
        // 预置对象不支持创建自定义的主从字段
        if (!Strings.isNullOrEmpty(describeApiName) && !AppFrameworkConfig.isCustomObject(describeApiName)) {
            fieldTypes.remove(IFieldType.MASTER_DETAIL);
        }
        // 不支持配置支付组件的企业
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.UN_SUPPORT_PAYMENT_GROUP_GRAY_EI, tenantId)) {
            fieldTypes.remove(GroupField.GROUP_TYPE_PAYMENT);
        }
        AppFrameworkConfig.processFieldTypes(tenantId, apiNameKey, fieldTypes);
        return fieldTypes;
    }

    public static List<String> getApprovalTaskFlowLayoutField(String apiName) {
        if (AppFrameworkConfig.isApprovalTaskFlowLayoutFieldUseGray()) {
            return APPROVAL_TASK_FLOW_LAYOUT_FIELD_GRAY.get(apiName);
        }
        return APPROVAL_TASK_FLOW_LAYOUT_FIELD.get(apiName);
    }

    public static Set<String> getInvisibleFieldNameListForAddLayout(String tenantId, String objectApiName) {
        Set<String> udobjFields = Sets.newHashSet(invisibleFieldNameListForAddLayout_775.get(UDOBJ));
        if (invisibleFieldNameListForAddLayout_775.containsKey(objectApiName)) {
            udobjFields.addAll(invisibleFieldNameListForAddLayout_775.get(objectApiName));
        }
        return ImmutableSet.copyOf(udobjFields);
    }

    public static boolean isReferenceObjectInvisible(String objectApiName, String refObjectApiName) {
        if (Strings.isNullOrEmpty(refObjectApiName)) {
            return false;
        }
        if (CollectionUtils.notEmpty(grayInvisibleReferenceMap)) {
            return grayInvisibleReferenceMap.getOrDefault(objectApiName, Collections.emptyList()).contains(refObjectApiName);
        }
        return invisibleReferenceMap.getOrDefault(objectApiName, Collections.emptyList()).contains(refObjectApiName);
    }

    public static boolean isComponentInvisible(String objectApiName, String componentApiName) {
        if (Strings.isNullOrEmpty(componentApiName)) {
            return false;
        }
        return invisibleComponentMap.getOrDefault(objectApiName, Collections.emptyList()).contains(componentApiName);
    }

    public static Set<String> getUnSupportFieldTypesIfOutUser(User user, String objectApiName) {
        List<String> result = null;
        if (AppFrameworkConfig.unSupportFieldTypesIfOutUserGrayRules(user.getTenantId(), objectApiName)) {
            result = UN_SUPPORT_FIELD_TYPES_IF_OUT_USER.get(objectApiName);
        }
        if (Objects.isNull(result)) {
            result = UN_SUPPORT_FIELD_TYPES_IF_OUT_USER.getOrDefault(UdobjGrayConfigKey.DEFAULT, Collections.emptyList());
        }
        return ImmutableSet.copyOf(result);
    }

}
