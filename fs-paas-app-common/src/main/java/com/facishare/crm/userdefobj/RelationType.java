package com.facishare.crm.userdefobj;

import com.facishare.paas.appframework.core.i18n.I18NKey;

/**
 * Created by luxin on 2017/4/20.
 */


public enum RelationType {

  ONE_TO_ONE(1, "ONE_TO_ONE"), ONE_TO_MANY(2, "ONE_TO_MANY"), MANY_TO_ONE(3, "MANY_TO_ONE"), MANY_TO_MANY(4, "MANY_TO_MANY");

  RelationType(int type, String desc) {
    this.type = type;
    this.desc = desc;
  }

  public int getType() {
    return type;
  }

  public String getDesc() {
    return desc;
  }

  int type;
  String desc;

}



