package com.facishare.crm.util;

import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.timezone.DateTimeFormatUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.TemporalAccessor;
import java.util.regex.Pattern;

import static com.facishare.paas.appframework.common.util.DateTimeUtils.isGrayTimeZone;

/**
 * 日期，时间处理工具类
 *
 * <AUTHOR>
 * @date 2019-05-15 17:47
 */
public class DateTimeUtils {


    // ---------------------------------------- Constants

    /**
     * 日期时间格式模式: yyyy-MM-dd HH:mm:ss
     */
    private static final Pattern PATTERN_DATE_TIME = Pattern.compile(
            "^((\\d{2}(([02468][048])|([13579][26]))" +
                    "[\\-\\/\\s]?" +
                    "((((0?[13578])|(1[02]))" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|([1-2][0-9])|(30)))|(0?2" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))" +
                    "[\\-\\/\\s]?" +
                    "((((0?[13578])|(1[02]))" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|([1-2][0-9])|(30)))|(0?2" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|(1[0-9])|(2[0-8]))))))" +
                    "(\\s(((0?[0-9])|([1][0-9])|([2][0-4]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))?))?$"
    );


    /**
     * 日期格式模式: yyyy-MM-dd
     */
    private static final Pattern PATTERN_DATE = Pattern.compile(
            "^((\\d{2}(([02468][048])|([13579][26]))" +
                    "[\\-\\/\\s]?" +
                    "((((0?[13578])|(1[02]))" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|([1-2][0-9])|(30)))|(0?2" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))" +
                    "[\\-\\/\\s]?" +
                    "((((0?[13578])|(1[02]))" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|([1-2][0-9])|(30)))|(0?2" +
                    "[\\-\\/\\s]?" +
                    "((0?[1-9])|(1[0-9])|(2[0-8]))))))?$"
    );

    private static final DateTimeFormatter DATE_TIME_FORMATTER = new DateTimeFormatterBuilder()
            .appendPattern("[yyyy-M-d H:m[:s]]")
            .appendPattern("[yyyy/M/d H:m[:s]]")
            .appendPattern("[M/d/yy H:m[:s]]")
            .appendPattern("[M/d/yyyy H:m[:s]]")
            .toFormatter();
    private static final DateTimeFormatter DATE_FORMATTER = new DateTimeFormatterBuilder()
            .appendPattern("[yyyy-M-d H:m[:s]]")
            .appendPattern("[yyyy/M/d H:m[:s]]")
            .appendPattern("[M/d/yy H:m[:s]]")
            .appendPattern("[M/d/yyyy H:m[:s]]")
            .appendPattern("[yyyy-M-d]")
            .appendPattern("[yyyy/M/d]")
            .appendPattern("[M/d/yyyy]")
            .appendPattern("[M/d/yy]")
            .appendPattern("[yyyy年M月d日]") // ignoreI18n
            .toFormatter();


    // ---------------------------------------- Constructors

    /**
     * 私有构造器，保证该类无法随意创建
     */
    private DateTimeUtils() {
    }


    // ---------------------------------------- Inner Class

    private static class Helper {
        private static final DateTimeUtils INSTANCE = new DateTimeUtils();
    }


    // ---------------------------------------- Static Factory Method

    public static DateTimeUtils getInstance() {
        return Helper.INSTANCE;
    }


    // ---------------------------------------- Public Methods

    /**
     * 日期时间格式是否匹配
     */
    public boolean isDateTime(String str) {
        return PATTERN_DATE_TIME.matcher(str).matches();
    }

    /**
     * 日期格式是否匹配
     */
    public boolean isDate(String str) {
        return PATTERN_DATE.matcher(str).matches();
    }

    /**
     * 形如yyyy-MM-dd HH:mm:ss 或者 yyyy-MM-dd
     * 或者 yyyy/MM/dd等格式匹配其中之一
     */
    public boolean isValidTime(String str) {
        return isDateTime(str);
    }

    /**
     * 将日期时间格式转为时间戳
     */
    public long convertToLong(String str) {
        if (isDateTime(str) && !isDate(str)) {
            String newStr = str.replaceAll("\\/", "-");
            newStr = formatDateTime(newStr);
            return convertDateTimeToLong(newStr);
        } else if (isDate(str)) {
            String newStr = str.replaceAll("\\/", "-");
            return convertDateToLong(newStr);
        }
        return -1;
    }

    public long convertDateToLong(String str) {
        if (isGrayTimeZone()) {
            try {
                return DateTimeFormatUtils.parse(str, IFieldType.DATE);
//                return DateTimeFormat.DATE.convertToTimestamp(str);
            } catch (Exception e) {
                return -1;
            }
        }
        TemporalAccessor accessor = null;

        try {
            accessor = DATE_FORMATTER.parse(str);
        } catch (Exception e) {
            return -1;
        }
        return LocalDate.from(accessor).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    public long convertDateTimeToLong(String str) {
        if (isGrayTimeZone()) {
            try {
                return DateTimeFormatUtils.parse(str, IFieldType.DATE_TIME);
//                return DateTimeFormat.DATE_TIME.convertToTimestamp(str);
            } catch (Exception e) {
                return -1;
            }
        }
        TemporalAccessor accessor = null;

        try {
            accessor = DATE_TIME_FORMATTER.parse(str);
        } catch (Exception e) {
            return -1;
        }
        return LocalDateTime.from(accessor).toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 格式化日期时间格式
     */
    private String formatDateTime(String newStr) {
        int colonTime = 0;
        for (char c : newStr.toCharArray()) {
            if (':' == c) {
                ++colonTime;
            }
        }
        if (colonTime == 1) {
            newStr = newStr.trim() + ":00";
        }
        return newStr;
    }


}
