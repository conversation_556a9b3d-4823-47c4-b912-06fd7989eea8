package com.facishare.crm.valueobject;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.google.common.base.Strings;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.time.ZoneId;
import java.util.Objects;


/**
 * Created by lei on 12/21/16.
 */
public class SessionContext {
    public static final String SOURCE_WEB_END = "source_web_end";
    public static final String SOURCE_MOBILE_END = "source_mobile_end";
    public static final String SOURCE_UNKNOWN_END = "source_unknown_end";
    private String ea;
    private Long eId;
    private Integer userId;
    private boolean privilegeCheck = true;
    private String clientInfo;
    private String appId;
    private String languageLocale;
    //外部用户Id
    private String outUserId;
    //外部企业
    private String outerTenantId;

    @Setter
    private boolean useTenantTimezone;
    @Setter
    @Getter
    private boolean dateFormatIncludeTimezoneInfo;
    @Setter
    @Getter
    private String region;
    @Setter
    @Getter
    private boolean fromQuoteField;

    public SessionContext() {
    }

    public static SessionContext of(User user) {
        SessionContext context = new SessionContext();
        context.setEId(Long.parseLong(user.getTenantId()));
        if (StringUtils.isNotEmpty(user.getUserId())) {
            context.setUserId(Integer.parseInt(user.getUserId()));
        }
        context.setOutUserId(user.getOutUserId());
        context.setOuterTenantId(user.getOutTenantId());
        return context;
    }

    public boolean isOutUser() {
        return (
                Strings.isNullOrEmpty(outerTenantId)
                        && StringUtils.isNoneBlank(outUserId)
                        && !Objects.equals(outerTenantId, "0")
                        && !Objects.equals(outUserId, "0")
        );
    }

    public String sourceFrom() {
        if (Strings.isNullOrEmpty(clientInfo)) {
            return SOURCE_UNKNOWN_END;
        }

        if (clientInfo.contains("Android") || clientInfo.contains("iOS")) {
            return SOURCE_MOBILE_END;
        }

        return SOURCE_WEB_END;
    }

    public String getOutUserId() {
        return outUserId;
    }

    public void setOutUserId(String outUserId) {
        this.outUserId = outUserId;
    }

    public String getEa() {
        return ea;
    }

    public void setEa(String ea) {
        this.ea = ea;
    }

    public Long getEId() {
        return eId;
    }

    public void setEId(Long eId) {
        this.eId = eId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {

        this.userId = userId;
    }

    public String getUserIdString() {
        if (Objects.isNull(userId)) {
            return null;
        }
        return String.valueOf(userId);
    }

    public boolean getPrivilegeCheck() {
        return privilegeCheck;
    }

    public void setPrivilegeCheck(Boolean privilegeCheck) {
        this.privilegeCheck = privilegeCheck;
    }

    public String getClientInfo() {
        return clientInfo;
    }

    public void setClientInfo(String clientInfo) {
        this.clientInfo = clientInfo;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getLanguageLocale() {
        return languageLocale;
    }

    public void setLanguageLocale(String languageLocale) {
        this.languageLocale = languageLocale;
    }

    public String getOuterTenantId() {
        return outerTenantId;
    }

    public void setOuterTenantId(String outerTenantId) {
        this.outerTenantId = outerTenantId;
    }

    public ZoneId getTimeZone() {
        return useTenantTimezone ? TimeZoneContextHolder.getTenantTimeZone() : TimeZoneContextHolder.getUserTimeZone();
    }
}
