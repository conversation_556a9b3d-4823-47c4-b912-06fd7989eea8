package com.facishare.paas.common.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by yusb on 2017/8/14.
 */

@Data
@Builder
public class BulkOpResult {
    private List<IObjectData> successObjectDataList;
    private List<IObjectData> failObjectDataList;
    private String failReason;

    public boolean hasFailed() {
        return CollectionUtils.notEmpty(failObjectDataList);
    }

    public void merge(BulkOpResult opResult) {
        if (opResult == null) {
            return;
        }
        init();
        if (CollectionUtils.notEmpty(opResult.getSuccessObjectDataList())) {
            successObjectDataList.addAll(opResult.getSuccessObjectDataList());
        }
        if (CollectionUtils.notEmpty(opResult.getFailObjectDataList())) {
            List<IObjectData> failDataToMerge = opResult.getFailObjectDataList().stream()
                    .filter(x -> failObjectDataList.stream().noneMatch(y -> Objects.equals(x.getId(), y.getId())))
                    .collect(Collectors.toList());
            failObjectDataList.addAll(failDataToMerge);
        }
        if (!Strings.isNullOrEmpty(opResult.getFailReason())) {
            if (Strings.isNullOrEmpty(failReason)) {
                failReason = opResult.getFailReason();
            } else {
                failReason = failReason + "|" + opResult.getFailReason();
            }
        }
    }

    public List<String> successObjectDataIds() {
        return CollectionUtils.nullToEmpty(successObjectDataList).stream()
                .map(IObjectData::getId)
                .distinct()
                .collect(Collectors.toList());
    }

    private void init() {
        if (successObjectDataList == null) {
            successObjectDataList = Lists.newArrayList();
        }
        if (failObjectDataList == null) {
            failObjectDataList = Lists.newArrayList();
        }
        if (failReason == null) {
            failReason = "";
        }
    }
}
