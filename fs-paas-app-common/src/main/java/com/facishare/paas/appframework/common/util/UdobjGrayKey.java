package com.facishare.paas.appframework.common.util;

public interface UdobjGrayKey {
    String FOLLOW_GRAY = "follow_gray";
    String DESCRIBE_LAYOUT_CHECK_FUNC_PRIVILEGE = "describe_layout_check_func_privilege";
    String BATCH_CALCULATE_RESULT_CHECK_MODIFY = "batch_calculate_result_check_modify";
    //计算和UI事件接口使用seriesId加锁
    String LOCK_BY_SERIES_ID_IN_CALCULATE_AND_UI_EVENT_AND_ADD_EDIT_REQUEST = "lock_by_series_id_in_calculate_and_ui_event_and_add_edit_request";
    //batchCalculate接口优化从对象计算的灰度
    String OPTIMIZE_DETAIL_OBJECT_CALCULATE_IN_BATCH_CALCULATE = "optimizeDetailObjectCalculateInBatchCalculate";
    String RECENT_VISIT = "RecentVisit";
    // 安全事件埋点灰度
    String SECURITY_INCIDENTS_LIST = "security_incidents_list";
    String SECURITY_INCIDENTS_DETAIL = "security_incidents_detail";
    String SECURITY_INCIDENTS_EXPORT = "security_incidents_export";
    // 字段合规性设置功能灰度键值
    String FIELD_COMPLIANCE_SETTING_GRAY = "field_compliance_setting_gray";

}
