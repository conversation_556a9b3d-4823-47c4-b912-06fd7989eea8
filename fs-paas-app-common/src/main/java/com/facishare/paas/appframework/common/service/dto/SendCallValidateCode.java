package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * wiki : http://git.firstshare.cn/Infrastructure/fs-register/wikis/validatecode
 * Created by fengjy in 2020/2/4 17:15
 */
public interface SendCallValidateCode {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg{
        /**
         * 手机号码
         */
        String mobile;
        /**
         * ip 地址
         */
        String ip;
        /**
         * 输入的验证码
         */
        String imgCode;
        /**
         * 图验 id
         */
        String epxId;
        /**
         * 配置文件fs-register-provider-vcode 的Actions
         * 暂时只支持RegisterSecurity,FindPassWordSecurity,InviteSecurity,TiyanSecurity,EmployeeBindMobile,CollectionCustomer,LoginSecurityAuthorize,LoginDynamicPassword,LoginSecurityAuthorizeOld,kisManagerVerify,CRM
         */
        String action;
        /**
         * 企业账号（EA）
         */
        String enterpriseAccount;
        /**
         * 操作员工编号
         */
        String employeeId;
        /**
         * 手机号码区号
         */
        String areaCode;
    }

    // com.facishare.paas.appframework.common.service.dto.SendCallValidateCode.Result
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result{
        String sendCodeResultEnum;
    }
}


