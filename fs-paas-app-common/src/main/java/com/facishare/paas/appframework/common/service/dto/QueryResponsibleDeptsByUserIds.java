package com.facishare.paas.appframework.common.service.dto;

import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.type.DepartmentStatus;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by renlb on 2018/4/23.
 */
public interface QueryResponsibleDeptsByUserIds {

    @Data
    @Builder
    class Arg{
        private OrgContext context;
        private List<String> managerIds;
        private Integer deptStatus;
    }

    @Data
    class Result{
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<DeptInfo> result;
        private boolean success;
    }


    @Data
    class DeptInfo{
        private String id;
        private String tenantId;
        private String name;
        private String managerId;
        private Integer status;
        private String parentId;
        private String description;
        private Long createTime;
        private Long modifyTime;
        private List<String> ancestors;

        public static DeptInfo convert(DepartmentDto dept) {
            DeptInfo info = new DeptInfo();
            info.setId(String.valueOf(dept.getDepartmentId()));
            info.setTenantId(String.valueOf(dept.getEnterpriseId()));
            info.setName(dept.getName());
            info.setManagerId(String.valueOf(dept.getPrincipalId()));
            info.setStatus(convertStatus(dept.getStatus()));
            info.setParentId(String.valueOf(dept.parentId()));
            info.setDescription(dept.getDescription());
            info.setCreateTime(dept.getCreateTime());
            info.setModifyTime(dept.getUpdateTime());
            info.setAncestors(dept.getAncestors().stream().map(String::valueOf).collect(Collectors.toList()));
            return info;
        }

        private static Integer convertStatus(DepartmentStatus status) {
            switch (status) {
                case DELETE:
                case STOP:
                    return QueryDeptInfoByDeptIds.DeptStatusEnum.DISABLE.getCode();
                case NORMAL:
                    return QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE.getCode();
                default:
                    return 0;
            }
        }
    }
}
