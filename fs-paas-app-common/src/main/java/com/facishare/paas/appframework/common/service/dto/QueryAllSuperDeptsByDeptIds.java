package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by renlb on 2018/4/9.
 */
public interface QueryAllSuperDeptsByDeptIds {

    @Data
    @Builder
    class Arg {
        private OrgContext context;
        private List<String> deptIds;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Map<String, List<DeptInfo>> result;
        private boolean success;
    }

    @Data
    class DeptInfo {
        private String tenantId;
        private String id;
        private String name;
        private String managerId;
        private Integer status;
        private Integer parentId;
        private String description;
        private Long createTime;
        private Long modifyTime;
        private List<String> ancestors;
    }
}
