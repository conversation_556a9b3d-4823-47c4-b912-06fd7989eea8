package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Data
@Builder
public class AuthContext {
    public static final String APP_ID = "CRM";
    private String tenantId;
    private String appId;
    private String userId;
    private Map<String, String> properties;
    private Map<String, Object> objectProperties;
    private String outerTenantId;
    private String outerUserId;
    private String identityType;

    public static AuthContext buildByUser(User user) {
        String outerUserId = user.isOutUser() ? user.getOutUserId() : null;
        String outerTenantId = user.isOutUser() ? user.getOutTenantId() : null;
        return AuthContext
                .builder()
                .appId(getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .outerTenantId(outerTenantId)
                .outerUserId(outerUserId)
                .properties(buildProperties(user))
                .identityType(RequestUtil.getOutIdentityType())
                .build();
    }

    public static String getAppId(User user) {
        if (user.isOutUser()) {
            return APP_ID;
        }
        String appId = RequestUtil.getAppId();
        if (StringUtils.isBlank(appId)) {
            return APP_ID;
        }
        return appId;
    }


    public static Map<String, String> buildProperties(User user) {
        if (!user.isOutUser()) {
            return null;
        }
        String bizAppId = RequestUtil.getAppId();
        Map<String, String> properties = Maps.newHashMap();
        properties.put("bizAppId", bizAppId);
        return properties;
    }
}
