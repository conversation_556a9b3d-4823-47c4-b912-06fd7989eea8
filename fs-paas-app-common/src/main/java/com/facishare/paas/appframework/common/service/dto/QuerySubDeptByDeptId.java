package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaopx on 2018/4/9.
 */
public interface QuerySubDeptByDeptId {

    @Data
    @Builder
    class Arg {
        private OrgContext context;
        private List<String> deptIds;
        private Integer childDeptStatus;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Map<String, List<QueryDeptByName.DeptInfo>> result;
        private boolean success;
    }
}


