package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.CancelJob;
import com.facishare.paas.appframework.common.service.dto.QueryJob;
import com.facishare.paas.appframework.common.service.dto.SubmitJob;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

@RestResource(
        value = "PAAS-SendCalculateJob",
        desc = "计算任务服务", // ignoreI18n
        contentType = "application/json"
)
public interface JobScheduleProxy {

    @POST(value = "/submit", desc = "提交计算任务")
    SubmitJob.Result submitJob(
            @HeaderMap Map<String, String> header,
            @Body SubmitJob.Arg arg);

    @POST(value = "/submitRealTimeJob", desc = "提交实时任务")
    SubmitJob.Result submitRealTimeJob(
            @HeaderMap Map<String, String> header,
            @Body SubmitJob.Arg arg);

    @POST(value = "/cancel", desc = "取消计算任务")
    CancelJob.Result cancelJob(
            @HeaderMap Map<String, String> header,
            @Body CancelJob.Arg arg);

    @POST(value = "/query", desc = "查询任务详情")
    QueryJob.Result queryJob(@HeaderMap Map<String, String> header,
                             @Body QueryJob.Arg arg);
}
