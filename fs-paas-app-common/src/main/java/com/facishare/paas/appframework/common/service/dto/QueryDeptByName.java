package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

public interface QueryDeptByName {

    String TYPE_DEPT = "dept";
    String TYPE_ORG = "org";

    @Data
    @Builder
    class Arg {
        private OrgContext context;
        private List<String> names;
        private Integer deptStatus;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<DeptInfo> result = new ArrayList<>();
        private boolean success;
    }

    @Data
    class DeptInfo {
        private String tenantId;
        private String id;
        private String name;
        private String managerId;
        private Integer status;
        private Integer parentId;
        private String description;
        private Long createTime;
        private Long modifyTime;
        private List<String> ancestors;
        private int order;
        /**
         * 部门类型：
         * 组织 org
         * 部门 dept
         */
        private String deptType;
    }
}
