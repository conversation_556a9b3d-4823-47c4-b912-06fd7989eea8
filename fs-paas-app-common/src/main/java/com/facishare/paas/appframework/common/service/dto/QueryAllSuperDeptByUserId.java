package com.facishare.paas.appframework.common.service.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import lombok.Builder;
import lombok.Data;

/**
 * Created by quzf on 2017/11/30
 */
public interface QueryAllSuperDeptByUserId {

    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        private String id;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Set<String> result;
        private boolean success;
    }
}
