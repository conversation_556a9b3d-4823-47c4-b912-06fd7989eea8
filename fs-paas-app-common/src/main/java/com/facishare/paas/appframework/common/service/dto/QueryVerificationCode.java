package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.service.model.ObjectDataDocument;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Created by fengjy in 2020/2/4 14:33
 */
public interface QueryVerificationCode {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        //手机号
        String phone;
        //图形验证码
        String captchaCode;
        //标识图形验证码的id
        String captchaId;
        //区域号
        String areaCode;

        // 主对象数据
        ObjectDataDocument objectData;
        // 从对象数据
        Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
        String fieldApiName;
        String describeApiName;

        public String getAreaCode() {
            //默认返回中国大陆 +86
            return StringUtils.isEmpty(areaCode) ? "+86" : areaCode;
        }
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        int errorCode;
        String errorMessage;

        public static final int SUCCESS = 200;
        public static final int NEED_CAPTCHA = 300;//需要图形验证码
        public static final int ERROR = 500;
    }
}
