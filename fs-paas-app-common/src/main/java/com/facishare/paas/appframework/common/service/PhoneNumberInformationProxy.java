package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.QueryPhoneNumberInformation;
import com.facishare.rest.core.annotation.*;

import java.util.Map;


@RestResource(value = "PAAS-MOBILE-PATH", desc = "手机号归属地查询", contentType = "application/json", codec = "com.facishare.paas.appframework.common.service.PhoneNumberCodec")
public interface PhoneNumberInformationProxy {

    @POST(value = "/rest/queryAll", desc = "手机号归属地查询服务")
    QueryPhoneNumberInformation.Code batchQueryPhoneNumberInformation(@QueryParamsMap Map<String, String> phoneNumber);

    @POST(value = "/rest/query-batch", desc = "手机号归属地查询服务")
    QueryPhoneNumberInformation.Code queryBatchQueryPhoneNumberInformation(@Body QueryPhoneNumberInformation.Mobiles arg, @QueryParam("language") String language);

    @GET(value = "/rest/query", desc = "手机号归属地查询服务")
    QueryPhoneNumberInformation.Code queryPhoneNumberInformation(@QueryParamsMap Map<String, String> phoneNumber, @QueryParam("language") String language);
}
