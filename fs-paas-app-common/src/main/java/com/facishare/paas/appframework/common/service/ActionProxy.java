package com.facishare.paas.appframework.common.service;

import com.facishare.rest.core.annotation.*;

import java.util.Map;

@RestResource(
        value = "NCRM",
        desc = "NCRM服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface ActionProxy {

    @POST(value = "/API/v1/rest/object/{apiName}/action/{actionCode}", desc = "执行Action")
    String executeAction(@Body Object arg, @HeaderMap Map<String, String> headers, @PathParams Map<String, String> pathParams,
                         @QueryParamsMap Map<String, String> queryParams);

}
