package com.facishare.paas.appframework.common.service.impl;

import com.facishare.paas.appframework.common.service.GeoAddressProxy;
import com.facishare.paas.appframework.common.service.GeoAddressService;
import com.facishare.paas.appframework.common.service.dto.GeoAddressDTO;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 地址解析服务实现
 */
@Service
public class GeoAddressServiceImpl implements GeoAddressService {

    @Autowired
    private GeoAddressProxy geoAddressProxy;

    @Override
    public GeoAddressDTO.GeoResult getAddressByGeo(User user, GeoAddressDTO.ReverseGeoRequest request) {
        Map<String, String> headers = RestUtils.buildHeaders(user);
        GeoAddressDTO.RestResult result = geoAddressProxy.getAddressByGeo(headers, request);
        return result != null && result.isSuccess() ? result.getData() : null;
    }
} 