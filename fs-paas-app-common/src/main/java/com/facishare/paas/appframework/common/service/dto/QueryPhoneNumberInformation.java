package com.facishare.paas.appframework.common.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.checkerframework.checker.units.qual.A;

import java.util.List;

public interface QueryPhoneNumberInformation {

    @Data
    class Arg {
        @JsonProperty("mobile")
        private String mobile;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Mobiles {
        @JsonProperty("mobiles")
        private String mobiles;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private String mobile;
        private String province;
        private String city;
        private String code;
        private String operator;
        private String mobilePath;
        private I18n i18n;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class I18n {
        private String language;
        private String province;
        private String city;
        @JSONField(name="operator", alternateNames={"carrier"})
        @SerializedName(value = "operator", alternate = {"carrier"})
        private String operator;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Code {
        private List<Result> results;
        private Integer errCode;
        private String errMessage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class CodeV2 {
        private List<Result> data;
        private Integer code;
        private String message;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class CodeV2Single {
        private Result data;
        private Integer code;
        private String message;
    }
}
