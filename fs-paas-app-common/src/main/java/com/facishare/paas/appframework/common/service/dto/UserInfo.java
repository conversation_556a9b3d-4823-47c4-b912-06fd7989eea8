package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfo {
    public static final int NORMAL_STATUS = 0;
    private String id;
    private String tenantId;
    private String enterpriseName;
    private String name;
    private String position;
    private String picAddr;
    private String email;
    private String nickname;
    private String title;
    private String supervisorId;
    private String phone;
    private String mobile;
    private String description;
    private Integer status;
    private Long createTime;
    private Long modifyTime;
    private String dept;
    private String post;
    private String empNum;

    public boolean disabled() {
        return !Objects.equals(status, NORMAL_STATUS);
    }
}
