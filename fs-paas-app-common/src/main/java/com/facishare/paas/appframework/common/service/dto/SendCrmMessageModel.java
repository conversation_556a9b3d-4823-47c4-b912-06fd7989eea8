package com.facishare.paas.appframework.common.service.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import java.util.Set;

/**
 * Created by linqy on 2018/01/24
 */
public interface SendCrmMessageModel {

    @Data
    @Builder
    class Arg {

        @SerializedName(value = "EmployeeID")
        private String employeeId;

        @SerializedName(value = "Content")
        private Object content;

        @SerializedName(value = "RemindRecordType")
        private Integer remindRecordType;

        @SerializedName(value = "Content2ID")
        private String content2Id;

        @SerializedName(value = "DataID")
        private String dataId;

        @SerializedName(value = "ReceiverIDs")
        private Set<Integer> receiverIds;

        @SerializedName(value = "Title")
        private String title;

        @SerializedName(value = "FixContent2ID")
        private String fixContent2ID;

        @SerializedName(value = "OutEI")
        private Integer outEI;

        @SerializedName(value = "AppId")
        private String appId;
    }

    @Data
    class Result {
        private boolean value;
        private boolean success;
        private String message;
        private Integer errorCode;

    }
}
