package com.facishare.paas.appframework.common.service;

import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 组织架构部门接口
 *
 * <AUTHOR>
 * @date 2019/12/26 11:02 上午
 */
public interface DepartmentService {
    /**
     * 批量部门信息，默认获取全部状态的部门
     *
     * @param tenantId 企业ID
     * @param deptIds  部门ID
     * @return 部门信息列表
     */
    List<DepartmentDto> batchGetDepartment(String tenantId, Collection<String> deptIds);


    /**
     * 批量获取部门信息
     *
     * @param tenantId 企业ID
     * @param deptIds  部门ID
     * @param status   部门状态
     * @return 部门信息
     */
    List<DepartmentDto> batchGetDepartment(String tenantId, Collection<String> deptIds, QueryDeptInfoByDeptIds.DeptStatusEnum status);

    /**
     * 查询给定部门ID的子部门
     *
     * @param tenantId 企业ID
     * @param deptIds  部门ID
     * @param status   部门状态
     * @return 所有子部门
     */
    List<DepartmentDto> getLowDepartment(String tenantId, String deptIds, QueryDeptInfoByDeptIds.DeptStatusEnum status);

    /**
     * 根据部门具体名称获取部门信息（不含部门编码）
     *
     * @param tenantId  企业ID
     * @param deptNames 部门名称
     * @param status    部门状态
     * @return 部门信息
     */
    List<DepartmentDto> batchGetDepartmentByNames(String tenantId, List<String> deptNames, QueryDeptInfoByDeptIds.DeptStatusEnum status);

    /**
     * 根据部门老大获取部门信息
     *
     * @param tenantId     企业ID
     * @param manageIdList 部门老大列表
     * @param status       部门状态
     * @return 返回部门列表
     */
    List<DepartmentDto> batchGetDepartmentByPrincipal(String tenantId, List<String> manageIdList, QueryDeptInfoByDeptIds.DeptStatusEnum status);

    /**
     * 获取指定员工的所有所属部门，包括主属部门
     *
     * @param tenantId 企业ID
     * @param userId   员工ID
     * @return 所有所属部门
     */
    List<DepartmentDto> getDepartmentByEmployeeId(String tenantId, String userId);

    /**
     * 批量获取上级部门
     * currentDept -> ancestor(999999), ..., great-great-grandparentDept, great-grandparentDept, grandparentDept, parentDept
     *
     * @param tenantId    企业ID
     * @param deptIds     部门ID列表
     * @param includeSelf 是否包含当前部门
     * @return key：部门ID， value：上级部门列表
     */
    Map<String, List<DepartmentDto>> batchGetUpperDepartment(String tenantId, List<String> deptIds, boolean includeSelf);

    Map<String, List<String>> batchGetUpperDepartmentIds(String tenantId, List<String> deptIds, boolean includeSelf);

    Map<String, List<String>> batchGetUpperDepartmentId(String tenantId, Collection<String> deptIds, boolean includeSelf);

    /**
     * 批量获取所有下级部门(包括下级的下级)
     *
     * @param tenantId 企业ID
     * @param deptIds  部门ID列表
     * @param status   部门状态
     * @param self     是否包括自身
     * @return key: 部门ID， value: 下级部门列表
     */
    Map<String, List<DepartmentDto>> batchGetLowDepartment(String tenantId,
                                                           List<String> deptIds,
                                                           QueryDeptInfoByDeptIds.DeptStatusEnum status,
                                                           boolean self);

    Map<String, List<String>> batchGetLowDepartmentIdsMap(String tenantId,
                                                          List<String> deptIds,
                                                          QueryDeptInfoByDeptIds.DeptStatusEnum status,
                                                          boolean self);

    /**
     * 查询上级部门
     *
     * @param tenantId 企业ID
     * @param deptId   部门ID
     * @param self     是否包含自身部门
     * @return 部门列表
     */
    List<DepartmentDto> getUpperDepartments(String tenantId, String deptId, boolean self);
}
