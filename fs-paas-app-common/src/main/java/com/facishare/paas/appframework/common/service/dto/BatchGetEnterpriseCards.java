package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.checkerframework.checker.units.qual.C;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/12 11:07 上午
 */
public interface BatchGetEnterpriseCards {
    @Data
    @Builder
    @AllArgsConstructor
    class Arg {
        private Integer upstreamTenantId;
        private List<Integer> outerTenantIds;
    }

    @Data
    class Result {
        private Integer errCode;
        private List<Card> data;
        private String errMessage;

        public boolean isSuccess() {
            return errCode == 0;
        }
    }

    @Data
    class Card {
        private Integer outerTenantId;
        private String enterpriseName;
        private String description;
    }


}
