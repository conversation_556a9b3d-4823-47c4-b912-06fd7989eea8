package com.facishare.paas.appframework.common.service.dto;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface BatchGetResponsibleEmployeeByUserId {
    @Data
    @Builder
    class Arg {
        private OrgContext context;
        private Set<String> employeeIds;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<UserInfo> result = Lists.newArrayList();
        private boolean success;
    }
}
