package com.facishare.paas.appframework.common.service.dto;

import com.github.trace.TraceContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * wiki:http://git.firstshare.cn/Infrastructure/fs-register/wikis/validatecode
 * Created by fengjy in 2020/2/4 17:21
 */
public interface VerifyValidateCode {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg{
        String areaCode;
        String mobile;
        String code;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result{
        String verifyValidateCodeEnum;
    }
}
