package com.facishare.paas.appframework.common.service.dto;

import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface BatchQuerySupervisorByUserId {
    @Data
    @Builder
    class Arg {
        private OrgContext context;
        private List<String> userIds;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Map<String, List<UserInfo>> result = Maps.newHashMap();
        private boolean success;
    }
}
