package com.facishare.paas.appframework.common.service;


import com.facishare.fsc.api.model.CreateValidateCode;
import com.facishare.fsc.api.service.CreateImageService;
import com.facishare.paas.appframework.common.service.dto.Captcha;
import com.facishare.paas.appframework.common.service.dto.CaptchaVerify;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.userlogin.api.model.captcha.BuildCodeDto;
import com.facishare.userlogin.api.model.captcha.VerifyCodeDto;
import com.facishare.userlogin.api.service.ImageCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 图形验证码图形（Base64）的生成和校验
 */
@Slf4j
@Service("appCaptchaService")
public class AppCaptchaServiceImpl implements AppCaptchaService {

    private ImageCodeService imageCodeService;

    /**
     * 生成登录图形验证码的验证码（用户登录服务）
     * see spring/common.xml
     *
     * @param imageCodeService from META-INF/fs-user-login-api-dubbo-rest-client.xml
     */
    @Autowired
    public void setImageCodeService(ImageCodeService imageCodeService) {
        this.imageCodeService = imageCodeService;
    }

    private CreateImageService createImageService;

    /**
     * 生成登录图形验证码的图片（文件系统服务）
     * see spring/common.xml
     *
     * @param createImageService spring/common.xml
     */
    @Autowired
    public void setCreateImageService(CreateImageService createImageService) {
        this.createImageService = createImageService;
    }

    @Override
    public Captcha.Result createCaptchaCode(User user, String captchaCode, String captchaId, Integer expireSeconds) {
        // 1. 验证码
        BuildCodeDto.Argument codeBody = new BuildCodeDto.Argument();
        codeBody.setCode(captchaCode);
        codeBody.setId(captchaId);
        codeBody.setExpireSeconds(expireSeconds);
        BuildCodeDto.Result codeRes = imageCodeService.buildCode(codeBody);
        // 2. 图
        CreateValidateCode.Arg imgBody = new CreateValidateCode.Arg();
        imgBody.code = codeRes.getCode();
        CreateValidateCode.Result imgRes = createImageService.createValidateCode(imgBody);
        return Captcha.Result.builder().epxId(codeRes.getId()).data(imgRes.data).build();
    }

    @Override
    public Captcha.Result createCaptchaCode(User user) {
        // 默认验证码有效期：180s
        // captchaCode 和 captchaId 由被调用者生成
        return createCaptchaCode(user, null, null, 180);
    }

    @Override
    public CaptchaVerify.Result verifyCaptchaCode(User user, String captchaCode, String captchaId) {
        VerifyCodeDto.Argument body = new VerifyCodeDto.Argument();
        body.setCode(captchaCode);
        body.setId(captchaId);
        VerifyCodeDto.Result res = imageCodeService.verifyCode(body);
        return CaptchaVerify.Result.builder().success(res.getMatch()).build();
    }
}
