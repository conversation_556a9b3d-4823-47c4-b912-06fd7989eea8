package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.SearchData;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "PAAS-SEARCH", desc = "全局搜索服务", contentType = "application/json")
public interface ObjectSearchServiceProxy {
    @POST(value = "/query", desc = "全局搜索")
    SearchData.Result searchData(@HeaderMap Map<String, String> header, @Body SearchData.Arg arg);
    @POST(value = "/query_name", desc = "全局搜索，")
    SearchDataByName.Result searchDataByName(@HeaderMap Map<String, String> header, @Body SearchData.Arg arg);
}
