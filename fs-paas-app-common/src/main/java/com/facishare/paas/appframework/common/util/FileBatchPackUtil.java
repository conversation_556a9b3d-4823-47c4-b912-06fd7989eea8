package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.common.service.model.FileBatchPack;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 文件批量打包工具类
 * 从 fs-paas-app-metadata 移动到 fs-paas-app-common，作为基础工具类
 * 直接构造新接口需要的结构化参数，避免XML的构造和解析
 * 
 * <AUTHOR>
 */
public class FileBatchPackUtil {

  /**
   * 构建一级目录结构的文档对象
   * 对应 XmlUtil.create1LayerXml
   */
  public static FileBatchPack.BatchFileDocuments create1LayerDocuments(
      IObjectDescribe describe, 
      List<IFieldDescribe> fieldsToExport, 
      List<IObjectData> dataList) {
    return create1LayerDocuments(describe, fieldsToExport, dataList, false);
  }

  public static FileBatchPack.BatchFileDocuments create1LayerDocuments(
      IObjectDescribe describe, 
      List<IFieldDescribe> fieldsToExport, 
      List<IObjectData> dataList, 
      boolean rename) {
    
    FileBatchPack.BatchFileDocuments documents = new FileBatchPack.BatchFileDocuments();
    documents.setWareHouseType("N");
    // 使用 FileExtUtil 中的公共方法
    documents.setDirectoryName(FileExtUtil.filterName(describe.getDisplayName()));
    
    List<FileBatchPack.BatchFileEntity> files = Lists.newArrayList();
    
    dataList.forEach(data -> {
      for (IFieldDescribe fieldDescribe : fieldsToExport) {
        if (Objects.nonNull(data.get(fieldDescribe.getApiName()))) {
          // fullName形式为：filename.Npath
          String[] fullNames = data.get(fieldDescribe.getApiName(), String.class).split("\\|");
          for (String fullName : fullNames) {
            if (StringUtils.isBlank(fullName)) {
              continue;
            }
            String filename = FileExtUtil.getFileName(fullName);
            if (rename) {
              filename = FileExtUtil.renameFileName(data.getName(), filename);
            }
            String path = FileExtUtil.getPath(fullName);
            String signature = FileExtUtil.getSignature(fullName);
            if (!StringUtils.isEmpty(fullName)) {
              FileBatchPack.BatchFileEntity fileEntity = new FileBatchPack.BatchFileEntity();
              fileEntity.setName(filename);
              fileEntity.setPath(path);
              if (StringUtils.isNotEmpty(signature)) {
                fileEntity.setSign(signature);
              }
              files.add(fileEntity);
            }
          }
        }
      }
    });
    
    documents.setFiles(files);
    return documents;
  }

  /**
   * 构建三级目录结构的文档对象
   * 对应 XmlUtil.create3LayerXml
   */
  public static FileBatchPack.BatchFileDocuments create3LayerDocuments(
      IObjectDescribe describe, 
      List<IFieldDescribe> fieldsToExport, 
      List<IObjectData> dataList) {
    
    FileBatchPack.BatchFileDocuments rootDocuments = new FileBatchPack.BatchFileDocuments();
    rootDocuments.setWareHouseType("N");
    rootDocuments.setDirectoryName(FileExtUtil.filterName(describe.getDisplayName()));
    
    List<FileBatchPack.BatchFileDocuments> secondLevelDirs = Lists.newArrayList();
    
    // 以数据主属性为名建立二级目录
    for (IObjectData data : dataList) {
      FileBatchPack.BatchFileDocuments secondLevel = new FileBatchPack.BatchFileDocuments();
      secondLevel.setDirectoryName(FileExtUtil.filterName(data.getName()));
      
      List<FileBatchPack.BatchFileDocuments> thirdLevelDirs = Lists.newArrayList();
      
      // 以导出字段为名建立三级目录
      for (IFieldDescribe field : fieldsToExport) {
        FileBatchPack.BatchFileDocuments thirdLevel = new FileBatchPack.BatchFileDocuments();
        thirdLevel.setDirectoryName(FileExtUtil.filterName(field.getLabel()));
        
        List<FileBatchPack.BatchFileEntity> files = Lists.newArrayList();
        
        //在第三级目录下创建文件
        String names = data.get(field.getApiName(), String.class);
        if (Objects.nonNull(names)) {
          for (String fullName : names.split("\\|")) {
            if (!StringUtils.isEmpty(fullName)) {
              String filename = FileExtUtil.getFileName(fullName);
              String path = FileExtUtil.getPath(fullName);
              String signature = FileExtUtil.getSignature(fullName);
              FileBatchPack.BatchFileEntity fileEntity = new FileBatchPack.BatchFileEntity();
              fileEntity.setName(filename);
              fileEntity.setPath(path);
              if (StringUtils.isNotEmpty(signature)) {
                fileEntity.setSign(signature);
              }
              files.add(fileEntity);
            }
          }
        }
        
        thirdLevel.setFiles(files);
        thirdLevelDirs.add(thirdLevel);
      }
      
      secondLevel.setDirs(thirdLevelDirs);
      secondLevelDirs.add(secondLevel);
    }
    
    rootDocuments.setDirs(secondLevelDirs);
    return rootDocuments;
  }

  /**
   * 构建导出文件和图片的文档对象
   * 对应 XmlUtil.createExportFileAndImage
   */
  public static FileBatchPack.BatchFileDocuments createExportFileAndImageDocuments(
      Map<String, String> pathAndName,
      String fileName,
      List<IObjectDescribe> describes,
      Map<String, List<IFieldDescribe>> fieldsToExport,
      Map<String, List<IObjectData>> dataListMap) {

    FileBatchPack.BatchFileDocuments rootDocuments = new FileBatchPack.BatchFileDocuments();
    rootDocuments.setWareHouseType("N");

    List<FileBatchPack.BatchFileEntity> rootFiles = Lists.newArrayList();
    List<FileBatchPack.BatchFileDocuments> firstLevelDirs = Lists.newArrayList();

    // 添加根级别的文件
    if (StringUtils.isNotBlank(fileName)) {
      FileBatchPack.BatchFileEntity mainFile = new FileBatchPack.BatchFileEntity();
      mainFile.setName(fileName);
      rootFiles.add(mainFile);
    }

    if (CollectionUtils.notEmpty(pathAndName)) {
      pathAndName.forEach((path, name) -> {
        FileBatchPack.BatchFileEntity fileEntity = new FileBatchPack.BatchFileEntity();
        fileEntity.setName(FileExtUtil.fillFileExt(name));
        fileEntity.setPath(path);
        rootFiles.add(fileEntity);
      });
    }

    // 以对象描述名称为名创建一级目录
    describes.forEach(describe -> {
      List<IFieldDescribe> fieldDescribes = fieldsToExport.get(describe.getApiName());
      if (CollectionUtils.empty(fieldDescribes)) {
        return;
      }
      List<IObjectData> dataList = dataListMap.get(describe.getApiName());
      if (CollectionUtils.empty(dataList)) {
        return;
      }

      FileBatchPack.BatchFileDocuments firstLevel = new FileBatchPack.BatchFileDocuments();
      firstLevel.setDirectoryName(FileExtUtil.filterName(describe.getDisplayName()));

      List<FileBatchPack.BatchFileDocuments> secondLevelDirs = Lists.newArrayList();

      // 以数据主属性为名建立二级目录
      dataList.forEach(data -> {
        FileBatchPack.BatchFileDocuments secondLevel = new FileBatchPack.BatchFileDocuments();
        secondLevel.setDirectoryName(FileExtUtil.filterName(data.getName()));

        List<FileBatchPack.BatchFileDocuments> thirdLevelDirs = Lists.newArrayList();

        // 以导出字段为名建立三级目录
        for (IFieldDescribe field : fieldDescribes) {
          FileBatchPack.BatchFileDocuments thirdLevel = new FileBatchPack.BatchFileDocuments();
          thirdLevel.setDirectoryName(FileExtUtil.filterName(field.getLabel()));

          List<FileBatchPack.BatchFileEntity> files = Lists.newArrayList();

          //在第三级目录下创建文件
          String names = data.get(field.getApiName(), String.class);
          if (Objects.nonNull(names)) {
            for (String fullName : names.split("\\|")) {
              if (!StringUtils.isEmpty(fullName)) {
                String filename = FileExtUtil.getFileName(fullName);
                String path = FileExtUtil.getPath(fullName);
                String signature = FileExtUtil.getSignature(fullName);
                FileBatchPack.BatchFileEntity fileEntity = new FileBatchPack.BatchFileEntity();
                fileEntity.setName(filename);
                fileEntity.setPath(path);
                if (StringUtils.isNotEmpty(signature)) {
                  fileEntity.setSign(signature);
                }
                files.add(fileEntity);
              }
            }
          }

          thirdLevel.setFiles(files);
          thirdLevelDirs.add(thirdLevel);
        }

        secondLevel.setDirs(thirdLevelDirs);
        secondLevelDirs.add(secondLevel);
      });

      firstLevel.setDirs(secondLevelDirs);
      firstLevelDirs.add(firstLevel);
    });

    rootDocuments.setFiles(rootFiles);
    rootDocuments.setDirs(firstLevelDirs);
    return rootDocuments;
  }
}
