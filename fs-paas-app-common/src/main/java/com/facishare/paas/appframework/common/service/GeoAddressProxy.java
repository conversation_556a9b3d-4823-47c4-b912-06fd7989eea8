package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.GeoAddress;
import com.facishare.paas.appframework.common.service.dto.GeoAddressDTO;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.GET;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.QueryParam;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(
        value = "GEO_NEW",
        desc = "定位地址解析服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface GeoAddressProxy {

    @GET(value = "/api/v2/geocode/encode", desc = "地址解析到经纬度")
    GeoAddress.RestResult getGeoByAddress(@HeaderMap Map<String, String> header, @QueryParam("address") String address);

    @POST(value = "/api/v2/reverse-geocode/reverse", desc = "经纬度解析到地址")
    GeoAddressDTO.RestResult getAddressByGeo(@HeaderMap Map<String, String> header, @Body GeoAddressDTO.ReverseGeoRequest body);
}
