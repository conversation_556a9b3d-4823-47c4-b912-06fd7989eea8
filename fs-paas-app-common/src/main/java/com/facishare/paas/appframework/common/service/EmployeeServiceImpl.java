package com.facishare.paas.appframework.common.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.facishare.organization.api.model.employee.BatchGetEmployeeIdsByDepartmentId;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.*;
import com.facishare.organization.api.model.employee.result.*;
import com.facishare.organization.api.model.param.GetAllSubordinateEmployeeIds;
import com.facishare.organization.api.model.search.arg.BatchGetEmployeesDtoByDepartmentIdAndMatchNameArg;
import com.facishare.organization.api.model.search.result.BatchGetEmployeesDtoByDepartmentIdAndMatchNameResult;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.organization.api.service.SearchService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ConvertUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.social.personnel.PersonnelObjService;
import com.facishare.social.personnel.model.FindByUserId;
import com.facishare.social.personnel.model.GetPersonnelListByNums;
import com.facishare.social.personnel.model.PersonnelDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.User.SUPPER_ADMIN_USER_ID_INT;

/**
 * 查询员工实现类
 *
 * <AUTHOR>
 * @date 2019/12/26 2:22 下午
 */
@Service("employeeServiceForOrg")
@Slf4j
public class EmployeeServiceImpl implements EmployeeService {
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private SearchService searchService;
    @Autowired
    private PersonnelObjService personnelObjService;

    @Override
    public List<EmployeeDto> batchGetUserInfo(String tenantId, Collection<String> userIds) {
        return batchGetUserInfo(tenantId, userIds, 2);
    }

    @Override
    public List<EmployeeDto> batchGetUserInfo(String tenantId, Collection<String> userIds, int status) {
        if (CollectionUtils.empty(userIds) || !ConvertUtils.isEffectiveId(tenantId)) {
            return Lists.newArrayList();
        }
        List<Integer> employeeIds = ConvertUtils.convertStringCollectionToIntegerList(userIds);
        if (CollectionUtils.empty(employeeIds)) {
            return handleEmployees(userIds, Lists.newArrayList());
        }

        try {
            BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg();
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setEmployeeIds(employeeIds);
            arg.setRunStatus(convertStatusToRunStatus(status));
            BatchGetEmployeeDtoResult result = employeeProviderService.batchGetEmployeeDto(arg);
            return handleEmployees(userIds, result.getEmployeeDtos());
        } catch (Exception e) {
            log.error("batch get user error, tenantId:{}, userIds:{}", tenantId, userIds, e);
            throw new RuntimeException(e);
        }
    }

    private List<EmployeeDto> handleEmployees(Collection<String> userIds, List<EmployeeDto> employeeDtos) {
        List<EmployeeDto> results = CollectionUtils.nullToEmpty(employeeDtos);
        if (userIds.contains(User.SUPPER_ADMIN_USER_ID)) {
            results.add(createSystemEmployee());
        }
        return results;
    }

    private EmployeeDto createSystemEmployee() {
        EmployeeDto employee = new EmployeeDto();
        employee.setEmployeeId(SUPPER_ADMIN_USER_ID_INT);
        employee.setName(I18N.text(I18NKey.SYSTEM));
        employee.setStatus(EmployeeEntityStatus.NORMAL);
        employee.setEmpNum("system");
        return employee;
    }

    @Override
    public EmployeeDto getUserInfo(String tenantId, String userId) {
        try {
            GetEmployeeDtoArg arg = new GetEmployeeDtoArg();
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setEmployeeId(ConvertUtils.convertStringToInteger(userId));
            GetEmployeeDtoResult result = employeeProviderService.getEmployeeDto(arg);
            return result.getEmployeeDto();
        } catch (Exception e) {
            log.error("get user error, tenantId:{}, userId:{}", tenantId, userId, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<EmployeeDto> batchGetUserByNickName(String tenantId, String nickName) {
        try {
            BatchGetEmployeesDtoByDepartmentIdAndMatchNameArg arg = new BatchGetEmployeesDtoByDepartmentIdAndMatchNameArg();
            arg.setDepartmentIds(Lists.newArrayList());
            arg.setIncludeLowDepartment(false);
            arg.setName(nickName);
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(convertStatusToRunStatus(2));
            BatchGetEmployeesDtoByDepartmentIdAndMatchNameResult result =
                    searchService.batchGetEmployeesByDepartmentIdAndMatchName(arg);
            return CollectionUtils.nullToEmpty(result.getEmployeeDtos());
        } catch (Exception e) {
            log.error("get user by name error, tenantId:{}, name:{}", tenantId, nickName, e);
            throw new RuntimeException(e);
        }

    }

    @Override
    public List<EmployeeDto> batchGetUserByNickNames(String tenantId, List<String> nickNames, Integer status) {
        try {
            boolean isMultiLang = AppFrameworkConfig.objectMultiLangGray(tenantId, Utils.PERSONNEL_OBJ_API_NAME);
            GetEmployeeByNamesArg arg = new GetEmployeeByNamesArg();
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setNames(nickNames);
            arg.setRunStatus(convertStatusToRunStatus(status));
            GetEmployeeByNamesResult result;
            if (isMultiLang) {
                arg.setLanguageType(I18N.getContext().getLanguage());
                result = employeeProviderService.getEmployeeByNamesMultiLanguage(arg);
            } else {
                result = employeeProviderService.getEmployeeByNames(arg);
            }
            return CollectionUtils.nullToEmpty(result.getEmployeeDtos());
        } catch (Exception e) {
            log.error("batch get user by nick name list error, tenantId:{}, nickNames:{}", tenantId, nickNames, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<PersonnelDto> batchGetUserByCodes(User user, List<String> codes) {
        try {
            GetPersonnelListByNums.Argument arg = new GetPersonnelListByNums.Argument();
            arg.setTenantId(user.getTenantId());
            arg.setEmpNums(codes);
            GetPersonnelListByNums.Result result = personnelObjService.getPersonnelListByNums(arg);
            return CollectionUtils.nullToEmpty(result.getList());
        } catch (Exception e) {
            log.error("batchGetUserByCodes error, tenantId:{}, nickNames:{}", user.getUserId(), codes, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<String> batchGetEmployeeIdsByDeptIds(String tenantId,
                                                     List<String> deptIds,
                                                     Integer userStatus,
                                                     boolean includeLowDepartment) {
        if (CollectionUtils.empty(deptIds)) {
            return Lists.newArrayList();
        }
        try {
            BatchGetEmployeeIdsByDepartmentId.Arg arg = new BatchGetEmployeeIdsByDepartmentId.Arg();
            arg.setDepartmentIds(ConvertUtils.batchConvertStringToInteger(deptIds));
            arg.setIncludeLowDepartment(includeLowDepartment);
            arg.setMainDepartment(MainDepartment.ALL);
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(convertStatusToRunStatus(userStatus));
            BatchGetEmployeeIdsByDepartmentId.Result result = employeeProviderService.batchGetEmployeeIdsByDepartmentId(arg);
            return ConvertUtils.batchConvertIntegerToString(result.getEmployeeIds());
        } catch (Exception e) {
            log.error("error get employee ids, tenantId:{}, deptIds:{}", tenantId, deptIds, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<EmployeeDto> querySubordinatesByUserId(String tenantId, String userId, Integer status, boolean cascade) {
        if (StringUtils.isBlank(userId)) {
            return Lists.newArrayList();
        }
        if (cascade) {
            GetAllSubordinateEmployeesDtoArg arg = new GetAllSubordinateEmployeesDtoArg();
            arg.setEmployeeId(ConvertUtils.convertStringToInteger(userId));
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(convertStatusToRunStatus(status));
            GetAllSubordinateEmployeesDtoResult result1 = employeeProviderService.getAllSubordinateEmployees(arg);
            return CollectionUtils.nullToEmpty(result1.getEmployeeDtos());
        } else {
            GetSubordinateEmployeesDtoArg arg = new GetSubordinateEmployeesDtoArg();
            arg.setEmployeeId(ConvertUtils.convertStringToInteger(userId));
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(convertStatusToRunStatus(status));
            GetSubordinateEmployeesDtoResult result2 = employeeProviderService.getSubordinateEmployees(arg);
            return CollectionUtils.nullToEmpty(result2.getEmployeeDtos());
        }
    }

    @Override
    public List<String> querySubordinateIdsByUserId(String tenantId, String userId, Integer status, boolean cascade) {
        if (StringUtils.isBlank(userId)) {
            return Lists.newArrayList();
        }
        if (cascade) {
            GetAllSubordinateEmployeeIds.Arg arg = new GetAllSubordinateEmployeeIds.Arg();
            arg.setEmployeeId(ConvertUtils.convertStringToInteger(userId));
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(convertStatusToRunStatus(status));
            GetAllSubordinateEmployeeIds.Result result = employeeProviderService.getAllSubordinateEmployeeIds(arg);
            return ConvertUtils.batchConvertIntegerToString(result.getEmployeeIds());
        }
        GetSubordinateEmployeesDtoArg arg = new GetSubordinateEmployeesDtoArg();
        arg.setEmployeeId(ConvertUtils.convertStringToInteger(userId));
        arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
        arg.setRunStatus(convertStatusToRunStatus(status));
        GetSubordinateEmployeesDtoResult result = employeeProviderService.getSubordinateEmployees(arg);

        return CollectionUtils.nullToEmpty(result.getEmployeeDtos()).stream()
                .map(it -> ConvertUtils.integerToString(it.getEmployeeId()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, List<EmployeeDto>> batchGetAllEmployeeLeaderMap(String tenantId, Collection<String> userIds,
                                                                        Integer status, int type) {
        if (CollectionUtils.empty(userIds)) {
            return Maps.newHashMap();
        }
        try {
            Set<String> filteredUserIds = userIds.stream().filter(id -> id.length() < 8).collect(Collectors.toSet());
            BatchGetAllEmployeeLeaderMapArg arg = new BatchGetAllEmployeeLeaderMapArg();
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setEmployeeIds(ConvertUtils.convertStringCollectionToIntegerList(filteredUserIds));
            arg.setRunStatus(convertStatusToRunStatus(status));
            arg.setType(type);
            BatchGetAllEmployeeLeaderMapResult result = employeeProviderService.batchGetAllEmployeeLeaderMap(arg);
            return handleEmployeeLeader(userIds, result.getEmployeeDto());
        } catch (Exception e) {
            log.error("batch get all employee leader map error, tenantId:{}, userIds:{}", tenantId, userIds, e);
            throw new RuntimeException(e);
        }
    }

    private Map<Integer, List<EmployeeDto>> handleEmployeeLeader(Collection<String> userIds, Map<Integer, List<EmployeeDto>> employeeDto) {
        if (userIds.contains(User.SUPPER_ADMIN_USER_ID)) {
            employeeDto.put(Integer.valueOf(User.SUPPER_ADMIN_USER_ID), Collections.emptyList());
        }
        return employeeDto;
    }

    @Override
    public Map<Integer, List<EmployeeDto>> getEmployeesByDepartment(String tenantId,
                                                                    Collection<String> deptIds,
                                                                    boolean includeLowDepartment) {
        return getEmployeesByDepartment(tenantId, deptIds, includeLowDepartment, null, null);

    }

    @Override
    public Map<Integer, List<EmployeeDto>> getEmployeesByDepartment(String tenantId,
                                                                    Collection<String> deptIds,
                                                                    boolean includeLowDepartment,
                                                                    Integer userStatus,
                                                                    Integer deptType) {
        if (CollectionUtils.empty(deptIds)) {
            return Maps.newHashMap();
        }
        RunStatus runStatus = convertStatusToRunStatus(userStatus);
        MainDepartment mainDepartment = convertDeptTypeToMainDepartType(deptType);
        try {
            BatchGetEmployeeMapByDepartmentIdArg arg = new BatchGetEmployeeMapByDepartmentIdArg();
            arg.setDepartmentIds(ConvertUtils.batchConvertStringToInteger(deptIds));
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setIncludeLowDepartment(includeLowDepartment);
            arg.setMainDepartment(mainDepartment);
            arg.setRunStatus(runStatus);
            BatchGetEmployeeMapByDepartmentIdResult result = employeeProviderService.batchGetEmployeeMapByDepartmentId(arg);
            return CollectionUtils.nullToEmpty(result.getEmployeeMap());
        } catch (Exception e) {
            log.error("get employee by deptId error,tenantId:{}, deptIds:{}", tenantId, deptIds);
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getEmployeeRegionId(User user) {
        FindByUserId.Argument argument = new FindByUserId.Argument();
        argument.setTenantId(user.getTenantId());
        argument.setUserId(user.getUserId());
        Optional<PersonnelDto> personnelInfo = personnelObjService.findByUserIdUseCacheChain(argument);
        //区域默认为中国
        return personnelInfo.map(PersonnelDto::getRegion).orElse("zh-cn");
    }

    @Override
    public EmployeeDto getEmployeeByMobile(String tenantId, String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return null;
        }
        int tenantIdInt = 0;
        try {
            tenantIdInt = Integer.parseInt(tenantId);
        } catch (NumberFormatException e) {
            log.warn("tenantId is not number,tenantId:{}", tenantId);
            return null;
        }

        GetEmployeesDtoByEnterpriseAndMobileArg arg = new GetEmployeesDtoByEnterpriseAndMobileArg();
        arg.setMobile(mobile);
        arg.setEnterpriseId(tenantIdInt);
        try {
            GetEmployeesDtoByEnterpriseAndMobileResult result = employeeProviderService.getEmployeeByEnterpriseAndMobile(arg);
            if (Objects.isNull(result)) {
                return null;
            }
            return result.getEmployeeDto();
        } catch (Exception e) {
            log.error("get employee by mobile error,tenantId:{}, mobile:{}", tenantIdInt, mobile, e);
            throw new RuntimeException(e);
        }
    }

    private MainDepartment convertDeptTypeToMainDepartType(Integer type) {
        if (Objects.isNull(type)) {
            return MainDepartment.ALL;
        }
        if (1 == type) {
            return MainDepartment.MAIN;
        }
        if (0 == type) {
            return MainDepartment.VICE;
        }
        return MainDepartment.ALL;
    }

    /*
     *  将数字形式的人员状态转为组织架构使用的RunStatus
     */
    private RunStatus convertStatusToRunStatus(Integer status) {
        if (Objects.isNull(status)) {
            return RunStatus.ALL;
        }
        switch (status) {
            case 0:
                return RunStatus.ACTIVE;
            case 1:
                return RunStatus.STOP;
            default:
                return RunStatus.ALL;
        }
    }
}
