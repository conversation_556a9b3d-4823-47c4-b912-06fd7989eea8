package com.facishare.paas.appframework.common.service;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SearchDataByName {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String tenantId;
        String userId;
        String keyword;
        Set<String> weightApiName;
        Set<String> searchApiNames;
        int size;
        String outTenantId;
        String outUserId;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        Integer totalSize;
        List<SearchDataByNameData> nameResults;
        Map<String, List<String>> apiIdsMap;
    }

    @Data
    class SearchDataByNameData {
        String apiName;
        String dataId;
    }
}
