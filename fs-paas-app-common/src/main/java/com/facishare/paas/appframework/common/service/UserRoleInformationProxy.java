package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.QueryRoleInfoByCodes;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "PAAS-PRIVILEGE", desc = "用户角色服务", contentType = "application/json")
public interface UserRoleInformationProxy {
    @POST(value = "/queryRoleInfoWithCodes", desc = "多roleCode查询角色信息")
    QueryRoleInfoByCodes.Result queryRoleInfoWithCodes(@Body QueryRoleInfoByCodes.Arg arg, @HeaderMap Map<String, String> header);
}
