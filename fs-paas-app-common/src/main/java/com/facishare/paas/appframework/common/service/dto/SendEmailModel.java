package com.facishare.paas.appframework.common.service.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.Set;

/**
 * Created by linqy on 2018/01/24
 */
public interface SendEmailModel {

    @Data
    @Builder
    class Arg {
        @SerializedName(value = "userid_list")
        private Set<String> userid_list;
        @SerializedName(value = "email_list")
        private Set<String> email_list;
        @SerializedName(value = "template_id")
        private String template_id;
        @SerializedName(value = "obj_data_id")
        private String obj_data_id;
        @SerializedName(value = "obj_api_name")
        private String obj_api_name;
        @SerializedName("forCalc")
        private Boolean forCalc;
    }

    @Data
    class Result {
        private Object result;
        private String msg;
        private Integer code;

    }
}
