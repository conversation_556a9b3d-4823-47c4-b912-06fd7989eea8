package com.facishare.paas.appframework.common.mq;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.fxiaoke.rocketmq.acl.AutoConfAclClientRPCHook;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.UUID;

/**
 * RocketMQ消息生产者
 * <p>
 * Created by liyiguang on 2018/1/23.
 */
@Slf4j
@Deprecated
public class RocketMQMessageSender {

    private String configName;

    private Config config;
    private DefaultMQProducer defaultMQProducer;
    private volatile boolean shutdown = true;

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    @PostConstruct
    public void init() {
        ConfigFactory.getInstance().getConfig(configName, conf -> {
            try {
                reload(conf);
            } catch (Exception e) {
                throw new RocketMQException(SystemErrorCode.MQ_INIT_ERROR, e);
            }
        });
    }

    public void sendMessage(Message message) {
        if (shutdown) {
            throw new RocketMQException(SystemErrorCode.MQ_SHUT_DOWN_ERROR);
        }
        doSendMessage(message);
    }

    public void sendMessage(Message message, int selectorHash) {
        if (shutdown) {
            throw new RocketMQException(SystemErrorCode.MQ_SHUT_DOWN_ERROR);
        }
        doSendMessage(message, selectorHash);
    }

    public void sendMessage(byte[] messageData) {
        Message message = new Message(config.getTopic(), config.getTags(), messageData);
        sendMessage(message);
    }

    public void sendMessage(byte[] messageData, int selectorHash) {
        Message message = new Message(config.getTopic(), config.getTags(), messageData);
        sendMessage(message, selectorHash);
    }

    private void doSendMessage(Message message, int selectorHash) {
        try {
            SendResult result = defaultMQProducer.send(message, (mqs, msg, arg) -> {
                int selectHash = (int) arg;
                int index = selectHash % mqs.size();
                return mqs.get(index);
            }, selectorHash);
            SendStatus status = result.getSendStatus();
            if (!status.equals(SendStatus.SEND_OK)) {
                throw new RocketMQException(SystemErrorCode.MQ_ERROR);
            }
            log.debug("doSendMessage OK,topic:{} tags:{}", message.getTopic(), message.getTags());
        } catch (Exception e) {
            log.error("send message failed!", e);
            Thread.currentThread().interrupt();
            throw new RocketMQException(SystemErrorCode.MQ_ERROR);
        }
    }

    private void doSendMessage(Message message) {
        try {
            SendResult result = defaultMQProducer.send(message);
            SendStatus status = result.getSendStatus();
            if (!status.equals(SendStatus.SEND_OK)) {
                throw new RocketMQException(SystemErrorCode.MQ_ERROR);
            }

            log.debug("doSendMessage OK,topic:{} tags:{}", message.getTopic(), message.getTags());
        } catch (Exception e) {
            log.error("send message failed!", e);
            Thread.currentThread().interrupt();
            throw new RocketMQException(SystemErrorCode.MQ_ERROR);
        }
    }


    private void reload(IConfig conf) {
        String content = new String(conf.getContent());
        if (Strings.isNullOrEmpty(content)) {
            log.error("{} config content is empty", configName);
            throw new RocketMQException(SystemErrorCode.MQ_ERROR);
        }
        this.config = JSON.parseObject(content, Config.class);
        log.info("reload config:{}", config);

        if (defaultMQProducer == null) {
            createProducer(config);
        } else {
            shutdown();
            createProducer(config);
        }
    }

    private void createProducer(Config config) {
        try {
            this.defaultMQProducer = new DefaultMQProducer(config.getProducerGroup(), new AutoConfAclClientRPCHook("rocketmq-acl"));
            defaultMQProducer.setNamesrvAddr(config.getNameServer());
            if (config.getMaxMessageSize() > 0) {
                defaultMQProducer.setMaxMessageSize(config.getMaxMessageSize());
            }
            defaultMQProducer.setInstanceName(UUID.randomUUID().toString());
            defaultMQProducer.start();
            shutdown = false;
        } catch (MQClientException e) {
            log.error("create RocketMQ producer failed!", e);
        }
    }

    private void shutdown() {
        shutdown = true;
        if (defaultMQProducer != null) {
            try {
                this.defaultMQProducer.shutdown();
                this.defaultMQProducer = null;
            } catch (Exception e) {
                log.error("shutdown RocketMQ sender failed!", e);
            }
        }
    }

    @Data
    public static class Config {
        String nameServer;
        String producerGroup; //仅仅是标识使用
        String topic;
        int maxMessageSize;
        List<String> tags;

        public String getTags() {
            if (CollectionUtils.notEmpty(tags)) {
                return Joiner.on(" ").skipNulls().join(tags);
            }
            return null;
        }
    }

}
