package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface SubmitJob {
    @Data
    class Arg {
        List<JobInfo> jobList;
    }

    @Data
    @Builder
    class JobInfo {
        private String jobType;
        private String jobParam;
        private String objectDescribeApiName;
        private String jobGroup;
        /**
         * job_schedule producer mq message中dataIdList的限定条件
         */
        private String wheresList;
        /**
         * 任务的开始时间
         */
        private Long startTime;
        /**
         * 需要处理的数据总数
         */
        private int predictDataNum;

        public JobInfo copy() {
            return JobInfo.builder()
                    .jobType(jobType)
                    .jobParam(jobParam)
                    .objectDescribeApiName(objectDescribeApiName)
                    .jobGroup(jobGroup)
                    .wheresList(wheresList)
                    .startTime(startTime)
                    .predictDataNum(predictDataNum)
                    .build();
        }

        public JobInfo copyWithJobParam(String jobParam) {
            JobInfo result = copy();
            result.setJobParam(jobParam);
            return result;
        }
    }

    @Data
    class Result {
        private int code;
        private String message;

        private JobData data;

        public boolean isSuccess() {
            return code == 0 && data != null;
        }

        public String getFirstJobId() {
            if (!isSuccess()) {
                return null;
            }
            return getData().getFirstJobId();
        }
    }

    @Data
    class JobData {
        private List<String> jobIdList;

        public String getFirstJobId() {
            return jobIdList.get(0);
        }
    }
}
