package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/12/23 3:29 下午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OutUserInfo {
    private String id;
    private String tenantId;
    private String name;
    private String profile;
    private Integer status;

    public boolean disabled() {
        return !Objects.equals(status, 0);
    }
}
