package com.facishare.paas.appframework.common.util;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;

/**
 * Created by liyiguang on 2017/11/20.
 */
public enum ObjectLockStatus {

    LOCK("1", I18NKey.action_lock),
    UNLOCK("0", I18NKey.action_unlock);

    ObjectLockStatus(String status, String label) {
        this.status = status;
        this.label = label;
    }

    private String status;
    private String label;

    public String getStatus() {
        return status;
    }

    public String getLabel() {
        return I18N.text(label);
    }

    public static final String LOCK_STATUS_API_NAME = "lock_status";
    public static final String LOCK_RULE_API_NAME = "lock_rule";
    public static final String LOCK_USER_API_NAME = "lock_user";
}
