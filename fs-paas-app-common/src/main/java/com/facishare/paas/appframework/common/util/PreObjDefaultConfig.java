package com.facishare.paas.appframework.common.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.math.NumberUtils;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class PreObjDefaultConfig {
    private static Map<String, Map<String, Object>> PRE_OBJ_DEFAULT_CONFIG;

    private static final String KEY_API_NAME = "describe_api_name";

    private static final String KEY_ICON_SLOT = "icon_slot";

    static {
        ConfigFactory.getConfig("fs-webpage-iconSlot-preObject", iConfig -> {
            String content = new String(iConfig.getContent(), Charset.defaultCharset());
            List<Map<String, Object>> conf = JacksonUtils.fromJson(content, new TypeReference<List<Map<String, Object>>>() {
            });
            if (CollectionUtils.notEmpty(conf)) {
                PRE_OBJ_DEFAULT_CONFIG = conf.stream().collect(Collectors.toMap(m -> (String) m.get(KEY_API_NAME), m -> m, (a, b) -> a));
            } else {
                PRE_OBJ_DEFAULT_CONFIG = Maps.newHashMap();
            }
        });

    }

    public static Map<String, Object> getConfByApi(String objApi) {
        Map<String, Object> config = PRE_OBJ_DEFAULT_CONFIG.get(objApi);
        if (config == null) {
            config = Maps.newHashMap();
            config.put(KEY_ICON_SLOT, 1);
        } else {
            config.putIfAbsent(KEY_ICON_SLOT, 1);
        }
        return config;
    }

    /**
     * 先配置 fs-webpage-iconSlot-preObject<br>
     * 该对象是否配置了 icon，并返回
     * @param objApi 对象 API
     * @return 如果不存在 icon 配置则返回 -1，否则返回对应 icon_slot
     */
    public static int existIconSlotAndGet(String objApi) {
        if (!PRE_OBJ_DEFAULT_CONFIG.containsKey(objApi)) {
            return -1;
        }

        Map<String, Object> config = PRE_OBJ_DEFAULT_CONFIG.get(objApi);
        if (!config.containsKey(KEY_ICON_SLOT)) {
            return -1;
        }

        String iconStr = String.valueOf(config.get(KEY_ICON_SLOT));
        if (!NumberUtils.isDigits(iconStr)) {
            return -1;
        }
        Number slotNum = NumberUtils.createNumber(iconStr);
        return slotNum.intValue();
    }
}
