package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by renlb on 2018/5/4.
 */
public interface QueryMemberInfosByDeptIds {

    @Data
    @Builder
    class Arg{
        private OrgContext context;
        private List<String> deptIds;
        //true:级联查询下属部门的员工,false:只查询部门直属的员工
        private Boolean includeLowDept;
        //null:所有状态, 0:启用状态,1:停用状态
        private Integer userStatus;
        //null:所有关系, 0:附属部门,1:主属部门
        private Integer deptUserType;
    }

    @Data
    class Result{
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Map<String,List<Member>> result;
        private boolean success;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Member {
        private String id;
        private String tenantId;
        private String name;
        private String position;
        private String picAddr;
        private String email;
        private String nickname;
        private String title;
        private String supervisorId;
        private String phone;
        private int status;
        private String description;
        private Long createTime;
        private Long modifyTime;
    }
}
