package com.facishare.paas.appframework.common.service.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/7/13
 */
@Data
public class ManageGroup {
    private final boolean allSupport;
    private final ManageGroupType type;
    private final String parentApiName;
    private final Set<String> supportApiNames;

    private static final Joiner joiner = Joiner.on("||").skipNulls();

    public ManageGroup(boolean all, ManageGroupType type, String parentApiName, Collection<String> supportApiNames) {
        this.allSupport = all;
        this.type = type;
        this.parentApiName = parentApiName;
        this.supportApiNames = CollectionUtils.empty(supportApiNames) ? Collections.emptySet() : ImmutableSet.copyOf(supportApiNames);
    }

    public static String buildSupportApiName(String parentApiName, String apiName, ManageGroupType manageGroupType) {
        if (manageGroupType == ManageGroupType.OBJECT) {
            return apiName;
        }
        return joiner.join(parentApiName, apiName);
    }

    public boolean support(String apiName) {
        if (allSupport) {
            return true;
        }
        return supportApiNames.contains(buildSupportApiName(parentApiName, apiName, type));
    }

    public static boolean support(ManageGroup manageGroup, String apiName) {
        if (Objects.isNull(manageGroup)) {
            return true;
        }
        return manageGroup.support(apiName);
    }

    public Set<String> getApiNames() {
        if (allSupport) {
            return Sets.newHashSet();
        }
        if (type == ManageGroupType.OBJECT) {
            return supportApiNames;
        }
        return supportApiNames.stream()
                .map(it -> StringUtils.substringAfterLast(it, "||"))
                .collect(Collectors.toSet());
    }
}
