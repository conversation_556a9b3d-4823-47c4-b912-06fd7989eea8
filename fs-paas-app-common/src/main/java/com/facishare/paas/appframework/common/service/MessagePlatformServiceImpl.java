package com.facishare.paas.appframework.common.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.paas.appframework.common.service.dto.SendTextMessage;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.Objects;

@Slf4j
@Service
public class MessagePlatformServiceImpl implements MessagePlatformService {
    public static final String WEB_URL_PATTERN = "fs://CRM/objectDetail/{0}?'{'\"apiName\":\"{0}\", \"dataId\":\"{1}\",\"handlerSelector\":\"pushObjectDetailVC:\",\"pushObjectDetailVC\":'{'\"kApiNameForDetail\":\"{0}\",\"kDataIdForDetail\":\"{1}\"'}}'";
    public static final String MOBILE_URL_PATTERN = "cml://CRM/ListBatchEditRemind?'{'\"remindRecordInfo\":{0}'}'";
    public static final String BATCH_OPERATE_LOG = "batchOperateLog";
    @Autowired
    private MessagePlatformProxy platformProxy;

    @Override
    public void sendTextMessage(User user, CRMNotification arg) {
        if (Strings.isNullOrEmpty(arg.getFixContent2ID())) {
            sendTextMessage(user, buildTextMessageArg(user, arg));
        } else {
            sendTextLinkMessage(user, buildTextMessageWithLinkArg(user, arg));
        }
    }

    @Override
    public void sendPlatFormMessage(User user, NewCrmNotification arg) {
        if (arg.isTextMessage()) {
            sendTextMessage(user, buildSendTextMessageArg(user, arg));
        } else {
            sendTextLinkMessage(user, buildSendLinkMessageArg(user, arg));
        }
    }

    private SendTextMessage.Result sendTextMessage(User user, SendTextMessage.Arg arg) {
        SendTextMessage.Result result = platformProxy.sendTextMessage(RestUtils.buildHeaders(user), arg);
        if (Objects.nonNull(result) && !result.isSuccess()) {
            log.warn("sendTextMessage fail, user:{}, arg:{}, result:{}", user, arg, result);
        }
        return result;
    }

    private SendTextMessage.Result sendTextLinkMessage(User user, SendTextMessage.Arg arg) {
        SendTextMessage.Result result = platformProxy.sendTextLinkMessage(RestUtils.buildHeaders(user), arg);
        if (Objects.nonNull(result) && !result.isSuccess()) {
            log.warn("sendTextLinkMessage fail, user:{}, arg:{}, result:{}", user, arg, result);
        }
        return result;
    }

    private SendTextMessage.Arg buildTextMessageWithLinkArg(User user, CRMNotification arg) {
        SendTextMessage.Arg result = SendTextMessage.Arg.builder()
                .ei(user.getTenantIdInt())
                .messageContent(arg.getContent())
                .receiverChannelData(ReceiverChannelData.builder().appId("CRM").build().toJSONString())
                .receiverChannelType(ReceiverChannelType.NOTIFICATION.getValue())
                .receiverIds(arg.getReceiverIds())
                .title(arg.getTitle())
                .objectApiName(arg.getObjectApiName())
                .objectId(arg.getDataId())
                .build();

        if (arg.getOutEI() != null && arg.getOutEI() != 0) {
            result.setOutEmployees(arg.getReceiverIds());
        }
        generateUrlType(result, arg);
        return result;
    }

    private void generateUrlType(SendTextMessage.Arg result, CRMNotification arg) {
        if (Objects.equals(CRMNotification.CUSTOM_REMIND_JOB_SCHEDULE_TYPE, arg.getRemindRecordType())) {
            result.setGenerateUrlType(GenerateUrlType.NO_GENERATE.getValue());
            String webUrl = MessageFormat.format(WEB_URL_PATTERN, String.format("%s|%s", arg.getObjectApiName(), BATCH_OPERATE_LOG), arg.getDataId());
            JSONArray jsonArray = JSONArray.parseArray(arg.getFixContent2ID());
            String mobileUrl = MessageFormat.format(MOBILE_URL_PATTERN, jsonArray.getJSONObject(0).toJSONString());
            result.setInnerPlatformWebUrl(webUrl);
            result.setInnerPlatformMobileUrl(mobileUrl);
            return;
        }
        result.setGenerateUrlType(GenerateUrlType.OBJECT_DETAIL.getValue());
    }

    private SendTextMessage.Arg buildTextMessageArg(User user, CRMNotification arg) {
        SendTextMessage.Arg result = SendTextMessage.Arg.builder()
                .ei(user.getTenantIdInt())
                .messageContent(arg.getContent())
                .receiverChannelData(ReceiverChannelData.builder().appId("CRM").build().toJSONString())
                .receiverChannelType(ReceiverChannelType.NOTIFICATION.getValue())
                .receiverIds(arg.getReceiverIds())
                .title(arg.getTitle())
                .build();
        if (arg.getOutEI() != null && arg.getOutEI() != 0) {
            result.setOutEmployees(arg.getReceiverIds());
        }
        return result;
    }


    private SendTextMessage.Arg buildSendTextMessageArg(User user, NewCrmNotification arg) {
        SendTextMessage.Arg result = SendTextMessage.Arg.builder()
                .ei(user.getTenantIdInt())
                .receiverIds(arg.getReceiverIDs())
                .departments(arg.getDepartments())
                .outEmployees(arg.getOutEmployees())
                .title(arg.getTitle())
                .titleInfo(arg.getTitleInfo())
                .messageContent(arg.getFullContent())
                .messageContentInfo(arg.getFullContentInfo())
                .receiverChannelData(ReceiverChannelData.builder().appId("CRM").build().toJSONString())
                .receiverChannelType(ReceiverChannelType.NOTIFICATION.getValue())
                .build();
        if (user.isOutUser()) {
            result.setOutEmployees(arg.getReceiverIDs());
        }
        return result;
    }

    private SendTextMessage.Arg buildSendLinkMessageArg(User user, NewCrmNotification arg) {
        SendTextMessage.Arg result = SendTextMessage.Arg.builder()
                .ei(user.getTenantIdInt())
                .messageContent(arg.getFullContent())
                .messageContentInfo(arg.getFullContentInfo())
                .receiverChannelData(ReceiverChannelData.builder().appId("CRM").build().toJSONString())
                .receiverChannelType(ReceiverChannelType.NOTIFICATION.getValue())
                .receiverIds(arg.getReceiverIDs())
                .departments(arg.getDepartments())
                .outEmployees(arg.getOutEmployees())
                .title(arg.getTitle())
                .titleInfo(arg.getTitleInfo())
                .objectApiName(arg.getObjectApiName())
                .objectId(arg.getObjectId())
                .build();

        if (user.isOutUser()) {
            result.setOutEmployees(arg.getReceiverIDs());
        }
        if (Objects.equals(NewCrmNotification.CUSTOM_REMIND_JOB_SCHEDULE_TYPE, arg.getType())) {
            result.setGenerateUrlType(GenerateUrlType.NO_GENERATE.getValue());
            String webUrl = MessageFormat.format(WEB_URL_PATTERN, String.format("%s|%s", arg.getObjectApiName(), BATCH_OPERATE_LOG), arg.getObjectId());
            String mobileUrl = MessageFormat.format(MOBILE_URL_PATTERN, JSON.toJSONString(arg.getUrlParameter()));
            result.setInnerPlatformWebUrl(webUrl);
            result.setInnerPlatformMobileUrl(mobileUrl);
        } else {
            result.setGenerateUrlType(GenerateUrlType.OBJECT_DETAIL.getValue());
        }
        return result;
    }
}
