package com.facishare.paas.appframework.common.util;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Objects;

public class NumberUtil {

    /** Reusable Integer constant for zero. */
    public static final Integer INTEGER_ZERO = Integer.valueOf(0);
    /** Reusable Integer constant for one. */
    public static final Integer INTEGER_ONE = NumberUtils.INTEGER_ONE;

    /** Reusable Integer constant for twenty. */
    public static final Integer INTEGER_TWENTY = Integer.valueOf(20);

    /** Reusable Integer constant for one thousand. */
    public static final Integer INTEGER_ONE_THOUSAND = Integer.valueOf(1000);

    /** Reusable Integer constant for two thousand. */
    public static final Integer INTEGER_TWO_THOUSAND = Integer.valueOf(2000);

    public static  <T extends Number> T firstNonNull(final T... values) {
        if (values != null) {
            for (final T val : values) {
                if ((Objects.nonNull(val))) {
                    return val;
                }
            }
        }
        return null;
    }
}
