package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.SendEmailModel;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

/**
 * Created by linqy on 2018/01/24
 */
@RestResource(
        value = "SendEmail",
        desc = "send mail",
        contentType = "application/json"
)
public interface SendEmailProxy {

    @POST(value = "/send", desc = "send crm message")
    SendEmailModel.Result sendEmail(@HeaderMap Map<String, String> header, @Body SendEmailModel.Arg arg);
}
