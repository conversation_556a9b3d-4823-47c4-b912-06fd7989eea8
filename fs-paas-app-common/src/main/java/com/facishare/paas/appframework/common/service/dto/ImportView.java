package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

public interface ImportView {

    @Data
    class Arg {
        private String filePath;
        private Boolean multiSheets;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<SheetContent> sheetContents;
    }

    @Data
    class SheetContent {
        private String sheetName;
        private String sheetIndex;
        private List<ExcelCol> excelCols;
    }

    @Data
    class ExcelCol {
        private String colIndex;
        private String colName;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ExcelCol excelCol = (ExcelCol) o;
            return Objects.equals(colName, excelCol.colName);
        }

        @Override
        public int hashCode() {
            return Objects.hash(colName);
        }
    }
}
