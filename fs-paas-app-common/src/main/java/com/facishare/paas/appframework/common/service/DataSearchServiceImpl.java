package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.SearchData;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class DataSearchServiceImpl implements DataSearchService {
    @Autowired
    private ObjectSearchServiceProxy objectSearchServiceProxy;

    @Override
    public SearchData.Result searchData(SearchData.Arg arg, User user) {
        SearchData.Result emptyResult = SearchData.Result.builder()
                .totalSize(0)
                .apiIdsMap(Maps.newHashMap())
                .build();
        if(Objects.isNull(arg)) {
            log.warn("SearchData.Arg is null");
            return emptyResult;
        }

        arg.setIncludeNameResults(false);
        arg.setIncludeApiIdsMap(true);
        arg.setAccurateQuery(arg.getAccurateQuery());
        SearchData.Result result = objectSearchServiceProxy.searchData(RestUtils.buildHeaders(user), arg);
        return Objects.isNull(result) ?
                emptyResult : result;
    }

    @Override
    public SearchDataByName.Result searchDataByName(SearchData.Arg arg, User user) {
        SearchDataByName.Result result = objectSearchServiceProxy.searchDataByName(RestUtils.buildHeaders(user), arg);
        return Objects.isNull(result) ?
                SearchDataByName.Result.builder()
                        .totalSize(0)
                        .nameResults(Lists.newArrayList())
                        .build() : result;
    }
}
