package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface QueryMembersIdByDeptIds {
    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> idList;
        /**
         * null:所有，0:启用,1:停用
         */
        private Integer userStatus;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<String> result;
        private boolean success;
    }
}
