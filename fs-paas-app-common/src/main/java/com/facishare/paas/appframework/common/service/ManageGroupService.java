package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupContainer;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ManageGroupException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2023/7/13
 */
@Slf4j
@Service
public class ManageGroupService {
    private static final String APP_ID = "facishare-system";

    private PlatServiceProxy platServiceProxy;

    @Autowired
    public void setPlatProviderServiceProxy(PlatServiceProxy PlatServiceProxy) {
        this.platServiceProxy = PlatServiceProxy;
    }

    public ManageGroup queryManageGroup(User user, String parentApiName, ManageGroupType manageGroupType) {
        return queryManageGroup(user, parentApiName, manageGroupType, false);
    }

    public ManageGroup queryManageGroup(User user, String parentApiName, ManageGroupType manageGroupType, boolean useOldMangeGroupGray) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.getTenantId())) {
            if (!(useOldMangeGroupGray && oldMangeGroupGray(user, manageGroupType))) {
                return new ManageGroup(true, manageGroupType, parentApiName, Collections.emptyList());
            }
        }
        if (user.isSupperAdmin()) {
            return new ManageGroup(true, manageGroupType, parentApiName, Collections.emptyList());
        }
        FindManageGroupObjects.Arg arg = new FindManageGroupObjects.Arg();
        arg.setTenantId(user.getTenantId());
        arg.setUserId(user.getUserId());
        arg.setAppId(APP_ID);
        if (!Strings.isNullOrEmpty(parentApiName)) {
            arg.setParentApiName(parentApiName);
        }
        arg.setType(manageGroupType.getType());

        Map<String, String> headers = RestUtils.buildHeaders(user);
        try {
            FindManageGroupObjects.RestResult restResult = platServiceProxy.findManageGroupObjects(headers, arg);
            if (restResult.isSuccess()) {
                FindManageGroupObjects.Result result = restResult.getResult();
                return new ManageGroup(result.isAllResult(), manageGroupType, parentApiName, result.getApiNames());
            }
        } catch (Exception e) {
            log.warn("findManageGroupObjects error,headers:{},arg:{}", JacksonUtils.toJson(headers), JacksonUtils.toJson(arg));
        }
        return new ManageGroup(true, manageGroupType, parentApiName, Collections.emptyList());
    }

    public ManageGroupContainer queryManageGroups(User user, ManageGroupType manageGroupType) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.getTenantId())) {
            return new ManageGroupContainer(true, Collections.emptyList());
        }
        if (user.isSupperAdmin()) {
            return new ManageGroupContainer(true, Collections.emptyList());
        }
        GetObjListWithParentByManageGroup.Arg arg = new GetObjListWithParentByManageGroup.Arg();
        arg.setTenantId(user.getTenantId());
        arg.setUserId(user.getUserId());
        arg.setAppId(APP_ID);
        arg.setType(manageGroupType.getType());

        Map<String, String> headers = RestUtils.buildHeaders(user);
        try {
            GetObjListWithParentByManageGroup.RestResult restResult = platServiceProxy.getObjListWithParentByManageGroup(headers, arg);
            if (restResult.isSuccess()) {
                GetObjListWithParentByManageGroup.Result result = restResult.getResult();
                return buildManageGroupContainer(result.isAllResult(), result.getManageGroups(), manageGroupType);
            }
        } catch (Exception e) {
            log.warn("getObjListWithParentByManageGroup error,headers:{},arg:{}", JacksonUtils.toJson(headers), JacksonUtils.toJson(arg));
        }
        return new ManageGroupContainer(true, Collections.emptyList());
    }

    private ManageGroupContainer buildManageGroupContainer(boolean allResult, List<FindManageGroupObjects.Result> manageGroups, ManageGroupType manageGroupType) {
        List<ManageGroup> groups = CollectionUtils.nullToEmpty(manageGroups).stream()
                .map(result -> new ManageGroup(result.isAllResult(), manageGroupType, result.getParentApiName(), result.getApiNames()))
                .collect(Collectors.toList());
        return new ManageGroupContainer(allResult, groups);
    }

    public void addToManageGroup(User user, String parentApiName, String apiName, ManageGroupType manageGroupType) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.getTenantId())) {
            if (!oldMangeGroupGray(user, manageGroupType)) {
                return;
            }
        }
        if (user.isSupperAdmin()) {
            return;
        }
        AddToManageGroupByUserId.Arg arg = AddToManageGroupByUserId.Arg.builder()
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .parentApiName(parentApiName)
                .apiName(ManageGroup.buildSupportApiName(parentApiName, apiName, manageGroupType))
                .type(manageGroupType.getType())
                .appId(APP_ID)
                .build();

        Map<String, String> headers = RestUtils.buildHeaders(user);
        AddToManageGroupByUserId.RestResult restResult = platServiceProxy.addToManageGroupByUserId(headers, arg);
        if (!restResult.isSuccess()) {
            log.warn("addToManageGroupByUserId error,headers:{},arg:{}", JacksonUtils.toJson(headers), JacksonUtils.toJson(arg));
            throw new ManageGroupException(restResult.getErrMessage());
        }
    }

    public void deleteManageGroup(User user, String parentApiName, Collection<String> apiNames, ManageGroupType manageGroupType) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.MANAGE_GROUP_GRAY, user.getTenantId())) {
            return;
        }
        List<String> apiNameList = apiNames.stream()
                .map(apiName -> ManageGroup.buildSupportApiName(parentApiName, apiName, manageGroupType))
                .collect(Collectors.toList());
        DeleteManageContent.Arg arg = DeleteManageContent.Arg.builder()
                .tenantId(user.getTenantId())
                .userId(user.getUserId())
                .apiNames(apiNameList)
                .groupType(manageGroupType.getType())
                .appId(APP_ID)
                .build();

        Map<String, String> headers = RestUtils.buildHeaders(user);
        DeleteManageContent.RestResult restResult = platServiceProxy.deleteManageContent(headers, arg);

        if (!restResult.isSuccess()) {
            log.warn("deleteManageGroup fail! headers:{},arg:{}", JacksonUtils.toJson(headers), JacksonUtils.toJson(arg));
            throw new ManageGroupException(restResult.getErrMessage());
        }
    }

    private boolean oldMangeGroupGray(User user, ManageGroupType manageGroupType) {
        return ManageGroupType.OBJECT == manageGroupType && AppFrameworkConfig.isGrayManageGroup(user.getTenantId());
    }

    public boolean isOpenOrganization(String tenantId) {
        OrganizationStatus.Arg arg = new OrganizationStatus.Arg();
        arg.setTenantId(tenantId);
        OrganizationStatus.Result result = platServiceProxy.openOrganization(arg);
        return result.isOpenOrganization();
    }
}
