package com.facishare.paas.appframework.common.mq;

import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;

/**
 * RocketMQ异常
 *
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/1/23.
 */
public class RocketMQException extends APPException {

    public RocketMQException(String message) {
        super(message);
    }

    public RocketMQException(String message, Throwable cause) {
        super(message, cause);
    }

    public RocketMQException(SystemErrorCode errorCode) {
        super(errorCode);
    }

    public RocketMQException(SystemErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }
}
