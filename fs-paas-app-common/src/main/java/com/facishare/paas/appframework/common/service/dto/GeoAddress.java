package com.facishare.paas.appframework.common.service.dto;

import lombok.Data;

import java.util.Objects;

public interface GeoAddress {

    class Arg {

    }

    @Data
    class Result {
        /**
         * 经度
         */
        private String lng;
        /**
         * 纬度
         */
        private String lat;
    }

    @Data
    class RestResult {
        String code;
        String message;
        Result data;

        public boolean isSuccess() {
            return Objects.equals(code, "200");
        }
    }
}
