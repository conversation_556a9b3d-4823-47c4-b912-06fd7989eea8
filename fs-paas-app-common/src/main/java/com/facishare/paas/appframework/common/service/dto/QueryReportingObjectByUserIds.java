package com.facishare.paas.appframework.common.service.dto;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

public interface QueryReportingObjectByUserIds {
    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> idList;
    }

    @Data
    class Result {
        private boolean success;
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<ReportingObject> result;

        /**
         *leaderId是-1，leaderName是null 时没有汇报对象
         * @return
         */
        public List<String> getLeaders(){
            if(result==null){
                return Lists.newArrayList();
            }

            return result.stream().filter(item->!item.getLeaderId().equals("-1")).map(ReportingObject::getLeaderId).collect(Collectors.toList());
        }
    }

    @Data
    class ReportingObject {
        private String userId;
        private String userName;
        private String leaderId;
        private String leaderName;

        public String getLeaderId(){
            if(Strings.isNullOrEmpty(leaderId)){
                return "-1";
            }
            return leaderId;
        }
    }
}
