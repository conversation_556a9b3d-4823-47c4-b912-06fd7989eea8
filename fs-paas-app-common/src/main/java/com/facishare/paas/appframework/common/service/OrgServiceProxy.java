package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

/**
 * 访问组织架构服务代理接口 <p> Created by <PERSON><PERSON>yi<PERSON><PERSON> on 2017/8/18.
 */
@RestResource(value = "PAAS-ORG", desc = "Call PaaS Org Service",
        codec = "com.facishare.paas.appframework.common.service.codec.AppDefaultCodeC", contentType = "application/json")
public interface OrgServiceProxy {

    @POST(value = "/org/employees/info/batch", desc = "批量获取用户信息")
    QueryUserInfoByIds.Result queryUserInfoByIds(@Body QueryUserInfoByIds.Arg arg);

    @POST(value = "/org/employees/info/batch", desc = "批量获取用户信息Ext")
    QueryUserExtInfoByIds.Result queryUserExtByIds(@Body QueryUserExtInfoByIds.Arg arg);

    @POST(value = "/org/main/dept/batch", desc = "批量获取用户部门信息")
    QueryDeptInfoByUserIds.Result queryDeptInfoByUserIds(@Body QueryDeptInfoByUserIds.Arg arg);

    @POST(value = "/org/department/info/batch", desc = "批量通过部门id获取部门信息")
    QueryDeptInfoByDeptIds.Result queryDeptInfoByDeptIds(@Body QueryDeptInfoByDeptIds.Arg arg);

    @POST(value = "/org/department/info/batch/status", desc = "批量通过部门id获取制定状态的部门信息")
    QueryDeptInfoByDeptIds.Result queryDeptInfoByDeptIdsAndStatus(@Body QueryDeptInfoByDeptIds.Arg arg);

    @POST(value = "/org/employee/departments/all/id", desc = "根据用户id获取用户所有的父级部门")
    QueryAllSuperDeptByUserId.Result QueryAllSuperDeptByUserId(@Body QueryAllSuperDeptByUserId.Arg arg);

    @POST(value = "/org/dept/name/exact", desc = "根据部门名称精确匹配获取部门信息")
    QueryDeptByName.Result queryDeptByName(@Body QueryDeptByName.Arg arg);

    @POST(value = "/org/user/nickname/exact", desc = "根据用户名称精确匹配获取用户信息")
    QueryUserByName.Result queryUserByName(@Body QueryUserByName.Arg arg);

    @POST(value = "org/department/employee/batch", contentType = "application/json")
    QueryMembersByDeptIds.Result getMembersByDeptIds(@Body QueryMembersByDeptIds.Arg arg);

    @POST(value = "/org/department/employeeIdList/batch", contentType = "application/json")
    QueryMembersIdByDeptIds.Result getMembersIdByDeptIds(@Body QueryMembersIdByDeptIds.Arg arg);

    @POST(value = "org/group/mem/batch", contentType = "application/json")
    QueryMembersByGroupIds.Result getMembersByGroupIds(@Body QueryMembersByGroupIds.Arg arg);

    //批量 根据用户Id查询其汇报对象
    @POST(value = "/org/leader/batch", contentType = "application/json")
    QueryReportingObjectByUserIds.Result getReportingObjectsByUserIds(@Body QueryReportingObjectByUserIds.Arg arg);

    @POST(value = "org/dept/children", contentType = "application/json", desc = "根据部门id获取子部门,isAll=true:递归获取所有子部门,isAll=false:直属子部门")
    QuerySubDeptByDeptId.Result getSubDeptByDeptId(@Body QuerySubDeptByDeptId.Arg arg);

    @POST(value = "/org/user/subordinate", contentType = "application/json", desc = "根据用户id获取下级")
    QuerySubordinatesByUserId.Result querySubordinatesByUserId(@Body QuerySubordinatesByUserId.Arg arg);

    @POST(value = "/org/dept/parent", contentType = "application/json", desc = "根据部门id获取所有的父级部门")
    QueryAllSuperDeptsByDeptIds.Result queryAllSuperDeptsByDeptIds(@Body QueryAllSuperDeptsByDeptIds.Arg arg);

    @POST(value = "/org/dept/managerId", contentType = "application/json", desc = "根据用户ids获取负责的部门")
    QueryResponsibleDeptsByUserIds.Result queryResponsibleDeptsByUserIds(@Body QueryResponsibleDeptsByUserIds.Arg arg);

    @POST(value = "/org/employee/departments", contentType = "application/json", desc = "根据用户id获取所在主属部门、附属部门")
    QueryDeptInfoByUserId.Result queryDeptInfoByUserId(@Body QueryDeptInfoByUserId.Arg arg);

    @POST(value = "/org/dept/user", contentType = "application/json", desc = "批量获取部门下用户信息")
    QueryMemberInfosByDeptIds.Result queryMemberInfosByDeptIds(@Body QueryMemberInfosByDeptIds.Arg arg);

    @POST(value = "/org/dept/path", contentType = "application/json", desc = "批量上级部门信息及其负责人")
    GetNDeptPathByUserId.Result getNDeptPathByUserId(@Body GetNDeptPathByUserId.Arg arg);

    @POST(value = "/org/group/own", contentType = "application/json", desc = "根据用户ids获取用户组信息")
    QueryGroupByUserIds.Result queryGroupByUserIds(@Body QueryGroupByUserIds.Arg arg);

    @POST(value = "/org/employee/name", contentType = "application/json", desc = "根据用户ids获取组信息")
    GetUserIdsByName.Result getUserIdsByName(GetUserIdsByName.Arg arg);

    @POST(value = "/org/user/batch/supervisor/path", contentType = "application/json", desc = "根据用户ids获取组信息")
    BatchQuerySupervisorByUserId.Result batchQuerySupervisorByUserId(@Body BatchQuerySupervisorByUserId.Arg arg);

    @POST(value = "/batch/get/responsible/employee", contentType = "application/json", desc = "根据用户ids获取负责部门的人员信息")
    BatchGetResponsibleEmployeeByUserId.Result batchGetResponsibleEmployeeByUserId(@Body BatchGetResponsibleEmployeeByUserId.Arg arg);


    @POST(value = "/org/group/list", contentType = "application/json", desc = "根据用户组ids获取用户组信息")
    QueryGroupByIds.Result queryGroupByIds(@Body QueryGroupByIds.Arg arg);

    @POST(value = "/org/group/name", contentType = "application/json", desc = "根据用户组名称获取用户组信息")
    QueryGroupByGroupName.Result queryGroupByGroupName(@Body QueryGroupByGroupName.Arg arg);

    @POST(value = "/org/group/names", contentType = "application/json", desc = "根据用户组名称批量获取用户组信息")
    QueryGroupByGroupName.Result queryGroupByGroupNames(@Body QueryGroupByGroupName.Arg arg);

    @POST(value = "/org/tenant_group/findTenantGroupByIds", contentType = "application/json", desc = "根据企业组ids获取企业组信息")
    QueryTenantGroupByIds.Result queryTenantGroupByIds(@Body QueryTenantGroupByIds.Arg arg);

    @POST(value = "/org/tenant_group/findOutTenantIdByGroupIds", contentType = "application/json", desc = "根据企业组ids获取下游企业id")
    FindOutTenantIdByGroupIds.RestResult findOutTenantIdByGroupIds(@Body FindOutTenantIdByGroupIds.Arg arg);
}
