package com.facishare.paas.appframework.common.service.model;


import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.dto.SendNewCrmMessageModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
public class NewCrmNotification {

    public static final int CUSTOM_REMIND_RECORD_TYPE = 92;
    public static final int CUSTOM_REMIND_DETAIL_RECORD_TYPE = 115;
    public static final int CUSTOM_REMIND_JOB_SCHEDULE_TYPE = 118;
    public static final int PAY_CALLBACK_REMIND_TYPE = 71;


    private String sourceId;
    private String senderId;
    private Integer type;
    private Set<Integer> receiverIDs;
    // receiverIDs 和 departments 二选一
    private List<SendNewCrmMessageModel.DepartmentReceiver> departments;
    /**
     * 发送 CRM 通知 给外部人员（上游企业 ID 和下游人员 ID 就可以了）
     */
    private Set<Integer> outEmployees;
    private boolean remindSender;
    private String title;
    private InternationalItem titleInfo;
    private String fullContent;
    private InternationalItem fullContentInfo;
    private int urlType;
    private Map<String, String> urlParameter;
    private String appId;
    private String lastSummary;
    private InternationalItem lastSummaryInfo;
    private String innerPlatformWebUrl;
    private String innerPlatformMobileUrl;
    private String outPlatformUrl;
    private String objectApiName;
    private String objectId;
    // 860 prm 支持外部通道（公众号）CRM 通知增加参数，wiki：https://wiki.firstshare.cn/pages/viewpage.action?pageId=131808626
    private List<KeyValueItem> bodyForm;
    private List<String> extraChannelList;
    private Map<String, Object> extraDataMap;
    private Map<String, List<Map<String, String>>> templateIdKeyListMap;

    public int getTypeWithDefaultValue() {
        if (type == null) {
            return CUSTOM_REMIND_RECORD_TYPE;
        }
        return type;
    }

    public NewCrmNotification addUrlParameter(String key, String value) {
        if (urlParameter == null) {
            urlParameter = Maps.newHashMap();
        }
        urlParameter.put(key, value);
        return this;
    }

    public boolean isTextMessage() {
        return this.urlType == 0;
    }

    public String getObjectApiName() {
        if (CollectionUtils.notEmpty(urlParameter)) {
            String apiName = urlParameter.get("objectApiName");
            return StringUtils.isNotBlank(apiName) ? apiName : objectApiName;
        }
        return objectApiName;
    }

    public String getObjectId() {
        if (CollectionUtils.notEmpty(urlParameter)) {
            String id = urlParameter.get("id");
            return StringUtils.isNotBlank(id) ? id : objectId;
        }
        return objectId;
    }
}
