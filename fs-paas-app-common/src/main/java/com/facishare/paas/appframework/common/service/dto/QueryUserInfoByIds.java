package com.facishare.paas.appframework.common.service.dto;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Singular;

import java.util.List;

/**
 * Created by liyiguang on 2017/8/18.
 */
public interface QueryUserInfoByIds {

    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        @Singular
        @SerializedName("idList")
        private List<String> ids;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<UserInfo> result;
        private Boolean success = false;

        public boolean isSuccess() {
            return Boolean.TRUE.equals(success);
        }
    }
}
