/*
 * Copyright (C) 2014 The Guava Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.facishare.paas.appframework.common.graph;

import com.facishare.paas.appframework.common.graph.GraphConstants.Presence;
import com.google.common.annotations.Beta;
import com.google.common.base.Function;
import com.google.common.base.Functions;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.errorprone.annotations.Immutable;

import static com.google.common.base.Preconditions.checkNotNull;

/**
 * A {@link Graph} whose elements and structural relationships will never change. Instances of this
 * class may be obtained with {@link #copyOf(Graph)}.
 *
 * <p>See the Guava User's Guide's <a
 * href="https://github.com/google/guava/wiki/GraphsExplained#immutable-implementations">discussion
 * of the {@code Immutable*} types</a> for more information on the properties and guarantees
 * provided by this class.
 *
 * @param <N> Node parameter type
 * <AUTHOR> Sexton
 * <AUTHOR> O'Madadhain
 * <AUTHOR> Darwish
 * @since 20.0
 */
@Beta
@Immutable(containerOf = {"N"})
public class ImmutableGraph<N> extends ForwardingGraph<N> {
    @SuppressWarnings("Immutable") // The backing graph must be immutable.
    private final BaseGraph<N> backingGraph;

    ImmutableGraph(BaseGraph<N> backingGraph) {
        this.backingGraph = backingGraph;
    }

    /**
     * Returns an immutable copy of {@code graph}.
     */
    public static <N> ImmutableGraph<N> copyOf(Graph<N> graph) {
        return (graph instanceof ImmutableGraph)
                ? (ImmutableGraph<N>) graph
                : new ImmutableGraph<N>(
                new ConfigurableValueGraph<N, Presence>(
                        GraphBuilder.from(graph), getNodeConnections(graph), graph.edges().size()));
    }

    /**
     * Simply returns its argument.
     *
     * @deprecated no need to use this
     */
    @Deprecated
    public static <N> ImmutableGraph<N> copyOf(ImmutableGraph<N> graph) {
        return checkNotNull(graph);
    }

    private static <N> ImmutableMap<N, GraphConnections<N, Presence>> getNodeConnections(
            Graph<N> graph) {
        // ImmutableMap.Builder maintains the order of the elements as inserted, so the map will have
        // whatever ordering the graph's nodes do, so ImmutableSortedMap is unnecessary even if the
        // input nodes are sorted.
        ImmutableMap.Builder<N, GraphConnections<N, Presence>> nodeConnections = ImmutableMap.builder();
        for (N node : graph.nodes()) {
            nodeConnections.put(node, connectionsOf(graph, node));
        }
        return nodeConnections.build();
    }

    private static <N> GraphConnections<N, Presence> connectionsOf(Graph<N> graph, N node) {
        Function<Object, Presence> edgeValueFn = Functions.constant(Presence.EDGE_EXISTS);
        return graph.isDirected()
                ? DirectedGraphConnections.ofImmutable(
                graph.predecessors(node), Maps.asMap(graph.successors(node), edgeValueFn))
                : UndirectedGraphConnections.ofImmutable(
                Maps.asMap(graph.adjacentNodes(node), edgeValueFn));
    }

    @Override
    protected BaseGraph<N> delegate() {
        return backingGraph;
    }
}
