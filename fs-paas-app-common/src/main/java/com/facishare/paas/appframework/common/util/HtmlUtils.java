package com.facishare.paas.appframework.common.util;

import com.google.common.base.Strings;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;

import java.util.List;
import java.util.Objects;

public class HtmlUtils {
    public static String parseText(String html) {
        Document doc = Jsoup.parse(html);
        StringBuilder buffer = new StringBuilder();
        doParse(doc, buffer);
        return buffer.toString();
    }

    public static boolean hasScriptTag(String html) {
        Document doc = Jsoup.parse(html);
        Elements script = doc.getElementsByTag("script");
        return Objects.nonNull(script) && !script.isEmpty();
    }

    private static void doParse(Node node, StringBuilder buffer) {
        if (node instanceof TextNode) {
            String text = ((TextNode)node).text();
            if (!Strings.isNullOrEmpty(text)) {
                buffer.append(text.trim());
            }
            return;
        }

        /**
         * 递归处理子节点
         */
        List<Node> children = node.childNodes();
        if (children != null && !children.isEmpty()) {
            for (Node child : children) {
                doParse(child, buffer);
            }
        }
    }
}
