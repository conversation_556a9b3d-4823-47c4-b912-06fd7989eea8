package com.facishare.paas.appframework.common.service.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.base.Strings;
import lombok.*;

import java.util.List;
import java.util.Objects;

public interface FindTodoList {
    @Data
    @Builder
    class Arg {
        String tenantId;
        String userId;
        Integer status;
        String type;
        String apiName;
        Integer pageSize;
        Integer offsetNum;
    }

    @Data
    @Builder
    class Result {
        List<MessageInfo> messageList;

        public boolean isEmpty() {
            return CollectionUtils.empty(messageList);
        }

    }

    @Data
    class MessageInfo {
        String apiName;
        Long createTime;
        String id;
        String mqId;
        String objectId;
        String status;
        String tag;
        String tenantId;
        String title;
        String type;
        String url;
        String ea;
        String extendFields;
        ExtendField extendFieldObj;
        String describeDisplayName;
        String objectName;

        public ExtendField getExtendFieldObj() {
            if(!Strings.isNullOrEmpty(extendFields) && Objects.isNull(extendFieldObj)) {
                extendFieldObj = JSON.parseObject(extendFields, ExtendField.class);
            }
            return extendFieldObj;
        }
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ExtendField {
        String taskId;
        String business;
        String createBy;
        String entityId;
        String objectId;
        String sourceId;
        String taskType;
        String tenantId;
        String attribute;
        Long createTime;

        Integer todoDelete;
        Integer todoDeal;

        String workflowId;
        String workflowName;
        String sourceWorkflowId;
        Integer sessionBOCItemKey;
        String workflowInstanceId;

        String createByName;
        String entityName;
    }
}
