package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.core.rest.InnerAPIResult;
import lombok.Data;

import java.util.List;

public interface FindManageGroupObjects {

    @Data
    class Arg {
        private String tenantId;
        private String userId;
        private String appId;
        /**
         * 父级的apiname;如果当前就是父级，则不传或者传 00000000000000000000000000000000
         */
        private String parentApiName;
        /**
         * 对象、流程、角色标识
         */
        private String type;
    }

    class RestResult extends InnerAPIResult<Result> {
    }

    @Data
    class Result {
        /**
         * 父级apiName
         */
        private String parentApiName;
        private List<String> apiNames;

        /**
         * 是否具有查看所有对象、流程、角色权限
         */
        private boolean allResult;
    }
}
