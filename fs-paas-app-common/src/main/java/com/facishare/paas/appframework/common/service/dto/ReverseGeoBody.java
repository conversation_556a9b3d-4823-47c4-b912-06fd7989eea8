package com.facishare.paas.appframework.common.service.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 逆地址解析请求体
 */
@Data
public class ReverseGeoBody {
    /**
     * 经度
     */
    @NotNull
    private Double longitude;

    /**
     * 纬度
     */
    @NotNull
    private Double latitude;

    /**
     * 是否启用缓存
     */
    private Boolean cache = true;

    /**
     * 是否返回POI列表
     */
    private Boolean returnPois = false;

    /**
     * 语言
     */
    private String language = "zh-CN";

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 平台
     */
    private String platform = "server";
} 