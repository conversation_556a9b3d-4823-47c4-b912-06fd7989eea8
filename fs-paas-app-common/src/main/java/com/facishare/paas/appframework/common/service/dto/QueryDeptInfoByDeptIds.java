package com.facishare.paas.appframework.common.service.dto;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2017/10/17
 */
public interface QueryDeptInfoByDeptIds {


    String TYPE_DEPT = "dept";
    String TYPE_ORG = "org";


    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> idList;
        private Integer deptStatus; // null:所有状态, 0:启用状态,1:停用状态
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<DeptInfo> result = new ArrayList<>();
        private boolean success;

        public List<String> getLeaderIds() {
            if (!CollectionUtils.isNotEmpty(result)) {
                return Lists.newArrayList();
            }
            return result.stream()
                    .filter(dept -> (!Strings.isNullOrEmpty(dept.getLeaderUserId())))
                    .map(dept -> dept.getLeaderUserId())
                    .collect(Collectors.toList());
        }
    }

    @Data
    class DeptInfo {
        private String parentId;
        @SerializedName(value = "deptId", alternate = "id")
        private String deptId;
        @SerializedName(value = "deptName", alternate = "name")
        private String deptName;
        private String leaderUserId;
        private String leaderName;
        private Integer status;
        /**
         * 部门类型：
         * 组织 org
         * 部门 dept
         */
        private String deptType;


        public boolean disabled() {
            return status != null && 1 == status;
        }
    }

    enum DeptStatusEnum {
        ENABLE(0),
        DISABLE(1),
        ALL(null),
        ;

        @Getter
        private Integer code;

        public static DeptStatusEnum of(Integer code) {
            if (Objects.isNull(code)) {
                return ALL;
            }
            if (0 == code) {
                return ENABLE;
            } else if (1 == code) {
                return DISABLE;
            } else {
                return ALL;
            }
        }

        DeptStatusEnum(Integer code) {
            this.code = code;
        }
    }
}
