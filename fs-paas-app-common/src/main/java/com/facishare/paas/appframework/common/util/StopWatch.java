package com.facishare.paas.appframework.common.util;

/**
 * performance tool
 * <p>
 * Created by liyigua<PERSON> on 16/5/26.
 * <p>
 * https://lexiangla.com/teams/k100011/docs/f9ddeba4342411ed978f5ad5fa474bf2?company_from=050524eee61811e783175254005b9a60
 * <p>
 * {@link com.fxiaoke.common.StopWatch}
 */
public class StopWatch {
    private com.fxiaoke.common.StopWatch delegate;

    public static StopWatch create(String tagName) {
        return new StopWatch(tagName);
    }

    private StopWatch(String tagName) {
        this.delegate = com.fxiaoke.common.StopWatch.createStarted(tagName);
    }

    public void lap(String stepName) {
        this.delegate.lap(stepName);
    }

    public void log() {
        this.delegate.logSlow();
    }

    public void logSlow(long slow) {
        this.delegate.logSlow(slow);
    }
}
