package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/19 6:23 下午
 */
public interface FindAllTags {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private Integer errCode;
        private String errMsg;
        private List<Tag> data;
    }

    @Data
    class Tag {
        String tagGroupName;
        List<String> tagDatas;
    }


}
