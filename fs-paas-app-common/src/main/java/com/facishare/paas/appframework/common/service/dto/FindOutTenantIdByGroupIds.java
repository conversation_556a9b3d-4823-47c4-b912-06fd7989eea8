package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.core.model.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/27
 */
public interface FindOutTenantIdByGroupIds {

    @Data
    @Builder
    class Arg {
        private TenantGroupContext context;
        private List<String> groupIds;
        private PageInfo pageInfo;
    }

    @Data
    @AllArgsConstructor
    class TenantGroupContext {
        private String tenantId;
        private String userId;

        public static TenantGroupContext fromUser(User user) {
            return new TenantGroupContext(user.getTenantId(), user.getUpstreamOwnerIdOrUserId());
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class PageInfo {
        private Integer pageSize = 20;
        private Integer currentPage = 1;
        private Integer total;

        public static PageInfo fromPageSize(Integer pageSize) {
            PageInfo pageInfo = new PageInfo();
            if (Objects.nonNull(pageSize)) {
                pageInfo.setPageSize(pageSize);
            }
            return pageInfo;
        }

        public boolean nextPage() {
            if (hasNext()) {
                this.currentPage = this.currentPage + 1;
                return true;
            }
            return false;
        }

        private boolean hasNext() {
            if (ObjectUtils.anyNull(this.total, this.currentPage, this.pageSize)) {
                return false;
            }
            return this.total > this.pageSize * this.currentPage;
        }
    }

    @Data
    class RestResult {
        private Integer errCode;
        private String errMessage;
        private List<Result> result;
        private PageInfo pageInfo;

        public boolean success() {
            return Objects.equals(0, errCode);
        }
    }

    @Data
    class Result {
        private String outTenantId;
        private String tenantGroupId;
    }
}
