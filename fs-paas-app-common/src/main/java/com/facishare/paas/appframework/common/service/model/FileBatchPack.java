package com.facishare.paas.appframework.common.service.model;

import lombok.Data;

import java.util.List;

/**
 * 文件批量打包接口参数封装
 * 
 * <AUTHOR>
 */
public interface FileBatchPack {

  @Data
  class Arg {
    /**
     * 企业账户
     */
    private String ea;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 下载人，格式如 E.1000
     */
    private String downloadUser;
    
    /**
     * bizType
     */
    private String business;
    
    /**
     * 安全组，网盘文件需要传此
     */
    private String securityGroup;
    
    /**
     * 打包的文件
     */
    private BatchFileDocuments documents;
    
    /**
     * 当前任务Id
     */
    private String jobId;
    
    /**
     * 文件名，如果传了需要带后缀
     */
    private String fileName;
    
    /**
     * 是否跳过重复文件
     */
    private boolean skipDuplicatedFile;
    
    /**
     * 是否发送CRM消息
     */
    private boolean sendCRMMessage = true;
    
    /**
     * 是否发送企信文件助手
     */
    private boolean sendQiXinAssistant = true;

    private int dataCount;
  }

  @Data
  class BatchFileDocuments {
    /**
     * 文件系统类型
     */
    private String wareHouseType;
    
    /**
     * 目录名称
     */
    private String directoryName;
    
    /**
     * 文件列表 平铺
     */
    private List<BatchFileEntity> files;
    
    /**
     * 文件列表 结构 递归
     */
    private List<BatchFileDocuments> dirs;
  }

  @Data
  class BatchFileEntity {
    /**
     * 文件名,需要携带后缀
     */
    private String Name;
    
    /**
     * 文件path，携带后缀
     */
    private String Path;
    
    /**
     * 签名，如果下载其他企业文件，可携带文件元信息签名
     */
    private String Sign;
  }

  @Data
  class Result {
    /**
     * 响应码，如果是0证明任务生命状态正常，否则就有问题
     */
    private Integer code;
    
    /**
     * 响应信息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private ResultData data;
  }

  @Data
  class ResultData {
    /**
     * 下载链接
     */
    private String downloadUrl;
    
    /**
     * 文件key
     */
    private String key;
    
    /**
     * 文件大小
     */
    private Integer size;
  }
}
