package com.facishare.paas.appframework.common.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.netdisk.api.model.*;
import com.facishare.netdisk.api.model.base.Operator;
import com.facishare.netdisk.api.model.type.NodeCategory;
import com.facishare.netdisk.api.model.type.V5FileInfo;
import com.facishare.paas.appframework.common.service.model.FileBatchPack;
import com.facishare.paas.appframework.common.util.FileBatchPackUtil;
import com.facishare.paas.appframework.common.util.ImportConfig;
import com.facishare.paas.appframework.common.util.XmlUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.FileSizeConverter;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.warehouse.api.dubbo.FilePackedService;
import com.facishare.warehouse.api.model.FilePackedArg;
import com.facishare.warehouse.api.model.FilePackedResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 网盘服务实现类
 *
 * 注意：此类中的一些工具方法（如 createBatch*Documents、filterName、getFileName 等）
 * 与 fs-paas-app-metadata 模块中的 FileBatchPackUtil 和 FileExtUtil 功能相同。
 * 由于模块依赖关系限制（fs-paas-app-common 不能依赖 fs-paas-app-metadata），
 * 这些方法需要在此处重复实现。如需修改逻辑，请同时更新两处的实现。
 *
 * <AUTHOR>
 * @date 2018/12/11 3:05 PM
 */
@Slf4j
@Service("networkDiskService")
public class NetworkDiskServiceImpl implements NetworkDiskService {

    //打包文件如果是网盘文件，需要传入一个身份标示，用于安全校验，
    //而非网盘文件不需要，但是NPath是否是网盘文件应该透明，所以这个标示都透传
    private static final String IDENTITY_INFORMATION = "XiaoKeNetDisk";


    @Autowired
    private NetworkDiskProxy networkDiskProxy;
    @Autowired
    private FilePackedService filePackedService;
    @Autowired
    private FileBatchPackProxy fileBatchPackProxy;
    @Autowired
    private GDSHandler gdsHandler;


    @Override
    public List<V5FileInfo> getFileInfoByNPath(User user, List<String> paths) {
        String ea = gdsHandler.getEAByEI(user.getTenantId());
        return getFileInfoByNPath(paths, user.getTenantId(), ea, user);
    }

    @Override
    public List<V5FileInfo> getFileInfoByNPath(List<String> paths, String tenantId, String ea, User user) {
        //组装请求网盘接口参数
        NSGetFileInfoByNPathArg arg = new NSGetFileInfoByNPathArg();
        arg.setNPathList(paths);
        Operator operator = new Operator();
        operator.setEmployeeId(Integer.parseInt(user.getUserId()));
        operator.setNetdiskManager(false);
        operator.setEnterpriseId(Integer.parseInt(tenantId));
        operator.setEnterpriseAccount(ea);
        arg.setOperator(operator);
        //请求接口
        NSGetFileInfoByNPathResult result = networkDiskProxy.getFieldInfoByNPath(RestUtils.buildHeaders(user), arg);
        return result.getFileInfoList() == null ? Lists.newArrayList() : result.getFileInfoList();
    }

    @Override
    public String exportFilesWithXml(String xml, String tenantId, String ea, User user, String fileName) {
        NSGetDownloadTokenArg arg = new NSGetDownloadTokenArg();
        arg.setEmployeeId(Integer.parseInt(user.getUserId()));
        arg.setEnterpriseAccount(ea);
        arg.setStructure(xml);
        arg.setFileName(fileName);
        arg.setExtension("zip");

        Operator operator = new Operator();
        operator.setEmployeeId(Integer.parseInt(user.getUserId()));
        operator.setNetdiskManager(false);
        operator.setEnterpriseId(Integer.parseInt(tenantId));
        operator.setEnterpriseAccount(ea);
        arg.setOperator(operator);
        NSGetDownloadTokenResult result = networkDiskProxy.getDownloadToken(RestUtils.buildHeaders(user), arg);
        return result.getToken();
    }

    @Override
    public FilePackedResult packedFile(User user, String xml, String jobId) {
        return packedFile(user, xml, jobId, null);
    }

    @Override
    public FilePackedResult packedFile(User user, String xml, String jobId, Integer totalCount) {
        return packedFile(user, xml, jobId, totalCount, false);
    }

    @Override
    public FilePackedResult packedFile(User user, String xml, String jobId, Integer totalCount, boolean skipDuplicatedFile) {
        return packedFileWithDubboApi(user, xml, jobId, totalCount, skipDuplicatedFile);
    }

    /**
     * 使用原有的Dubbo接口进行文件打包
     */
    private FilePackedResult packedFileWithDubboApi(User user, String xml, String jobId, Integer totalCount, boolean skipDuplicatedFile) {
        FilePackedArg arg = new FilePackedArg();
        arg.setEmployId(user.getTenantIdInt());
        arg.setBizType(DefObjConstants.PACKAGE_NAME_CRM);
        arg.setEa(gdsHandler.getEAByEI(user.getTenantId()));
        arg.setDocuments(xml);
        arg.setDownloadUser(user.getUserIdOrOutUserIdIfOutUser());
        arg.setDownloadSecurityGroup(IDENTITY_INFORMATION);
        arg.setWarehouseType("N");
        arg.setJobId(jobId);
        arg.setSkipDuplicatedFile(skipDuplicatedFile);
        if (Objects.nonNull(totalCount)) {
            arg.setDataCount(totalCount);
        }
        int length = JSON.toJSONString(arg).getBytes().length;
        long exportXmlMaxLength = ImportConfig.getExportXmlMaxLength();
        if (length > exportXmlMaxLength) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.EXPORT_XML_MAX_LENGTH_ERROR,
                    "文件请求长度{0},超过最大允许长度{1},请减少数据量后重新操作.", // ignoreI18n
                    FileSizeConverter.convertFileSize(length), FileSizeConverter.convertFileSize(exportXmlMaxLength)));
        }
        return filePackedService.getFilePackedResult(arg);
    }

    @Override
    public FilePackedResult packedFileWith1Layer(User user,
                                                 IObjectDescribe describe,
                                                 List<IFieldDescribe> fieldsToExport,
                                                 List<IObjectData> dataList,
                                                 String jobId,
                                                 Integer totalCount,
                                                 boolean skipDuplicatedFile,
                                                 boolean rename) {
        // 灰度判断：是否使用新接口
        boolean useNewApi = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILE_BATCH_PACK_NEW_API, user.getTenantId());

        if (useNewApi) {
            return callRestBatchPackApi(user, jobId, totalCount, skipDuplicatedFile, null,
                () -> FileBatchPackUtil.create1LayerDocuments(describe, fieldsToExport, dataList, rename));
        } else {
            // 使用 XmlUtil 构造 XML
            String xml = XmlUtil.create1LayerXml(describe, fieldsToExport, dataList, rename);
            return packedFileWithDubboApi(user, xml, jobId, totalCount, skipDuplicatedFile);
        }
    }

    @Override
    public FilePackedResult packedFileWith3Layer(User user,
                                                 IObjectDescribe describe,
                                                 List<IFieldDescribe> fieldsToExport,
                                                 List<IObjectData> dataList,
                                                 String jobId,
                                                 Integer totalCount,
                                                 boolean skipDuplicatedFile) {
        // 灰度判断：是否使用新接口
        boolean useNewApi = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILE_BATCH_PACK_NEW_API, user.getTenantId());

        if (useNewApi) {
            return callRestBatchPackApi(user, jobId, totalCount, skipDuplicatedFile, null,
                () -> FileBatchPackUtil.create3LayerDocuments(describe, fieldsToExport, dataList));
        } else {
            // 使用 XmlUtil 构造 XML
            String xml = XmlUtil.create3LayerXml(describe, fieldsToExport, dataList);
            return packedFileWithDubboApi(user, xml, jobId, totalCount, skipDuplicatedFile);
        }
    }

    @Override
    public FilePackedResult packedFileWithExportFileAndImage(User user,
                                                             Map<String, String> pathAndName,
                                                             String fileName,
                                                             List<IObjectDescribe> describes,
                                                             Map<String, List<IFieldDescribe>> fieldsToExport,
                                                             Map<String, List<IObjectData>> dataListMap,
                                                             String jobId,
                                                             Integer totalCount) {
        // 灰度判断：是否使用新接口
        boolean useNewApi = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILE_BATCH_PACK_NEW_API, user.getTenantId());
        if (useNewApi) {
            return callRestBatchPackApi(user, jobId, totalCount, false, fileName,
                () -> FileBatchPackUtil.createExportFileAndImageDocuments(pathAndName, fileName, describes, fieldsToExport, dataListMap));
        } else {
            return packedFileWithExportFileAndImageOldApi(user, pathAndName, fileName, describes, fieldsToExport, dataListMap, jobId, totalCount);
        }
    }

    /**
     * 将新接口的返回结果转换为原有的FilePackedResult格式
     */
    private FilePackedResult convertToFilePackedResult(FileBatchPack.Result result) {
        // 将新接口的返回结果转换为原有格式，保证调用方无感知
        FilePackedResult filePackedResult = new FilePackedResult();
        if (result != null) {
            // 根据新接口的结果结构转换为旧格式
            filePackedResult.setCode(result.getCode());
            filePackedResult.setMessage(result.getMessage());
            filePackedResult.setKey(result.getData().getKey());
            if (result.getData() != null) {
                filePackedResult.setDownloadURL(result.getData().getDownloadUrl());
                filePackedResult.setSize(Long.valueOf(result.getData().getSize()));
            }
        }
        return filePackedResult;
    }

    /**
     * 文档构建器函数式接口
     * 用于不同类型的文档构建逻辑
     */
    @FunctionalInterface
    private interface DocumentsBuilder {
        FileBatchPack.BatchFileDocuments build();
    }

    /**
     * 通用的REST接口调用方法
     * 统一处理参数构建、REST接口调用和结果转换
     */
    private FilePackedResult callRestBatchPackApi(User user,
                                                String jobId,
                                                Integer totalCount,
                                                boolean skipDuplicatedFile,
                                                String fileName,
                                                DocumentsBuilder documentsBuilder) {
        // 构建基础参数
        FileBatchPack.Arg arg = buildFileBatchPackArg(user, jobId, skipDuplicatedFile, totalCount, fileName);

        // 构建文档结构
        arg.setDocuments(documentsBuilder.build());

        // 调用新的REST接口
        FileBatchPack.Result result = fileBatchPackProxy.batchPack(RestUtils.buildHeaders(user), arg);

        // 转换返回结果为原有格式
        return convertToFilePackedResult(result);
    }

    /**
     * 构建 FileBatchPack.Arg 的基础参数
     * 支持可选的 fileName 参数
     */
    private FileBatchPack.Arg buildFileBatchPackArg(User user,
                                                   String jobId,
                                                   boolean skipDuplicatedFile,
                                                   Integer totalCount,
                                                   String fileName) {
        FileBatchPack.Arg arg = new FileBatchPack.Arg();
        arg.setEa(gdsHandler.getEAByEI(user.getTenantId()));
        arg.setUserId(user.getTenantIdInt());
        arg.setDownloadUser(user.getUserIdOrOutUserIdIfOutUser());
        arg.setBusiness(DefObjConstants.PACKAGE_NAME_CRM);
        arg.setSecurityGroup(IDENTITY_INFORMATION);
        arg.setJobId(jobId);
        arg.setSkipDuplicatedFile(skipDuplicatedFile);
        arg.setSendCRMMessage(true);
        arg.setSendQiXinAssistant(true);

        // 设置可选参数
        if (StringUtils.isNotBlank(fileName)) {
            arg.setFileName(fileName);
        }

        if (Objects.nonNull(totalCount)) {
            arg.setDataCount(totalCount);
        }

        return arg;
    }

    /**
     * 使用旧接口进行导出文件和图片的打包
     */
    private FilePackedResult packedFileWithExportFileAndImageOldApi(User user,
                                                                    Map<String, String> pathAndName,
                                                                    String fileName,
                                                                    List<IObjectDescribe> describes,
                                                                    Map<String, List<IFieldDescribe>> fieldsToExport,
                                                                    Map<String, List<IObjectData>> dataListMap,
                                                                    String jobId,
                                                                    Integer totalCount) {
        // 使用 XmlUtil 构造 XML
        String xml = XmlUtil.createExportFileAndImage(pathAndName, fileName, describes, fieldsToExport, dataListMap);
        return packedFileWithDubboApi(user, xml, jobId, totalCount, false);
    }

    @Override
    public NSGetFoldersByParentIDResult getFoldersByParentId(String parentId, String tenantId, String ea, User user) {
        if (Objects.isNull(parentId)) {
            return null;
        }
        NSGetFoldersByParentIDArg arg = new NSGetFoldersByParentIDArg();
        Operator operator = new Operator();
        operator.setEmployeeId(Integer.parseInt(user.getUserId()));
        operator.setNetdiskManager(false);
        operator.setEnterpriseId(Integer.parseInt(tenantId));
        operator.setEnterpriseAccount(ea);
        arg.setOperator(operator);

        arg.setNodeCategory(NodeCategory.Personal);
        arg.setParentId(parentId);

        return networkDiskProxy.getFoldersByParentId(RestUtils.buildHeaders(user), arg);
    }
}
