package com.facishare.paas.appframework.common.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.ObjectAction.*;

/**
 * 自定对象页面及组件上的可用操作(button)列表
 * <p>
 * Created by liyigua<PERSON> on 2017/11/15.
 */
public enum ComponentActions {

    //详情页
    DETAIL_PAGE(SALE_RECORD, DIAL, SEND_MAIL, DISCUSS, SCHEDULE, REMIND, UPDATE, CHANGE, EFFECTIVE, INVALID, CHANGE_OWNER, START_BPM, PRINT, LOCK, UNLOCK, CLONE, CHANGE_PARTNER, CHANGE_PARTNER_OWNER, DELETE_PARTNER, START_STAGE_PROPELLOR, CANCEL_ENTRY, ENTER_ACCOUNT, ADD_TO_PRICEBOOK, TRANSFORM, FOLLOW, UNFOLLOW),

    //feed为服务记录的详情页
    SERVICE_RECORD_DETAIL_PAGE(SERVICE_RECORD, DIAL, SEND_MAIL, DISCUSS, SCHEDULE, REMIND, UPDATE, INVALID, CHANGE_OWNER, START_BPM, PRINT, LOCK, UNLOCK, CLONE),

    //回收站详情页
    RECYCLE_BIN_DETAIL_PAGE(DELETE, RECOVER),

    //SFA回收站详情页
    SFA_RECYCLE_BIN_DETAIL_PAGE(SALE_RECORD, VIEW_FEED_CARD, SALE_RECORD, DIAL, SEND_MAIL, DISCUSS, SCHEDULE, REMIND, DELETE, RECOVER),

    //列表页头
    LIST_PAGE_HEADER(CREATE, REFERENCE_CREATE, BATCH_IMPORT, BATCH_EXPORT, INTELLIGENTFORM, DUPLICATECHECK, EXPORT_FILE, PRIORITY, BATCH_MANAGE_PICTURE),

    //关联对象
    RELATED_OBJECT(CREATE, BULK_RELATE, BULK_DISRELATE, INTELLIGENTFORM),

    //从对象
    SLAVE_OBJECT(CREATE),

    //相关团队
    TEAM(ADD_TEAM_MEMBER, EDIT_TEAM_MEMBER, DELETE_TEAM_MEMBER),

    //销售记录
    SALES_RECORD(SALE_RECORD),

    //移动终端列表页
    TERMINAL_LIST_PAGE(CREATE, REFERENCE_CREATE, INTELLIGENTFORM, DUPLICATECHECK),

    //SFA详情页
    SFA_DETAIL_PAGE(UPDATE, INVALID, CHANGE_OWNER),

    //邮件
    EMAIL(CREATE),

    //关联SFA对象
    SFA_RELATED_OBJECT(INTELLIGENTFORM, BULK_RELATE, BULK_DISRELATE),

    //新建页
    ADD_PAGE(CREATE_SAVE, CREATE_SAVE_CONTINUE, CREATE_SAVE_DRAFT),

    //客户新建页面
    ACCOUNT_ADD_PAGE(CREATE_SAVE, CREATE_SAVE_CONTINUE, CREATE_SAVE_DRAFT, CREATE_SAVE_CREATE_CONTACT),

    //合作伙伴新建页面
    PARTNER_ADD_PAGE(CREATE_SAVE, CREATE_SAVE_CONTINUE, CREATE_SAVE_DRAFT, CREATE_SAVE_CREATE_CONTACT),

    //项目新建页面
    PROJECT_ADD_PAGE(CREATE_SAVE, CREATE_SAVE_CONTINUE, CREATE_SAVE_DRAFT, CREATE_SAVE_CREATE_PROJECT_STAGE),

    //任务新建页面
    PROJECT_TASK_ADD_PAGE(CREATE_SAVE, CREATE_SAVE_CONTINUE, CREATE_SAVE_DRAFT, CREATE_SAVE_CREATE_DEPENDENCIES),

    //阶段新建页面
    PROJECT_STAGE_ADD_PAGE(CREATE_SAVE, CREATE_SAVE_CONTINUE, CREATE_SAVE_DRAFT, CREATE_SAVE_CREATE_PROJECT_TASK),

    //编辑页
    EDIT_PAGE(UPDATE_SAVE, EDIT_SAVE_DRAFT),

    //主从新建编辑页从对象通用按钮 注：移动端无 IMPORT_EXCEL 按钮
    DETAIL_WITH_MASTER_EDIT_PAGE_DETAIL_NORMAL(IMPORT_EXCEL, SINGLE_CREATE),

    //主从新建编辑页从对象批量按钮
    DETAIL_WITH_MASTER_EDIT_PAGE_DETAIL_BATCH(BATCH_UPDATE, DELETE, CLONE),

    //主从新建编辑页从对象单行按钮
    DETAIL_WITH_MASTER_EDIT_PAGE_DETAIL_SINGLE(DELETE, CLONE),

    //主从新建编辑页从对象单行按钮（灰度）
    DETAIL_WITH_MASTER_EDIT_PAGE_DETAIL_SINGLE_GRAY(DELETE, CLONE, TILE),

    // 独立站点列表页页头
    WEBSITE_LIST_PAGE_HEADER(CREATE);

    private List<ObjectAction> actions;

    ComponentActions(ObjectAction... actions) {
        this.actions = Lists.newArrayList(actions);
    }

    public List<ObjectAction> getActions() {
        return actions;
    }

    public List<String> getActionCodes() {
        return actions.stream().map(x -> x.getActionCode()).collect(Collectors.toList());
    }

    public List<IButton> getActionButtons() {
        return actions.stream().map(this::createButton).collect(Collectors.toList());
    }

    private IButton createButton(ObjectAction action) {
        return action.createButton();
    }

    public static ComponentActions getAddPageActions(String objectApiName) {
        return addPageActionMap.getOrDefault(objectApiName, ADD_PAGE);
    }

    private static Map<String, ComponentActions> addPageActionMap = Maps.newHashMap();

    static {
        addPageActionMap.put(Utils.ACCOUNT_API_NAME, ACCOUNT_ADD_PAGE);
        addPageActionMap.put(Utils.PROJECT_API_NAME, PROJECT_ADD_PAGE);
        addPageActionMap.put(Utils.PROJECT_TASK_API_NAME, PROJECT_TASK_ADD_PAGE);
        addPageActionMap.put(Utils.PROJECT_STAGE_API_NAME, PROJECT_STAGE_ADD_PAGE);
        addPageActionMap.put(Utils.PARTNER_API_NAME, PARTNER_ADD_PAGE);
    }
}
