package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

public interface GetNDeptPathByUserId {
    @Data
    @Builder
    class Arg{
        private String tenantId;
        private String appId;
        private String userId;
        private Integer n;
    }

    @Data
    class Result{
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<DeptInfo> result = new ArrayList<>();
        private boolean success;
    }

    @Data
    @Builder
    class DeptInfo {
        private String deptId;
        private String tenantId;
        private String name;
        private String managerId;
        private String parentId;
        private String description;
        private String createdBy;
        private String lastModifiedBy;
        private Integer status;
        private Long createTime;
        private Long lastModifiedTime;
        private Integer isDeleted;
    }
}
