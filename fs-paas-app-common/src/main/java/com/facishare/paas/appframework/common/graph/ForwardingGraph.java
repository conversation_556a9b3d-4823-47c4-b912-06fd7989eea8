/*
 * Copyright (C) 2016 The Guava Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.facishare.paas.appframework.common.graph;

import java.util.Set;

/**
 * A class to allow {@link Graph} implementations to be backed by a {@link BaseGraph}. This is not
 * currently planned to be released as a general-purpose forwarding class.
 *
 * <AUTHOR>
 */
abstract class ForwardingGraph<N> extends AbstractGraph<N> {

    protected abstract BaseGraph<N> delegate();

    @Override
    public Set<N> nodes() {
        return delegate().nodes();
    }

    /**
     * Defer to {@link AbstractGraph#edges()} (based on {@link #successors(Object)}) for full edges()
     * implementation.
     */
    @Override
    protected long edgeCount() {
        return delegate().edges().size();
    }

    @Override
    public boolean isDirected() {
        return delegate().isDirected();
    }

    @Override
    public boolean allowsSelfLoops() {
        return delegate().allowsSelfLoops();
    }

    @Override
    public ElementOrder<N> nodeOrder() {
        return delegate().nodeOrder();
    }

    @Override
    public Set<N> adjacentNodes(N node) {
        return delegate().adjacentNodes(node);
    }

    @Override
    public Set<N> predecessors(N node) {
        return delegate().predecessors(node);
    }

    @Override
    public Set<N> successors(N node) {
        return delegate().successors(node);
    }

    @Override
    public int degree(N node) {
        return delegate().degree(node);
    }

    @Override
    public int inDegree(N node) {
        return delegate().inDegree(node);
    }

    @Override
    public int outDegree(N node) {
        return delegate().outDegree(node);
    }

    @Override
    public boolean hasEdgeConnecting(N nodeU, N nodeV) {
        return delegate().hasEdgeConnecting(nodeU, nodeV);
    }
}
