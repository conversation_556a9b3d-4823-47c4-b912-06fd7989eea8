package com.facishare.paas.appframework.common.service;

import com.facishare.rest.core.annotation.*;

import java.util.Map;

@RestResource(
        value = "NCRM",
        desc = "NCRM服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface ControllerProxy {

    @POST(value = "/API/v1/rest/object/{apiName}/controller/{methodName}", desc = "执行Controller")
    String executeController(@Body Object arg, @HeaderMap Map<String, String> headers, @PathParams Map<String, String> pathParams,
                             @QueryParamsMap Map<String, String> queryParams);

}
