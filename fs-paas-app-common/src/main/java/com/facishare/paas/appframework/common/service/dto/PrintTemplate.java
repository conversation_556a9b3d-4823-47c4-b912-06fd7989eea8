package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface PrintTemplate {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg{
        String objectAPIName;
        String templateId;
        String objectId;
        String instanceId;
        String orientation;

    }
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private String name;
        private String fileType;
        private String path;
        private long fileSize;
    }
}
