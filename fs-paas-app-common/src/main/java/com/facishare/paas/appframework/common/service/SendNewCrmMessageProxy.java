package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.SendNewCrmMessageModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(
        value = "SendNewCrmMessage",
        desc = "发送新CRM通知消息", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.AppDefaultCodeC"
)
public interface SendNewCrmMessageProxy {

    @POST(value = "/addRemindRecord", desc = "发送新CRM通知消息")
    SendNewCrmMessageModel.Result sendNewCrmMessages(@HeaderMap Map<String, String> header, @Body SendNewCrmMessageModel.RemindRecordItem remindRecordItem);
}
