/*
 * Copyright (C) 2016 The Guava Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.facishare.paas.appframework.common.graph;

import com.google.common.annotations.Beta;
import com.google.errorprone.annotations.CanIgnoreReturnValue;

/**
 * A subinterface of {@link ValueGraph} which adds mutation methods. When mutation is not required,
 * users should prefer the {@link ValueGraph} interface.
 *
 * @param <N> Node parameter type
 * @param <V> Value parameter type
 * <AUTHOR>
 * @since 20.0
 */
@Beta
public interface MutableValueGraph<N, V> extends ValueGraph<N, V> {

    /**
     * Adds {@code node} if it is not already present.
     *
     * <p><b>Nodes must be unique</b>, just as {@code Map} keys must be. They must also be non-null.
     *
     * @return {@code true} if the graph was modified as a result of this call
     */
    @CanIgnoreReturnValue
    boolean addNode(N node);

    /**
     * Adds an edge connecting {@code nodeU} to {@code nodeV} if one is not already present; associate
     * that edge with {@code value}. In an undirected graph, the edge will also connect {@code nodeV}
     * to {@code nodeU}.
     *
     * <p>Values do not have to be unique. However, values must be non-null.
     *
     * <p>If {@code nodeU} and {@code nodeV} are not already present in this graph, this method will
     * silently {@link #addNode(Object) add} {@code nodeU} and {@code nodeV} to the graph.
     *
     * @return the value previously associated with the edge connecting {@code nodeU} to {@code
     * nodeV}, or null if there was no such edge.
     * @throws IllegalArgumentException if the introduction of the edge would violate {@link
     *                                  #allowsSelfLoops()}
     */
    @CanIgnoreReturnValue
    V putEdgeValue(N nodeU, N nodeV, V value);

    /**
     * Removes {@code node} if it is present; all edges incident to {@code node} will also be removed.
     *
     * @return {@code true} if the graph was modified as a result of this call
     */
    @CanIgnoreReturnValue
    boolean removeNode(N node);

    /**
     * Removes the edge connecting {@code nodeU} to {@code nodeV}, if it is present.
     *
     * @return the value previously associated with the edge connecting {@code nodeU} to {@code
     * nodeV}, or null if there was no such edge.
     */
    @CanIgnoreReturnValue
    V removeEdge(N nodeU, N nodeV);
}
