package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface GenerateVerificationCode {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        String code;
        String status;

        public boolean success() {
            return Objects.equals("SUCCESS", getStatus());
        }
    }

    @Data
    class Arg {
        private String mobile;
        private String ip;
        private String captchaCode;
        private String captchaId;
        private int expireTime;
    }

    @Data
    @Builder
    class SendVerificationCodeArg {
        private String objectApiName;
        private String fieldApiName;
        private String verificationCode;
        // 主对象数据局
        private Map<String, Object> objectData;
        //从对象数据
        private Map<String, List<Map<String, Object>>> objectDetails;
    }

}
