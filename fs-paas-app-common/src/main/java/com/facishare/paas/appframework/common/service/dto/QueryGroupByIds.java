package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/08/26
 */
public interface QueryGroupByIds {

    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> groupIdList;
        private boolean isFilterByUser;
        private boolean isPublic;
        private Integer status;
//        private PageInfo page;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<UserGroupInfo> result;
        private boolean success = false;
    }

    @Data
    class UserGroupInfo {
        private String id;
        private String tenantId;
        private String appId;
        private String name;
        private int status;
        private int type;
        private String description;
        private boolean delFlag;
    }
}
