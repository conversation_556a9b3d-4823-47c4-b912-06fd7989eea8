package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by renlb on 2018/4/24.
 */
public interface QueryDeptInfoByUserId {

    @Data
    @Builder
    class Arg{
        private String tenantId;
        private String appId;
        private String userId;
        private String id;
    }

    @Data
    class Result{
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<DeptInfo> result;
        private boolean success;
    }

    @Data
    class DeptInfo{
        private String deptId;
        private String deptName;
        private String leaderUserId;
        private String leaderName;
        private String parentId;
    }
}
