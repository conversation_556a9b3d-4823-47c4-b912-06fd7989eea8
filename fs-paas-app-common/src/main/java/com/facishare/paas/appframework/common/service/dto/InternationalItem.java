package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 国际化信息数据对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InternationalItem {
    private String internationalKey;
    private List<String> internationalParameters;
    private Map<String, String> defaultParameterValues;
}
