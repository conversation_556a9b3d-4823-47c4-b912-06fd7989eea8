package com.facishare.paas.appframework.common.service.codec;

import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.facishare.rest.core.util.JsonUtil;
import org.apache.http.HttpStatus;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <AUTHOR>
 * @date 2019/12/19 6:06 下午
 */
public class AppDefaultCodeC implements IRestCodeC {
    @Override
    public <T> byte[] encodeArg(T obj) {

        if(Objects.isNull(obj)){
            return null;
        }

        if(obj instanceof String){
            return ((String) obj).getBytes(UTF_8);
        }
        return JsonUtil.toJson(obj).getBytes(UTF_8);
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        String bodyString = new String(bytes, UTF_8);
        if(statusCode >= HttpStatus.SC_MULTIPLE_CHOICES){
            throw new RestProxyRuntimeException(statusCode,bodyString);
        }
        if(clazz==String.class){
            return (T)bodyString;
        }

        T ret = JsonUtil.fromJson(new String(bytes, UTF_8), clazz);
        return ret;
    }
}
