package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.FindObjectPageComponentList;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/26
 */
@RestResource(
        value = "WebPage",
        desc = "自定义页面", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec"
)
public interface WebPageProxy {
    @POST(value = "/Homepage/findObjectPageComponentList", desc = "查询组件定义", socketReadTimeoutSecond = 5)
    FindObjectPageComponentList.Result findObjectPageComponentList(@HeaderMap Map<String, String> headerMap, @Body FindObjectPageComponentList.Arg arg);
}
