package com.facishare.paas.appframework.common.service;

import com.facishare.function.biz.api.exception.FunctionExecuteException;
import com.facishare.function.biz.api.model.FuncExecuteContext;
import com.facishare.function.biz.api.model.FuncUser;
import com.facishare.function.biz.api.service.FunctionService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.userlogin.api.model.validatecode.BuildValidateCode;
import com.facishare.userlogin.api.model.validatecode.VerifyValidateCode;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.validator.ValidatorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.PHONE_NUMBER_INFO_V2;

@Slf4j
@Service("PhoneNumberService")
public class PhoneNumberServiceImpl implements PhoneNumberService {

    @Autowired
    private PhoneNumberInformationProxy phoneNumberInformationProxy;
    @Autowired
    private PhoneNumberInfoV2Proxy phoneNumberInfoV2Proxy;
    @Autowired
    private SmsServiceProxy smsServiceProxy;
    @Autowired
    @Qualifier("bizFunctionService")
    private FunctionService functionService;

    private AppCaptchaService appCaptchaService;
    
    private SmsCodeService smsCodeService;

    @Autowired
    public void setCaptchaService(AppCaptchaService appCaptchaService) {
        this.appCaptchaService = appCaptchaService;
    }

    @Autowired
    public void setSmsCodeService(SmsCodeService smsCodeService) {
        this.smsCodeService = smsCodeService;
    }

    /**
     * 查询手机归属地只支持国内的手机号，所以要限制数量为11
     *
     * @param mobileSet
     * @return
     */
    @Override
    public List<QueryPhoneNumberInformation.Result> batchQueryPhoneNumberInfo(Set<String> mobileSet) {
        mobileSet = CollectionUtils.nullToEmpty(mobileSet).stream()
                .filter(x -> !Strings.isNullOrEmpty(x)).collect(Collectors.toSet());
        List<QueryPhoneNumberInformation.Result> results = Lists.newCopyOnWriteArrayList();
        if (CollectionUtils.empty(mobileSet)) {
            return results;
        }

        if (mobileSet.size() > 200) {
            log.info("batchQueryPhoneNumberInfo mobile count more than 200! size:{}", mobileSet.size());
        }
        List<List<String>> partition = Lists.partition(Lists.newArrayList(mobileSet), 200);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        try {
            for (List<String> mobileList : partition) {
                String mobile = StringUtils.join(mobileList.stream().map(ConvertUtils::removeSpaceForPhoneNumber).collect(Collectors.toSet()), ",");
                mobile = URLEncoder.encode(mobile, "utf-8");
                String tenantId = Objects.isNull(RequestContextManager.getContext()) ? null :
                        RequestContextManager.getContext().getTenantId();
                String finalMobile = mobile;
                parallelTask.submit(() -> {
                    if (UdobjGrayConfig.isAllow(PHONE_NUMBER_INFO_V2, tenantId)) {
                        QueryPhoneNumberInformation.CodeV2 code = phoneNumberInfoV2Proxy.queryBatchQueryPhoneNumberInformation(finalMobile, I18N.getContext().getLanguage());
                        if (code.getCode() == 200) {
                            results.addAll(code.getData());
                        }
                    } else {
                        QueryPhoneNumberInformation.Mobiles arg = QueryPhoneNumberInformation.Mobiles.builder().mobiles(finalMobile).build();
                        QueryPhoneNumberInformation.Code code = phoneNumberInformationProxy.queryBatchQueryPhoneNumberInformation(arg, I18N.getContext().getLanguage());
                        if (code.getErrCode() == 200) {
                            results.addAll(code.getResults());
                        }
                    }

                });
            }
        } catch (Exception e) {
            log.error("batchQueryPhoneNumberInfo error!", e);
            return Lists.newArrayList();
        }
        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("findPhoneNumberInformation time out!", e);
        }

        if (CollectionUtils.empty(results)) {
            return Lists.newArrayList();
        }
        results.forEach(this::dealMultiLanguage);
        results.forEach(this::dealResult);

        return results;
    }

    private void dealMultiLanguage(QueryPhoneNumberInformation.Result result) {
        if (Objects.isNull(result.getI18n())) {
            return;
        }
        QueryPhoneNumberInformation.I18n i18n = result.getI18n();
        result.setCity(Strings.isNullOrEmpty(i18n.getCity()) ? result.getCity() : i18n.getCity());
        result.setProvince(Strings.isNullOrEmpty(i18n.getProvince()) ? result.getProvince() : i18n.getProvince());
        result.setOperator(Strings.isNullOrEmpty(i18n.getOperator()) ? result.getOperator() : i18n.getOperator());
    }

    private void dealResult(QueryPhoneNumberInformation.Result result) {
        if (Objects.isNull(result.getProvince())) {
            result.setProvince("");
        }
        if (AppFrameworkConfig.getMunicipalityDirectlyUnderTheCentralGovernmentCode().contains(result.getCode()) || Objects.isNull(result.getCity())) {
            result.setCity("");
        }
        if (!AppFrameworkConfig.getCommunicationOperator().contains(result.getOperator()) || Objects.isNull(result.getOperator())) {
            result.setOperator("");
        }
        if (Strings.isNullOrEmpty(result.getProvince()) && Strings.isNullOrEmpty(result.getCity())) {
            result.setMobilePath(result.getOperator());
        } else {
            if (I18N.EN.equals(I18N.getContext().getLanguage())) {
                result.setMobilePath(result.getProvince() + " " + result.getCity() + " " + result.getOperator());
            } else {
                result.setMobilePath(result.getProvince() + result.getCity() + " " + result.getOperator());
            }
        }
    }

    @Override
    public QueryPhoneNumberInformation.Result queryPhoneNumberInfo(String mobile) {
        return queryPhoneNumberInfo(mobile, true);
    }

    @Override
    public QueryPhoneNumberInformation.Result queryPhoneNumberInfo(String mobile, boolean municipalityIgnoreCity) {
        if (Strings.isNullOrEmpty(mobile)) {
            return QueryPhoneNumberInformation.Result.builder().build();
        }
        mobile = ConvertUtils.removeSpaceForPhoneNumber(mobile);

        Map<String, String> mobileMap = Maps.newHashMap();
        QueryPhoneNumberInformation.Result result = null;
        mobileMap.put("mobile", mobile);
        mobileMap.put("municipalityIgnoreCity", String.valueOf(municipalityIgnoreCity));
        try {
            String tenantId = Objects.isNull(RequestContextManager.getContext()) ? null :
                    RequestContextManager.getContext().getTenantId();
            if (UdobjGrayConfig.isAllow(PHONE_NUMBER_INFO_V2, tenantId)) {
                QueryPhoneNumberInformation.CodeV2Single code = phoneNumberInfoV2Proxy.queryPhoneNumberInformation(mobile,
                        I18N.getContext().getLanguage(), String.valueOf(municipalityIgnoreCity));
                if (code.getCode() == 200) {
                    result = code.getData();
                }
            } else {
                QueryPhoneNumberInformation.Code code = phoneNumberInformationProxy.queryPhoneNumberInformation(mobileMap, I18N.getContext().getLanguage());
                if (code.getErrCode() == 200 && CollectionUtils.notEmpty(code.getResults())) {
                    result = code.getResults().get(0);
                }
            }

            if (Objects.isNull(result)) {
                return QueryPhoneNumberInformation.Result.builder().build();
            }
            dealMultiLanguage(result);
            dealResult(result);
            return result;
        } catch (Exception e) {
            log.warn("queryPhoneNumberInformation warn phoneNumber:{}", mobile, e);
            return QueryPhoneNumberInformation.Result.builder().build();
        }
    }

    @Override
    public String queryVerificationCode(User user, String ea, String areaCode, String ip, String mobile, String captchaCode, String captchaId) {
        return sendSmsCode(ea, user, ip, areaCode, mobile, captchaCode, captchaId);
    }

    @Override
    public GetImageCode.Result refreshCaptcha() {
        Captcha.Result res = appCaptchaService.createCaptchaCode(null);
        return GetImageCode.Result.builder().epxId(res.getEpxId()).data(ArrayUtils.toObject(res.getData())).build();
    }

    @Override
    public String checkVerificationCode(String areaCode, String phone, String smsCode) {
        return verifySmsCode(null, areaCode, phone, smsCode);
    }

    @Override
    public boolean checkSmsStatus(String tenantId) {
        CheckSmsStatus.Result result = smsServiceProxy.checkSmsStatus(new CheckSmsStatus.Arg(Integer.valueOf(tenantId)));
        return result.getStatus() == 0;
    }

    @Override
    public String sendSMValidateCode(User user, String ea, String areaCode, String ip, String mobile, String captchaCode, String captchaId) {
        //action 不能设置crm，crm会到深研短信平台扣配额
        return smsCodeService.sendSmsCode(ea, user, ip, areaCode, mobile, captchaCode, captchaId, LOGIN_DYNAMIC_PASSWORD, I18NKey.DEFAULT_SM_LOGIN_I18N_KEY);
    }

    @Override
    public String verifyValidateCode(String areaCode, String phone, String smsCode) {
        return verifySmsCode(null, areaCode, phone, smsCode);
    }

    @Override
    public GetImageCode.Result getImageCode() {
        Captcha.Result res = appCaptchaService.createCaptchaCode(null);
        return GetImageCode.Result.builder().epxId(res.getEpxId()).data(ArrayUtils.toObject(res.getData())).build();
    }

    @Override
    public GenerateVerificationCode.Result generateVerificationCode(User user, String mobile, String ip, String captchaCode, String captchaId, int expireTime) {
        BuildValidateCode.Argument arg = new BuildValidateCode.Argument();
        arg.setImgCode(captchaCode);
        arg.setCodeExpireTime(expireTime);
        arg.setIp(ip);
        arg.setEpxId(captchaId);
        arg.setKey(mobile);
        BuildValidateCode.Result result = smsCodeService.createSmsCode(user, ip, null, mobile, captchaCode, captchaId, expireTime);
        if (Objects.nonNull(result) && Objects.nonNull(result.getResult())) {
            return GenerateVerificationCode.Result.builder()
                    .status(result.getResult().name())
                    .code(result.getCode())
                    .build();
        }
        return GenerateVerificationCode.Result.builder()
                .status(null)
                .code(null)
                .build();
    }

    @Override
    public void sendCodeByAPL(User user, String functionApiName, String methodName, GenerateVerificationCode.SendVerificationCodeArg arg) {
        try {
            FuncExecuteContext funcExecuteContext = Optional.ofNullable(RequestContextManager.getContext())
                    .map(it -> {
                        FuncUser funcUser = new FuncUser();
                        funcUser.setUserId(user.getUserId());
                        funcUser.setTenantId(user.getTenantId());
                        funcUser.setOutUserId(user.getOutUserId());
                        funcUser.setOutTenantId(user.getOutTenantId());
                        funcUser.setUpstreamOwnerId(user.getUpstreamOwnerId());

                        FuncExecuteContext simpleContext = new FuncExecuteContext();
                        simpleContext.setUser(funcUser);
                        simpleContext.setAppId(it.getAppId());
                        simpleContext.setThirdAppId(it.getThirdAppId());
                        simpleContext.setThirdType(it.getThirdType());
                        simpleContext.setThirdUserId(it.getThirdUserId());
                        return simpleContext;
                    }).orElseGet(() -> FuncExecuteContext.getSimpleContext(user.getTenantId(), user.getUserId()));

            List<String> requestBody = Lists.newArrayList(JacksonUtils.toJson(arg));
            String functionResult = functionService.executeFuncMethod(funcExecuteContext, functionApiName, methodName, requestBody, 0);

            JsonNode jsonNode = JacksonUtils.readTree(functionResult);
            if (Objects.isNull(jsonNode)) {
                return;
            }

            JsonNode node = jsonNode.get("success");
            JsonNode messageNode = jsonNode.get("errorMessage");
            Boolean sendSuccess = JacksonUtils.convertValue(node, Boolean.class);
            String errorMessage = JacksonUtils.convertValue(messageNode, String.class);
            if (BooleanUtils.isNotTrue(sendSuccess) && org.apache.commons.lang3.StringUtils.isNotBlank(errorMessage)) {
                throw new ValidatorException(errorMessage);
            }

        } catch (FunctionExecuteException e) {
            log.warn("function execute fail, ei:{}, functionApiName:{}, methodName:{}", user.getTenantId(), functionApiName, methodName, e);
            throw new FunctionException(I18NExt.text(I18NKey.FUNC_FAIL));
        } catch (Exception e) {
            log.error("function execute error, ei:{}, functionApiName:{}, methodName:{}", user.getTenantId(), functionApiName, methodName, e);
            throw new FunctionException(I18NExt.text(I18NKey.FUNC_FAIL));
        }
    }

    @Override
    public String verifyCode(User user, String code, String mobile) {
        return smsCodeService.verifySmsCode(user, null, mobile, code);
    }

    @Override
    public BuildValidateCode.Result createSmsCode(User user, String ip, String areaCode, String mobile, String captchaCode, String captchaId, int expireTime) {
        return smsCodeService.createSmsCode(user, ip, areaCode, mobile, captchaCode, captchaId, expireTime);
    }

    @Override
    public String verifySmsCode(User user, String areaCode, String mobile, String smsCode) {
        return smsCodeService.verifySmsCode(user, areaCode, mobile, smsCode);
    }
    

    @Override
    public String sendSmsCode(String ea, User user, String ip, String areaCode, String mobile, String captchaCode, String captchaId) {
        return smsCodeService.sendSmsCode(ea, user, ip, areaCode, mobile, captchaCode, captchaId, CRM_ACTION, I18NKey.DEFAULT_SM_LOGIN_I18N_KEY);
    }
}
