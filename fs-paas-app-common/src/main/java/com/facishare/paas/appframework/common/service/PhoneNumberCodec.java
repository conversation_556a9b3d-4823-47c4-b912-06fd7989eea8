package com.facishare.paas.appframework.common.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.service.dto.QueryPhoneNumberInformation;
import com.facishare.rest.core.codec.DefaultRestCodec;
import com.facishare.rest.core.codec.IRestCodeC;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;

@Slf4j
public class PhoneNumberCodec implements IRestCodeC {

    @Override
    public <T> byte[] encodeArg(T t) {
        return DefaultRestCodec.instance.encodeArg(t);
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        try {
            String json = new String(bytes, Charset.forName("UTF-8"));
            if (Strings.isNullOrEmpty(json)) {
                return null;
            }
            if (StringUtils.startsWith(json, "[")) {
                List<QueryPhoneNumberInformation.Result> results = JSON.parseArray(json, QueryPhoneNumberInformation.Result.class);
                QueryPhoneNumberInformation.Code code = QueryPhoneNumberInformation.Code.builder().results(results).errCode(statusCode).build();
                return (T) code;
            } else if (StringUtils.startsWith(json, "{")) {
                QueryPhoneNumberInformation.Result result = JSON.parseObject(json, QueryPhoneNumberInformation.Result.class);
                QueryPhoneNumberInformation.Code code = QueryPhoneNumberInformation.Code.builder().errCode(statusCode).results(Lists.newArrayList(result)).build();
                return (T) code;
            } else {
                log.warn("PhoneNumberCodec failed, statusCode:{},result:{}", statusCode, new String(bytes));
                return (T) QueryPhoneNumberInformation.Code.builder().errCode(statusCode).errMessage(new String(bytes)).build();
            }

        } catch (Exception e) {
            log.warn("PhoneNumberCodec failed, statusCode:{},result:{}", statusCode, new String(bytes));
            return null;
        }
    }
}
