package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface QueryTenantGroupByIds {

    @Data
    @Builder
    class Arg {
        private TenantGroupContext context;
        private List<String> ids;
    }

    @Data
    @Builder
    class TenantGroupContext {
        private String tenantId;
        private String userId;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private List<QueryTenantGroupByIds.TenantGroupInfo> result;
    }

    @Data
    class TenantGroupInfo {
        private String id;
        private String name;
        private int status;
        private int type;
        //企业组分类id
        private String categoryId;
        private String createBy;
        private long createTime;
        private String lastModifyBy;
        private long lastModifyTime;
    }
}
