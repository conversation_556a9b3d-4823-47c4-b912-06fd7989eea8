package com.facishare.paas.appframework.common.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 元组
 * <p>
 * Created by liyiguang on 2017/8/21.
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class Tuple<K, V> {
    private K key;
    private V value;

    public static <K, V> Tuple<K, V> of(K key, V value) {
        return new Tuple<>(key, value);
    }
}
