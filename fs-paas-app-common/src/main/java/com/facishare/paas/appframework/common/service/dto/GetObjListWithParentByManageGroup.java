package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.core.rest.InnerAPIResult;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/7/31
 */
public interface GetObjListWithParentByManageGroup {

    @Data
    class Arg {
        private String tenantId;
        private String userId;
        private String appId;
        /**
         * 对象、流程、角色标识
         */
        private String type;
    }

    class RestResult extends InnerAPIResult<Result> {
    }

    @Data
    class Result {
        private List<FindManageGroupObjects.Result> manageGroups;

        /**
         * 是否具有查看所有对象、流程、角色权限
         */
        private boolean allResult;
    }
}
