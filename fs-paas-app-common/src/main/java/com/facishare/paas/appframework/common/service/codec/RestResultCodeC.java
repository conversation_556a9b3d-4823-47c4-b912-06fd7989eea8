package com.facishare.paas.appframework.common.service.codec;

import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class RestResultCodeC implements IRestCodeC {
    @Override
    public <T> byte[] encodeArg(T obj) {
        if (Objects.isNull(obj)) {
            return null;
        } else {
            return obj instanceof String ? ((String) obj).getBytes(StandardCharsets.UTF_8) : JacksonUtils.toJson(obj).getBytes(StandardCharsets.UTF_8);
        }
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        String bodyString = new String(bytes, StandardCharsets.UTF_8);
        if (statusCode >= 300) {
            throw new RestProxyRuntimeException(statusCode, bodyString);
        } else if (clazz == String.class) {
            return (T) bodyString;
        } else if (clazz == Void.TYPE) {
            return null;
        }

        T ret = JacksonUtils.fromJson(bodyString, clazz);
        if (!(ret instanceof BaseAPIResult)) {
            return ret;
        }
        BaseAPIResult restResult = (BaseAPIResult) ret;
        if (!restResult.isSuccess()) {
            log.warn("rest request failed,statusCode:{},result:{}", statusCode, restResult);
            throw new ValidateException(restResult.getMessage(), restResult.getCode());
        }
        return ret;
    }
}
