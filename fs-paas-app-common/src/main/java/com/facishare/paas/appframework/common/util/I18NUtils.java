package com.facishare.paas.appframework.common.util;

import com.facishare.paas.I18N;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class I18NUtils {
    public static Map<String, String> getObjectDisplayName(List<String> apiNameList) {
        Map<String, String> apiNameKeyMap = CollectionUtils.nullToEmpty(apiNameList)
                .stream()
                .distinct()
                .collect(Collectors.toMap(a -> a, GetI18nKeyUtil::getDescribeDisplayNameKey));

        return apiNameKeyMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, a -> I18N.text(a.getValue())));
    }

}
