package com.facishare.paas.appframework.common.service;

import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.social.personnel.model.PersonnelDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 对接组织结构员工相关接口
 *
 * <AUTHOR>
 * @date 2019/12/26 2:14 下午
 */
public interface EmployeeService {
    /**
     * 批量查询员工
     *
     * @param tenantId 企业ID
     * @param userIds  人员ID列表
     * @param status   员工状态：2-all, 1-stop, 0-active
     * @return 员工列表
     */
    List<EmployeeDto> batchGetUserInfo(String tenantId, Collection<String> userIds, int status);

    /**
     * 批量查询员工列表，默认查询全部状态的员工
     *
     * @param tenantId 企业ID
     * @param userIds  人员ID列表
     * @return 员工列表
     */
    List<EmployeeDto> batchGetUserInfo(String tenantId, Collection<String> userIds);

    /**
     * 根据user id获取员工信息
     *
     * @param tenantId 企业ID
     * @param userId   员工ID
     * @return 员工信息
     */
    EmployeeDto getUserInfo(String tenantId, String userId);

    /**
     * 根据人员昵称批量获取人员信息，支持模糊匹配
     *
     * @return
     */
    List<EmployeeDto> batchGetUserByNickName(String tenantId, String nickName);

    /**
     * 根据人员昵称列表，批量获取人员信息
     *
     * @param tenantId  企业ID
     * @param nickNames 昵称列表
     * @return 员工列表
     */
    List<EmployeeDto> batchGetUserByNickNames(String tenantId, List<String> nickNames, Integer status);


    /**
     * 根据人员codes，批量获取人员信息
     *
     * @param user  用户信息
     * @param codes 人员codes
     * @return 员工列表
     */
    List<PersonnelDto> batchGetUserByCodes(User user, List<String> codes);

    /**
     * 根据部门ID批量获取员工ID
     *
     * @param tenantId             企业ID
     * @param deptIds              部门ID列表
     * @param userStatus           人员状态
     * @param includeLowDepartment 是否包含下级部门
     * @return 人员ID
     */
    List<String> batchGetEmployeeIdsByDeptIds(String tenantId,
                                              List<String> deptIds,
                                              Integer userStatus,
                                              boolean includeLowDepartment);

    /**
     * 查询给定ID人员的下属
     *
     * @param tenantId 企业ID
     * @param userId   人员ID
     * @param status   人员状态
     * @param cascade  是否级联
     * @return 所有下属人员
     */
    List<EmployeeDto> querySubordinatesByUserId(String tenantId, String userId, Integer status, boolean cascade);

    List<String> querySubordinateIdsByUserId(String tenantId, String userId, Integer status, boolean cascade);


    Map<Integer, List<EmployeeDto>> batchGetAllEmployeeLeaderMap(String tenantId,
                                                                 Collection<String> userIds,
                                                                 Integer status,
                                                                 int type);

    /**
     * 根据部门ID，查询相应部门下所有的员工
     *
     * @param tenantId             企业ID
     * @param deptIds              部门ID
     * @param includeLowDepartment 是否包含下级部门
     * @return 相应部门所对应的人员
     */
    Map<Integer, List<EmployeeDto>> getEmployeesByDepartment(String tenantId,
                                                             Collection<String> deptIds,
                                                             boolean includeLowDepartment);

    Map<Integer, List<EmployeeDto>> getEmployeesByDepartment(String tenantId, Collection<String> deptIds,
                                                             boolean includeLowDepartment,
                                                             Integer userStatus,
                                                             Integer deptType);

    String getEmployeeRegionId(User user);

    EmployeeDto getEmployeeByMobile(String tenantId, String mobile);
}
