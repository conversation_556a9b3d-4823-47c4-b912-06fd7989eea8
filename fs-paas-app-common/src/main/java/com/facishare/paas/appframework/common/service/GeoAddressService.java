package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.GeoAddressDTO;
import com.facishare.paas.appframework.core.model.User;

/**
 * 地址解析服务
 */
public interface GeoAddressService {
    /**
     * 根据经纬度获取地址信息
     *
     * @param user    用户
     * @param request 请求参数
     * @return 地址信息
     */
    GeoAddressDTO.GeoResult getAddressByGeo(User user, GeoAddressDTO.ReverseGeoRequest request);
} 