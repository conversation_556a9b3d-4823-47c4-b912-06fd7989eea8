package com.facishare.paas.appframework.common.service.model;

import com.facishare.polling.api.enums.PollingOSType;

public enum PollingMsgEndType {
    EMPTY("empty"),
    IOS("iOS"),
    <PERSON><PERSON>ID("android"),
    WEB("web"),
    H5("h5"),
    MOBILE("mobile"),
    AL<PERSON>("all");

    private String code;
    PollingMsgEndType(String type) {
        this.code = type;
    }


    public PollingOSType toPollingOSType() {
        switch (code) {
            case "iOS":
                return PollingOSType.IOS;
            case "android":
                return PollingOSType.ANDROID;
            case "web":
            case "h5":
                return PollingOSType.WEB;
            case "mobile":
                return PollingOSType.APP;
            case "all":
                return PollingOSType.WEBANDAPP;
            default:
                return null;
        }
    }
}
