package com.facishare.paas.appframework.common.util;

import com.github.autoconf.ConfigFactory;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import org.apache.commons.lang3.StringUtils;

public class AppIdMapping {
    // 派工单
    public static final String DISPATCH_ORDERS = "DISPATCH_ORDERS";
    /**
     * 代理通
     */
    public static final String PRM_APP_ID = "prm";
    /**
     * 访销通
     */
    public static final String FXT_APP_ID = "fxt";
    /**
     * 主数据应用
     */
    public static final String MASTER_DATA_APP = "masterDataApp";
    /**
     * 订货通
     */
    public static final String DHT_APP_ID = "dht";
    /**
     * 服务通
     */
    public static final String FWT_APP_ID = "fwt";

    public static BiMap<String, String> appIdMapping;

    static {
        ConfigFactory.getInstance().getConfig("fs-appframework-appid", config -> {
            appIdMapping = HashBiMap.create(config.getAll());
        });
    }

    public static String getNamedAppId(String appId) {
        return appIdMapping.inverse().get(appId);
    }

    public static boolean isPRM(String originalAppId) {
        return PRM_APP_ID.equals(getNamedAppId(originalAppId));
    }

    public static boolean isFXT(String originalAppId) {
        return FXT_APP_ID.equals(getNamedAppId(originalAppId));
    }

    public static String getAppIdByName(String name) {
        return appIdMapping.get(name);
    }

    public static boolean isTargetApp(String appId, String appKey) {
        if (StringUtils.isAnyBlank(appId, appKey)) {
            return false;
        }
        return appKey.equals(getNamedAppId(appId));
    }
}
