package com.facishare.paas.appframework.common.service.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/7/30 14:31
 */
public interface QueryGroupByUserIds {

    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> userIdList;
        private boolean isFilterByUser;
        private boolean isPublic;
        private Integer status;
        private PageInfo page;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Set<String> result;
        private PageInfo pageInfo;
        private boolean success;
    }

    @Data
    class PageInfo {
        private Long total;
        private Integer pageSize;
        private Integer currentPage;
        private Integer totalPage;
    }
}
