package com.facishare.paas.appframework.common.service;

import com.facishare.netdisk.api.model.NSGetFoldersByParentIDResult;
import com.facishare.netdisk.api.model.type.V5FileInfo;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.warehouse.api.model.FilePackedResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/11 2:23 PM
 */
public interface NetworkDiskService {
    /**
     * 批量通过文件系统路径（NPath）获取文件信息
     *
     * @param paths    文件NPath
     * @param tenantId 企业id
     * @param ea       企业账号
     * @param user     用户
     * @return 文件信息列表
     */
    List<V5FileInfo> getFileInfoByNPath(List<String> paths, String tenantId, String ea, User user);

    /**
     * 批量通过文件系统路径（NPath）获取文件信息
     *
     * @param paths 文件NPath
     * @param user  用户
     * @return 文件信息列表
     */
    List<V5FileInfo> getFileInfoByNPath(User user, List<String> paths);

    /**
     * 批量将文件导出到网盘
     *
     * @param xml      导出文件目录以及文件的结构
     * @param tenantId 企业ID
     * @param ea       企业EA
     * @param user     用户
     */
    String exportFilesWithXml(String xml, String tenantId, String ea, User user, String fileName);

    /**
     * 文件打包
     *
     * @param xml
     * @param user
     * @param jobId
     * @return
     */
    @Deprecated
    FilePackedResult packedFile(User user, String xml, String jobId);

    @Deprecated
    FilePackedResult packedFile(User user, String xml, String jobId, Integer totalCount);

    @Deprecated
    FilePackedResult packedFile(User user, String xml, String jobId, Integer totalCount, boolean skipDuplicatedFile);

    /**
     * 根据父文件夹ID查询其下所有文件夹
     *
     * @param parentId 父ID
     * @param tenantId 企业ID
     * @param ea       企业账号
     * @param user     用户
     * @return 文件夹列表
     */
    NSGetFoldersByParentIDResult getFoldersByParentId(String parentId, String tenantId, String ea, User user);

    // ==================== 新增的文件批量打包方法 ====================


    /**
     * 使用一级目录结构进行文件打包（完整版本）
     */
    FilePackedResult packedFileWith1Layer(User user,
                                        IObjectDescribe describe,
                                        List<IFieldDescribe> fieldsToExport,
                                        List<IObjectData> dataList,
                                        String jobId,
                                        Integer totalCount,
                                        boolean skipDuplicatedFile,
                                        boolean rename);

    /**
     * 使用三级目录结构进行文件打包（完整版本）
     */
    FilePackedResult packedFileWith3Layer(User user,
                                        IObjectDescribe describe,
                                        List<IFieldDescribe> fieldsToExport,
                                        List<IObjectData> dataList,
                                        String jobId,
                                        Integer totalCount,
                                        boolean skipDuplicatedFile);

    /**
     * 导出文件和图片的文件打包
     * 替代 XmlUtil.createExportFileAndImage + packedFile 的组合
     */
    FilePackedResult packedFileWithExportFileAndImage(User user,
                                                     Map<String, String> pathAndName,
                                                     String fileName,
                                                     List<IObjectDescribe> describes,
                                                     Map<String, List<IFieldDescribe>> fieldsToExport,
                                                     Map<String, List<IObjectData>> dataListMap,
                                                     String jobId,
                                                     Integer totalCount);

}
