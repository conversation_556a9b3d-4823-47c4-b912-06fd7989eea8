package com.facishare.paas.appframework.common.service.dto;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018/9/28 17:29
 * @instruction
 */
@Data
public class SendCrmNewMessageModel {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        @SerializedName(value = "EmployeeID")
        private String employeeId;


        @SerializedName(value = "Content")
        private Object content;

        @SerializedName(value = "RemindRecordType")
        private Integer remindRecordType;


        @SerializedName(value = "Content2ID")
        private String content2Id;

        @SerializedName(value = "FixContent2ID")
        private String fixContent2ID;

        @SerializedName(value = "DataID")
        private String dataId;

        @SerializedName(value = "ReceiverIDs")
        private Set<Integer> receiverIds;

        @SerializedName(value = "Title")
        private String title;
        @SerializedName(value = "OutEI")
        private Integer outEI;
        @SerializedName(value = "AppId")
        private String appId;

    }

    @Data
    public static class Result {
        private boolean value;
        private boolean success;
        private String message;
        private Integer errorCode;
    }

}
