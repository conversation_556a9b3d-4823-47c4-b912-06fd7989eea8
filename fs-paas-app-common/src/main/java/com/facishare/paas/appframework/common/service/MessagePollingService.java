package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.model.PollingMsgEndType;
import com.facishare.paas.appframework.core.model.User;

public interface MessagePollingService {
    void sendPollingMessage(User user, String pollingKey, PollingMsgEndType pollingMegEndType, boolean isRealTime);

    void sendWholeNetworkPollingMessage(User user, String pollingKey, PollingMsgEndType pollingMegEndType, boolean isRealTime);

    void notifyDescribeLayoutChange(String tenantId, boolean isWholeNetwork);
}
