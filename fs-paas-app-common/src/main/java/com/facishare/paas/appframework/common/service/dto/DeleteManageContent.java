package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.core.rest.InnerAPIResult;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhaooju on 2023/11/14
 */
public interface DeleteManageContent {

    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String userId;
        private String appId;
        private String groupType;
        private List<String> apiNames;
    }

    class RestResult extends InnerAPIResult<Result> {
    }

    class Result {

    }
}
