package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.*;

public interface QueryMembersByDeptIds {

    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        private List<String> idList;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Map<String, List<QueryMembersByDeptIds.Member>> result;
        private boolean success;

        public List<String> getMemberIds() {
            if(CollectionUtils.empty(result)) {
                return Lists.newArrayList();
            } else {
                Set<String> memberIds = Sets.newHashSet();
                result.values().forEach(members -> members.forEach(member -> memberIds.add(member.getId())));
                return Lists.newArrayList(memberIds);
            }
        }
    }

    @Data
    @AllArgsConstructor
    class Member {
        private String id;
        private String name;
        private String email;
        private String dept;
        private String post;
    }
}
