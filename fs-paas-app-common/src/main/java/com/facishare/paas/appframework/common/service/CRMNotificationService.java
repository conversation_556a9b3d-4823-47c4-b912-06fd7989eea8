package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by l<PERSON>yi<PERSON>ng on 2018/3/28.
 */
public interface CRMNotificationService {

    @Deprecated
    void sendCRMNotification(User user, CRMNotification notification);

    void sendNewCrmNotification(User user, NewCrmNotification newCrmNotification);
}
