package com.facishare.paas.appframework.common.service.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * 地理位置相关DTO
 */
public interface GeoAddressDTO {

    @Data
    class ReverseGeoRequest {
        /**
         * 经度
         */
        @NotNull
        private Double longitude;

        /**
         * 纬度
         */
        @NotNull
        private Double latitude;

        /**
         * 是否启用缓存
         */
        private Boolean cache = true;

        /**
         * 是否返回POI列表
         */
        private Boolean returnPois = false;

        /**
         * 语言
         */
        private String language = "zh-CN";

        /**
         * 业务名称
         */
        private String bizName;

        /**
         * 平台
         */
        private String platform = "server";
    }

    @Data
    class GeoResult {
        /**
         * 详细地址
         */
        private String address;
        /**
         * 地点名称
         */
        private String featureName;
    }

    @Data
    class RestResult {
        String code;
        String message;
        GeoResult data;

        public boolean isSuccess() {
            return Objects.equals(code, "200");
        }
    }
} 