package com.facishare.paas.appframework.common.util;

import java.util.Set;

public class FieldManyMaxConfigItem {

    private String apiName;

    private Set<String> tenantId;

    private int employeeManyLimit;

    private int objectReferenceManyLimit;

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public Set<String> getTenantId() {
        return tenantId;
    }

    public void setTenantId(Set<String> tenantId) {
        this.tenantId = tenantId;
    }

    public int getEmployeeManyLimit() {
        return employeeManyLimit;
    }

    public void setEmployeeManyLimit(int employeeManyLimit) {
        this.employeeManyLimit = employeeManyLimit;
    }

    public int getObjectReferenceManyLimit() {
        return objectReferenceManyLimit;
    }

    public void setObjectReferenceManyLimit(int objectReferenceManyLimit) {
        this.objectReferenceManyLimit = objectReferenceManyLimit;
    }
}
