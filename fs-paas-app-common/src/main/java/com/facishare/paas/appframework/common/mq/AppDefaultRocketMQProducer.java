package com.facishare.paas.appframework.common.mq;


import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;

/**
 * create by z<PERSON><PERSON> on 2019/11/07
 */
@Slf4j
public class AppDefaultRocketMQProducer {
    private String config;
    private String sectionName;
    private AutoConfMQProducer mqProducer;
    private String topic;

    public AppDefaultRocketMQProducer(String config) {
        this.config = config;
    }

    public AppDefaultRocketMQProducer(String config,String sectionName) {
        this.config = config;
        this.sectionName = sectionName;
    }

    public void init() {
        mqProducer = new AutoConfMQProducer(config, sectionName);
        topic = mqProducer.getDefaultTopic();
    }

    public void close() {
        if (mqProducer != null) {
            mqProducer.close();
        }
    }


    public SendResult sendMessage(Message message) {
        return doSendMessage(message);
    }

    public SendResult sendMessage(byte[] messageData) {
        return doSendMessage(new DefaultTopicMessage(messageData));
    }

    public SendResult sendMessage(int flag, byte[] messageData) {
        return sendMessage(topic, "", flag, messageData);
    }

    public SendResult sendMessage(String tags, int flag, byte[] messageData) {
        return sendMessage(topic, tags, flag, messageData);
    }

    public SendResult sendMessage(String topic, String tags, byte[] messageData) {
        return sendMessage(topic, tags, 0, messageData);
    }

    public SendResult sendMessage(String topic, String tags, int flag, byte[] messageData) {
        return doSendMessage(new Message(topic, tags, "", flag, messageData, true));
    }

    public SendResult sendMessage(byte[] messageData, int selectorHash) {
        try {
            Message message = new Message(topic, messageData);
            SendResult result = mqProducer.send(message, (mqs, msg, arg) -> {
                int selectHash = (int) arg;
                int index = selectHash % mqs.size();
                return mqs.get(index);
            }, selectorHash);
            SendStatus status = result.getSendStatus();
            log.debug("doSendMessage OK,topic:{} tags:{} status:{}", message.getTopic(), message.getTags(), status);
            return result;
        } catch (Exception e) {
            log.error("send message failed!", e);
            return null;
        }
    }

    public void sendMessage(String topic, String tags, byte[] messageData, String ipGroup) {
        try {
            Message message = new Message(topic, tags, messageData);
            SendResult result = mqProducer.send(message, (mqs, msg, arg) -> {
                int hashCode = ipGroup.replace(".", "").hashCode();
                int index = Math.abs(hashCode % mqs.size());
                log.debug("doSendMessage group:{},index:{}", ipGroup, index);
                return mqs.get(index);
            }, "");
            log.debug("doSendMessage OK,topic:{} tags:{} status:{}", message.getTopic(), message.getTags(), result.getSendStatus());
        } catch (Exception e) {
            log.error("send message failed!", e);
        }
    }

    private SendResult doSendMessage(Message message) {
        return mqProducer.send(message);
    }

    private SendResult doSendMessage(DefaultTopicMessage message) {
        return mqProducer.send(message);
    }
}
