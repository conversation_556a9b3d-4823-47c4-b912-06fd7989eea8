package com.facishare.paas.appframework.common.service;

import com.facishare.netdisk.api.model.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/11 3:22 PM
 */
@RestResource(
        value = "NetworkDisk",
        desc = "网盘接口", // ignoreI18n
        contentType = "application/json")
public interface NetworkDiskProxy {

    @POST(value = "/getFileInfoByNPath", desc = "请求文件信息")
    NSGetFileInfoByNPathResult getFieldInfoByNPath(@HeaderMap Map<String, String> header, @Body NSGetFileInfoByNPathArg arg);

    @POST(value = "/getFoldersByParentId", desc = "获取文件夹以及其下文件夹和文件")
    NSGetFoldersByParentIDResult getFoldersByParentId(@HeaderMap Map<String, String> header, @Body NSGetFoldersByParentIDArg arg);

    @POST(value = "/createFolder", desc = "创建文件夹")
    NSCreateFolderResult createFolder(@HeaderMap Map<String, String> header, @Body NSCreateFolderArg arg);

    @POST(value = "/getDownloadToken", desc = "导出xml并获取token")
    NSGetDownloadTokenResult getDownloadToken(@HeaderMap Map<String, String> header, @Body NSGetDownloadTokenArg arg);

}
