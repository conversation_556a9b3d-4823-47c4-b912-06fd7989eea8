package com.facishare.paas.appframework.common.service.dto;

import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/01/21
 */
public class OrganizationInfo {
    private static final OrganizationInfo EMPTY_VALUE = new OrganizationInfo();

    private OrganizationInfo() {
    }

    private Map<String, OrgInfo> orgInfoMap = Maps.newHashMap();

    public static OrganizationInfo empty() {
        return EMPTY_VALUE;
    }

    public static OrganizationInfo of(Collection<OrgInfo> orgInfos) {
        OrganizationInfo organizationInfo = new OrganizationInfo();
        organizationInfo.orgInfoMap = orgInfos.stream().collect(Collectors.toMap(OrgInfo::getUserId, it -> it));
        return organizationInfo;
    }

    public String getMainDeptId(String userId) {
        return Optional.ofNullable(orgInfoMap.get(userId)).map(OrgInfo::getMainDeptId).orElse(null);
    }

    public String getMainDeptName(String userId) {
        return Optional.ofNullable(orgInfoMap.get(userId)).map(OrgInfo::getMainDeptName).orElse(null);
    }

    public String getMainOrgName(String userId) {
        return Optional.ofNullable(orgInfoMap.get(userId)).map(OrgInfo::getMainOrgName).orElse(null);
    }

    public DeptInfo getMainOrg(String userId) {
        return Optional.ofNullable(orgInfoMap.get(userId)).map(OrgInfo::getMainOrg).orElse(null);
    }

    public String getMainOrgId(String userId) {
        return Optional.ofNullable(orgInfoMap.get(userId)).map(OrgInfo::getMainOrgId).orElse(null);
    }


    @Data
    @Builder
    public static class OrgInfo {
        private String userId;
        private DeptInfo mainOrg;
        private DeptInfo mainDept;

        public String getMainDeptId() {
            if (Objects.isNull(mainDept)) {
                return null;
            }
            return mainDept.getDeptId();
        }

        public String getMainDeptName() {
            if (Objects.isNull(mainDept)) {
                return null;
            }
            return mainDept.getDeptName();
        }

        public String getMainOrgId() {
            if (Objects.isNull(mainOrg)) {
                return null;
            }
            return mainOrg.getDeptId();
        }

        public String getMainOrgName() {
            if (Objects.isNull(mainOrg)) {
                return null;
            }
            return mainOrg.getDeptName();
        }
    }
}
