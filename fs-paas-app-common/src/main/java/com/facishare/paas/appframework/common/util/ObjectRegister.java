package com.facishare.paas.appframework.common.util;

import com.google.common.collect.Sets;

import java.util.Set;

public class ObjectRegister {
    private static final Set<String> objectApiNames = Sets.newHashSet();

    public static void registerObjectApiName(String objectApiName) {
        objectApiNames.add(objectApiName);
    }

    public static boolean containsObject(String objectApiName) {
        return objectApiNames.contains(objectApiName);
    }
}
