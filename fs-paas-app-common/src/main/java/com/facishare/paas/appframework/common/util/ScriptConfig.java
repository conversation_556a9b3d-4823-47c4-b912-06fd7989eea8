package com.facishare.paas.appframework.common.util;

import com.github.autoconf.ConfigFactory;
import org.apache.commons.lang3.StringUtils;

public class ScriptConfig {

    // KEYS结构:[
//    {
//        "dataDup": ["key1", "key2", "key3"],
//        "geoDup": ["DUPLICATED_GEO_123_Account_address_120.5_30.5_1000"]
//    },
//    {
//        "dataDup": ["key7", "key8", "key9"],
//        "geoDup": ["DUPLICATED_GEO_123_Contact_location_121.5_31.5_2000"]
//    }
//]
    public static String SEARCH_LUA_SCRIPT_V2_CONFIG = "";
    public static String SAVE_LUA_SCRIPT_V2_CONFIG = "";
    public static String SEARCH_LUA_SCRIPT_V2 = "local arg = ARGV[1]\n" +
            "local temp_keys = {}\n" +
            "\n" +
            "-- 简单的ZSET转SET函数\n" +
            "local function convertToSet(key)\n" +
            "    -- 检查键是否为ZSET类型\n" +
            "    if redis.call(\"exists\", key) == 1 then\n" +
            "        local type = redis.call(\"type\", key).ok\n" +
            "        if type == \"zset\" then\n" +
            "            -- 获取所有成员\n" +
            "            local members = redis.call(\"zrange\", key, 0, -1)\n" +
            "            -- 删除原键\n" +
            "            redis.call(\"del\", key)\n" +
            "            -- 如果有成员，创建SET\n" +
            "            if #members > 0 then\n" +
            "                redis.call(\"sadd\", key, unpack(members))\n" +
            "            end\n" +
            "        end\n" +
            "    end\n" +
            "end\n" +
            "\n" +
            "-- 处理每个Map元素\n" +
            "for i, value in ipairs(KEYS) do\n" +
            "    local body = cjson.decode(value)\n" +
            "    local data_temp_key = arg .. \"_data_\" .. tostring(i)\n" +
            "    local geo_temp_key = arg .. \"_geo_\" .. tostring(i)\n" +
            "    local final_temp_key = arg .. \"_final_\" .. tostring(i)\n" +
            "\n" +
            "    -- 处理标志\n" +
            "    local has_data = false\n" +
            "    local has_geo = false\n" +
            "\n" +
            "    -- 处理dataDup\n" +
            "    if body.dataDup and #body.dataDup > 0 then\n" +
            "        redis.call(\"sinterstore\", data_temp_key, unpack(body.dataDup))\n" +
            "        has_data = redis.call(\"exists\", data_temp_key) == 1\n" +
            "    end\n" +
            "\n" +
            "    -- 处理geoDup\n" +
            "    if body.geoDup and #body.geoDup > 0 then\n" +
            "        local first_geo = true\n" +
            "\n" +
            "        -- 处理每个geo键\n" +
            "        for _, geoKey in ipairs(body.geoDup) do\n" +
            "            local parts = {}\n" +
            "            for part in string.gmatch(geoKey, \"[^:]+\") do\n" +
            "                table.insert(parts, part)\n" +
            "            end\n" +
            "\n" +
            "            if #parts >= 9 then\n" +
            "                local preKey = parts[1] .. \":\" .. parts[2]\n" +
            "                local tenantId = parts[3]\n" +
            "                local objectApiName = parts[4]\n" +
            "                local version = parts[5]\n" +
            "                local fieldApiName = parts[6]\n" +
            "                local longitude = tonumber(parts[7])\n" +
            "                local latitude = tonumber(parts[8])\n" +
            "                local distance = tonumber(parts[9])\n" +
            "\n" +
            "                if tenantId and objectApiName and fieldApiName and longitude and latitude and distance then\n" +
            "                    local real_geo_key = preKey .. \":\" .. tenantId .. \":\" .. objectApiName .. \":\" .. version .. \":\" ..\n" +
            "                                             fieldApiName\n" +
            "                    local temp_result_key = geo_temp_key .. \"_temp\"\n" +
            "\n" +
            "                    -- 执行地理位置查询\n" +
            "                    if redis.call(\"exists\", real_geo_key) == 1 then\n" +
            "                        redis.call(\"georadius\", real_geo_key, longitude, latitude, distance, \"m\", \"store\",\n" +
            "                            temp_result_key)\n" +
            "                        -- 将ZSET结果转为SET\n" +
            "                        convertToSet(temp_result_key)\n" +
            "\n" +
            "                        if first_geo then\n" +
            "                            -- 第一个geo查询结果直接复制到geo_temp_key\n" +
            "                            redis.call(\"rename\", temp_result_key, geo_temp_key)\n" +
            "                            first_geo = false\n" +
            "                        else\n" +
            "                            -- 后续的geo查询结果与之前的结果取交集\n" +
            "                            redis.call(\"sinterstore\", geo_temp_key, geo_temp_key, temp_result_key)\n" +
            "                            redis.call(\"del\", temp_result_key)\n" +
            "                        end\n" +
            "                    end\n" +
            "                end\n" +
            "            end\n" +
            "        end\n" +
            "        if not first_geo then\n" +
            "            -- 确保geo_temp_key是SET类型\n" +
            "            convertToSet(geo_temp_key)\n" +
            "            has_geo = true\n" +
            "        end\n" +
            "    end\n" +
            "\n" +
            "    -- 合并dataDup和geoDup的结果\n" +
            "    if has_data and has_geo then\n" +
            "        -- 两个都存在，取交集\n" +
            "        redis.call(\"sinterstore\", final_temp_key, data_temp_key, geo_temp_key)\n" +
            "    elseif has_data then\n" +
            "        -- 只有dataDup结果\n" +
            "        redis.call(\"rename\", data_temp_key, final_temp_key)\n" +
            "    elseif has_geo then\n" +
            "        -- 只有geoDup结果\n" +
            "        redis.call(\"rename\", geo_temp_key, final_temp_key)\n" +
            "        -- 确保final_temp_key是SET类型\n" +
            "        convertToSet(final_temp_key)\n" +
            "    end\n" +
            "\n" +
            "    -- 清理中间结果键\n" +
            "    if has_data then\n" +
            "        redis.call(\"del\", data_temp_key)\n" +
            "    end\n" +
            "    if has_geo then\n" +
            "        redis.call(\"del\", geo_temp_key)\n" +
            "    end\n" +
            "\n" +
            "    -- 只添加最终结果到temp_keys\n" +
            "    if redis.call(\"exists\", final_temp_key) == 1 then\n" +
            "        -- 确保所有临时结果都是SET类型\n" +
            "        convertToSet(final_temp_key)\n" +
            "        table.insert(temp_keys, final_temp_key)\n" +
            "    end\n" +
            "end\n" +
            "\n" +
            "-- 计算所有Map结果的并集\n" +
            "local result_key = arg .. \"_result\"\n" +
            "if #temp_keys > 0 then\n" +
            "    redis.call(\"del\", result_key)\n" +
            "\n" +
            "    -- 找到第一个有效的集合\n" +
            "    local first_valid_key = nil\n" +
            "    for _, key in ipairs(temp_keys) do\n" +
            "        if redis.call(\"exists\", key) == 1 then\n" +
            "            first_valid_key = key\n" +
            "            break\n" +
            "        end\n" +
            "    end\n" +
            "\n" +
            "    -- 如果找到有效集合，初始化结果集\n" +
            "    if first_valid_key then\n" +
            "        redis.call(\"sunionstore\", result_key, first_valid_key)\n" +
            "\n" +
            "        -- 合并其余的集合\n" +
            "        for _, key in ipairs(temp_keys) do\n" +
            "            if key ~= first_valid_key and redis.call(\"exists\", key) == 1 then\n" +
            "                redis.call(\"sunionstore\", result_key, result_key, key)\n" +
            "            end\n" +
            "        end\n" +
            "    end\n" +
            "\n" +
            "    -- 获取最终结果\n" +
            "    local result = redis.call(\"smembers\", result_key)\n" +
            "\n" +
            "    -- 清理所有临时键\n" +
            "    redis.call(\"del\", result_key)\n" +
            "    for _, key in ipairs(temp_keys) do\n" +
            "        redis.call(\"del\", key)\n" +
            "    end\n" +
            "\n" +
            "    return result\n" +
            "else\n" +
            "    return {}\n" +
            "end\n";


    public static String SAVE_LUA_SCRIPT_V2 = "local expireTime = tonumber(ARGV[1])\n" +
            "local jsonData = ARGV[2]\n" +
            "\n" +
            "redis.log(redis.LOG_WARNING, \"Received JSON: \" .. jsonData:sub(1, 20) .. \"...\")\n" +
            "\n" +
            "\n" +
            "-- 验证输入参数\n" +
            "if not expireTime or expireTime <= 0 then\n" +
            "    return redis.error_reply(\"Invalid expire time\")\n" +
            "end\n" +
            "\n" +
            "-- 验证并解析JSON数据\n" +
            "local success, data = pcall(cjson.decode, jsonData)\n" +
            "if not success or type(data) ~= \"table\" then\n" +
            "    return redis.error_reply(\"Invalid JSON data\")\n" +
            "end\n" +
            "\n" +
            "-- 创建一个表来存储成功插入的key\n" +
            "local successKeys = {}\n" +
            "\n" +
            "-- 处理dataDup部分\n" +
            "if data.dataDup then\n" +
            "    for key, values in pairs(data.dataDup) do\n" +
            "        if type(values) == \"table\" and #values > 0 then\n" +
            "            -- 删除原有数据并添加新数据\n" +
            "            redis.call(\"DEL\", key)\n" +
            "            redis.call(\"SADD\", key, unpack(values))\n" +
            "            redis.call(\"EXPIRE\", key, expireTime)\n" +
            "            -- 如果集合中有元素，表示添加成功，记录key\n" +
            "            if redis.call(\"SCARD\", key) > 0 then\n" +
            "                table.insert(successKeys, key)\n" +
            "            end\n" +
            "        end\n" +
            "    end\n" +
            "end\n" +
            "\n" +
            "-- 处理geoDup部分\n" +
            "if data.geoDup then\n" +
            "    for key, geoData in pairs(data.geoDup) do\n" +
            "        if type(geoData) == \"table\" then\n" +
            "            -- 删除原有数据\n" +
            "            redis.call(\"DEL\", key)\n" +
            "            local addedAny = false\n" +
            "            -- 处理每个ID的地理位置信息\n" +
            "            for id, coords in pairs(geoData) do\n" +
            "                if type(coords) == \"table\" and #coords == 2 and tonumber(coords[1]) and tonumber(coords[2]) then\n" +
            "                    -- 交换经纬度顺序，保证是经度在前，纬度在后\n" +
            "                    local lon, lat = coords[1], coords[2] -- 原来是[纬度,经度]，交换为[经度,纬度]\n" +
            "                    redis.call(\"GEOADD\", key, lon, lat, id)\n" +
            "                    addedAny = true\n" +
            "                end\n" +
            "            end\n" +
            "            -- 如果有数据添加成功，设置过期时间并记录key\n" +
            "            if addedAny and redis.call(\"EXISTS\", key) == 1 then\n" +
            "                redis.call(\"EXPIRE\", key, expireTime)\n" +
            "                table.insert(successKeys, key)\n" +
            "            end\n" +
            "        end\n" +
            "    end\n" +
            "end\n" +
            "\n" +
            "-- 检查是否成功添加了数据\n" +
            "local hasAddedData = #successKeys > 0\n" +
            "\n" +
            "-- 根据是否成功添加数据返回结果\n" +
            "if hasAddedData then\n" +
            "    -- 将成功的key列表序列化为JSON返回\n" +
            "    return cjson.encode(successKeys)\n" +
            "else\n" +
            "    return cjson.encode({})\n" +
            "end\n" +
            "\n";


    static {
        ConfigFactory.getConfig("fs-paas-script-search-config", config -> {
            SEARCH_LUA_SCRIPT_V2_CONFIG = config.getString();
        });
        ConfigFactory.getConfig("fs-paas-script-save-config", config -> {
            SAVE_LUA_SCRIPT_V2_CONFIG = config.getString();
        });
    }

    public static String getSearchLuaScriptV2() {
        if (StringUtils.isEmpty(SEARCH_LUA_SCRIPT_V2_CONFIG)) {
            return SEARCH_LUA_SCRIPT_V2;
        }
        return SEARCH_LUA_SCRIPT_V2_CONFIG;
    }

    public static String getSaveLuaScriptV2() {
        if (StringUtils.isEmpty(SAVE_LUA_SCRIPT_V2_CONFIG)) {
            return SAVE_LUA_SCRIPT_V2;
        }
        return SAVE_LUA_SCRIPT_V2_CONFIG;
    }
}
