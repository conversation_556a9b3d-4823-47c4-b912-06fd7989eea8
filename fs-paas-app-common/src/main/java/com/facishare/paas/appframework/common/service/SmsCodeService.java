package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.MobileInfo;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.userlogin.api.model.validatecode.BuildValidateCode;

/**
 * 短信验证码发送和验证
 */
public interface SmsCodeService {

    /**
     * 发送短信验证码
     * <p>1. 生成短信验证码（包括验证前校验如是否能发送短信验证码、ip\手机号频次、是否需要图形验证码验证等）
     * 使用格式化后的手机号码作为验证码的 key: {@link MobileInfo#smsMobile()}
     * <p>2. 根据当前语言生成短信内容
     * <p>3. 发送短信
     *
     * @param ea          企业账号
     * @param user        操作人
     * @param ip          操作客户端 IP 地址
     * @param areaCode    区码
     * @param mobile      移动手机号码
     * @param captchaCode 图形验证码
     * @param captchaId   图形验证码 ID
     * @param biz         业务 CRM、LoginDynamicPassword 等
     * @param i18nKey     短信模版
     * @return SendValidateCodeEnum
     */
    String sendSmsCode(String ea, User user, String ip, String areaCode, String mobile, String captchaCode, String captchaId, String biz, String i18nKey);

    /**
     * 生成短信验证码
     * <p> 通过 {@link #verifySmsCode(User, String, String, String)} 验证验证码
     * <p> 生成验证码的 mobile 要和验证时的 mobile 保持一致
     *
     * @param user 操作人
     * @param ip 操作客户端 ip
     * @param areaCode 区号
     * @param mobile 移动手机号码
     * @param captchaCode 根据图形提交的图形验证码
     * @param captchaId 图形验证码ID
     * @param expireTime 有效期
     * @return 结果
     */
    BuildValidateCode.Result createSmsCode(User user, String ip, String areaCode, String mobile, String captchaCode, String captchaId, int expireTime);

    /**
     * 验证短信验证码
     * <p> 通过 {@link #createSmsCode(User, String, String, String, String, String, int)} 生成验证码
     *
     * @param user 验证人
     * @param areaCode 区号
     * @param mobile 移动手机号码
     * @param smsCode 短信验证码
     * @return 是否成功
     */
    String verifySmsCode(User user, String areaCode, String mobile, String smsCode);

    /**
     * 发送短信
     *
     * @param ea 企业账号
     * @param user 操作人
     * @param areaCode 区号
     * @param mobile 移动手机号
     * @param biz 业务
     * @param content 短信内容
     * @return 发送结果
     */
    boolean sendSMS( String ea, User user, String areaCode, String mobile, String biz, String content);
}
