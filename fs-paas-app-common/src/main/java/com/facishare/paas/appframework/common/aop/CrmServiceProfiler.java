package com.facishare.paas.appframework.common.aop;

import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.foundation.boot.exception.BadRequestException;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.exception.MetadataValidateException;
import com.fxiaoke.sql.exception.ElasticSql2DslException;
import com.github.trace.aop.ServiceProfiler;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.dao.DuplicateKeyException;

import java.util.List;

/**
 * Created by zhouwr on 2020/5/27
 */
public class CrmServiceProfiler extends ServiceProfiler {

    @Override
    protected boolean isFail(Throwable e) {
        List<Throwable> ts = ExceptionUtils.getThrowableList(e);
        for (Throwable t : ts) {
            if (t instanceof AppBusinessException
                    || t instanceof MetadataServiceException
                    || t instanceof MetadataValidateException
                    || t instanceof BadRequestException
                    || t instanceof ElasticSql2DslException
                    || t instanceof DuplicateKeyException) {
                return false;
            }
        }
        return super.isFail(e);
    }
}
