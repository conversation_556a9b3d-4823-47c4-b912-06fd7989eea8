package com.facishare.paas.appframework.common.util;

import com.github.autoconf.ConfigFactory;

public abstract class StoneCGIConfig {
    private static volatile String fileShareSkey = "";

    static {
        ConfigFactory.getConfig("fs-stone-cgi-common", config -> {
            fileShareSkey = config.get("sKey");
        });
    }

    public static String getFileShareSkey() {
        return fileShareSkey;
    }
}
