package com.facishare.paas.appframework.common.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.*;
import com.facishare.organization.api.model.department.result.*;
import com.facishare.organization.api.model.organizationtree.OrganizationTreeDepartment;
import com.facishare.organization.api.model.organizationtree.arg.BatchGetOrganizationTreeDepartmentArg;
import com.facishare.organization.api.model.organizationtree.result.BatchGetOrganizationTreeDepartmentResult;
import com.facishare.organization.api.model.param.BatchGetUpperDepartmentDtoMap;
import com.facishare.organization.api.model.param.BatchGetUpperDepartmentIdsMap;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.OrganizationTreeService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ConvertUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds.DeptStatusEnum;

/**
 * <AUTHOR>
 * @date 2019/12/26 11:03 上午
 */
@Service
@Slf4j
public class DepartmentServiceImpl implements DepartmentService {
    // 组织结构提供的部门接口
    @Autowired
    private DepartmentProviderService departmentProviderService;

    @Autowired
    private OrganizationTreeService organizationTreeService;


    @Override
    public List<DepartmentDto> batchGetDepartment(String tenantId, Collection<String> deptIds, DeptStatusEnum status) {
        if (CollectionUtils.empty(deptIds)) {
            return Lists.newArrayList();
        }
        try {
            BatchGetDepartmentDtoArg arg = new BatchGetDepartmentDtoArg();
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setDepartmentIds(ConvertUtils.convertStringCollectionToIntegerList(deptIds));
            arg.setRunStatus(convertDeptStatusToRunStatus(status));
            BatchGetDepartmentDtoResult result = departmentProviderService.batchGetDepartmentDto(arg);
            return CollectionUtils.nullToEmpty(result.getDepartments());
        } catch (Exception e) {
            log.error("batch get main department error, tenantId:{}, deptIds:{}", tenantId, deptIds, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<DepartmentDto> batchGetDepartment(String tenantId, Collection<String> deptIds) {
        return batchGetDepartment(tenantId, deptIds, DeptStatusEnum.ALL);
    }

    @Override
    public List<DepartmentDto> getLowDepartment(String tenantId, String deptId, DeptStatusEnum status) {
        try {
            GetLowDepartmentsDtoArg arg = new GetLowDepartmentsDtoArg();
            arg.setDepartmentId(ConvertUtils.convertStringToInteger(deptId));
            arg.setSelf(true);
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(convertDeptStatusToRunStatus(status));
            GetLowDepartmentsDtoResult result = departmentProviderService.getLowDepartmentsDto(arg);
            return CollectionUtils.nullToEmpty(result.getDepartmentDtos());
        } catch (Exception e) {
            log.error("get low departments error, tenantId:{}, deptId:{}", tenantId, deptId, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<DepartmentDto> batchGetDepartmentByNames(String tenantId, List<String> deptNames, DeptStatusEnum status) {
        try {
            if (AppFrameworkConfig.objectMultiLangGray(tenantId, Utils.DEPARTMENT_OBJ_API_NAME)) {
                return getDepartmentByNamesMultiLanguage(tenantId, deptNames, status);
            }
            GetDepartmentByNamesArg arg = new GetDepartmentByNamesArg();
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(convertDeptStatusToRunStatus(status));
            arg.setNames(deptNames);
            GetDepartmentByNamesResult result = departmentProviderService.getDepartmentByNames(arg);
            return CollectionUtils.nullToEmpty(result.getDepartments());
        } catch (Exception e) {
            log.error("get departments by exact names error, tenantId:{}, names:{}", tenantId, deptNames, e);
            throw new RuntimeException(e);
        }
    }

    private List<DepartmentDto> getDepartmentByNamesMultiLanguage(String tenantId, List<String> deptNames, DeptStatusEnum status) {
        GetDepartmentByNamesMultiLanguageArg arg = new GetDepartmentByNamesMultiLanguageArg();
        arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
        arg.setRunStatus(convertDeptStatusToRunStatus(status));
        arg.setNames(deptNames);
        arg.setLanguageType(I18N.getContext().getLanguage());
        GetDepartmentByNamesResult result = departmentProviderService.getDepartmentByNamesMultiLanguage(arg);
        return CollectionUtils.nullToEmpty(result.getDepartments());
    }

    @Override
    public List<DepartmentDto> batchGetDepartmentByPrincipal(String tenantId, List<String> manageIdList, DeptStatusEnum status) {
        if (CollectionUtils.empty(manageIdList)) {
            return Lists.newArrayList();
        }
        try {
            BatchGetDepartmentByPrincipalArg arg = new BatchGetDepartmentByPrincipalArg();
            arg.setPrincipalIds(ConvertUtils.batchConvertStringToInteger(manageIdList));
            arg.setRunStatus(convertDeptStatusToRunStatus(status));
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            BatchGetDepartmentByPrincipalResult result = departmentProviderService.batchGetDepartmentByPrincipal(arg);
            return CollectionUtils.nullToEmpty(result.getDepartments());
        } catch (Exception e) {
            log.error("get departments by principal error, tenantId:{}, managerIdList:{}", tenantId, manageIdList, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<DepartmentDto> getDepartmentByEmployeeId(String tenantId, String userId) {
        if (StringUtils.isBlank(userId)) {
            return Lists.newArrayList();
        }
        try {
            GetDepartmentDtoByEmployeeIdArg arg = new GetDepartmentDtoByEmployeeIdArg();
            arg.setEmployeeId(ConvertUtils.convertStringToInteger(userId));
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            GetDepartmentDtoByEmployeeIdResult result = departmentProviderService.getDepartmentDtoByEmployeeId(arg);
            return CollectionUtils.nullToEmpty(result.getDepartments());
        } catch (Exception e) {
            log.error("get department by employee id error, tenantId:{}, userId:{}", tenantId, userId, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, List<DepartmentDto>> batchGetUpperDepartment(String tenantId, List<String> deptIds, boolean includeSelf) {
        if (CollectionUtils.empty(deptIds)) {
            return Maps.newHashMap();
        }
        try {
            BatchGetUpperDepartmentDtoMap.Arg arg = new BatchGetUpperDepartmentDtoMap.Arg();
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setDepartmentIds(ConvertUtils.batchConvertStringToInteger(deptIds));
            arg.setRunStatus(RunStatus.ALL);
            arg.setRecursive(true);
            arg.setSelf(includeSelf);
            BatchGetUpperDepartmentDtoMap.Result result = departmentProviderService.batchGetUpperDepartmentDtoMap(arg);
            Map<Integer, List<Integer>> deptIdMap = CollectionUtils.nullToEmpty(result.getUpperDepartmentIdMap());
            Map<Integer, DepartmentDto> departmentDtoMap = CollectionUtils.nullToEmpty(result.getDepartmentDtoMap());
            Map<String, List<DepartmentDto>> resultMap = Maps.newHashMap();
            deptIds.forEach(id -> {
                List<Integer> upperIdList = deptIdMap.get(ConvertUtils.convertStringToInteger(id));
                if (CollectionUtils.notEmpty(upperIdList)) {
                    List<DepartmentDto> departments = upperIdList.stream()
                            .filter(departmentDtoMap::containsKey)
                            .map(departmentDtoMap::get).collect(Collectors.toList());
                    resultMap.put(id, departments);
                } else {
                    resultMap.put(id, Lists.newArrayList());
                }
            });


            deptIdMap.forEach((id, upperIdList) -> {
                List<DepartmentDto> departments = upperIdList.stream()
                        .filter(departmentDtoMap::containsKey)
                        .map(departmentDtoMap::get).collect(Collectors.toList());
                resultMap.put(String.valueOf(id), departments);
            });
            return resultMap;
        } catch (Exception e) {
            log.error("batch get upper department error, tenantId:{}, deptIds:{}", tenantId, deptIds, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, List<String>> batchGetUpperDepartmentIds(String tenantId, List<String> deptIds, boolean includeSelf) {
        if (CollectionUtils.empty(deptIds)) {
            return Maps.newHashMap();
        }
        BatchGetUpperDepartmentIdsMap.Arg arg = new BatchGetUpperDepartmentIdsMap.Arg();
        arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
        arg.setDepartmentIds(ConvertUtils.batchConvertStringToInteger(deptIds));
        arg.setRunStatus(RunStatus.ALL);
        arg.setRecursive(true);
        arg.setSelf(includeSelf);
        try {
            BatchGetUpperDepartmentIdsMap.Result result = departmentProviderService.batchGetUpperDepartmentIdsMap(arg);
            return CollectionUtils.nullToEmpty(result.getUpperDepartmentIdMap()).entrySet().stream()
                    .collect(Collectors.toMap(it -> ConvertUtils.integerToString(it.getKey()),
                            it -> ConvertUtils.batchConvertIntegerToString(it.getValue())));
        } catch (RuntimeException e) {
            log.warn("batch get upper department ids failed, tenantId:{}, deptIds:{}", tenantId, deptIds, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, List<String>> batchGetUpperDepartmentId(String tenantId, Collection<String> deptIds, boolean includeSelf) {
        if (CollectionUtils.empty(deptIds)) {
            return Maps.newHashMap();
        }
        BatchGetOrganizationTreeDepartmentArg arg = new BatchGetOrganizationTreeDepartmentArg();
        arg.setDepartmentIds(ConvertUtils.batchConvertStringToInteger(deptIds));
        arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));

        BatchGetOrganizationTreeDepartmentResult batchGetUpperDepartmentDtoMap = organizationTreeService.batchGetOrganizationTreeDepartment(arg);

        List<OrganizationTreeDepartment> departmentOrgList = CollectionUtils.nullToEmpty(batchGetUpperDepartmentDtoMap.getDepartments());
        departmentOrgList.forEach(x -> x.getAncestors().add(x.getDepartmentId()));
        Map<Integer, List<Integer>> deptIdMap = departmentOrgList.stream().collect(Collectors.toMap(OrganizationTreeDepartment::getDepartmentId, OrganizationTreeDepartment::getAncestors, (x, y) -> y));

        Map<String, List<String>> result = Maps.newHashMap();
        deptIdMap.keySet().forEach(x -> result.put(ConvertUtils.integerToString(x), ConvertUtils.batchConvertIntegerToString(deptIdMap.get(x))));
        return result;
    }

    @Override
    public Map<String, List<DepartmentDto>> batchGetLowDepartment(String tenantId,
                                                                  List<String> deptIds,
                                                                  DeptStatusEnum status,
                                                                  boolean self) {
        if (CollectionUtils.empty(deptIds)) {
            return Maps.newHashMap();
        }
        try {
            BatchGetLowDepartmentsDtoArg arg = new BatchGetLowDepartmentsDtoArg();
            arg.setDepartmentIds(ConvertUtils.batchConvertStringToInteger(deptIds));
            arg.setSelf(self);
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(convertDeptStatusToRunStatus(status));
            BatchGetLowDepartmentsDtoResult result = departmentProviderService.batchGetLowDepartment(arg);
            List<DepartmentDto> departments = CollectionUtils.nullToEmpty(result.getDepartmentDtos());
            Map<String, List<DepartmentDto>> mapResult = Maps.newHashMap();
            for (String id : deptIds) {
                List<DepartmentDto> lowDeptList = Lists.newArrayList();
                for (DepartmentDto dept : departments) {
                    if (dept.getAncestors().contains(ConvertUtils.convertStringToInteger(id)) ||
                            id.equals(String.valueOf(dept.getDepartmentId()))) {
                        lowDeptList.add(dept);
                    }
                }
                mapResult.put(id, lowDeptList);
            }
            return mapResult;
        } catch (Exception e) {
            log.error("batch get low departments error, tenantId:{}, deptIds:{}", tenantId, deptIds, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, List<String>> batchGetLowDepartmentIdsMap(String tenantId, List<String> deptIds, DeptStatusEnum status, boolean self) {
        if (CollectionUtils.empty(deptIds)) {
            return Maps.newHashMap();
        }
        try {
            BatchGetLowDepartmentIdsMap.Arg arg = new BatchGetLowDepartmentIdsMap.Arg();
            arg.setDepartmentIds(ConvertUtils.batchConvertStringToInteger(deptIds));
            arg.setSelf(self);
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            arg.setRunStatus(convertDeptStatusToRunStatus(status));
            BatchGetLowDepartmentIdsMap.Result result = departmentProviderService.batchGetLowDepartmentIdsMap(arg);

            Map<Integer, Set<Integer>> lowDepartmentIdsMap = result.getLowDepartmentIdsMap();
            if (CollectionUtils.empty(lowDepartmentIdsMap)) {
                return Maps.newHashMap();
            }

            Map<String, List<String>> resultMap = Maps.newHashMap();
            lowDepartmentIdsMap.forEach((id, lowDeptIds) ->
                    resultMap.put(String.valueOf(id), ConvertUtils.batchConvertIntegerToString(lowDeptIds)));
            return resultMap;
        } catch (Exception e) {
            log.error("batch get low department ids map error, tenantId:{}, deptIds:{}", tenantId, deptIds, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<DepartmentDto> getUpperDepartments(String tenantId, String deptId, boolean self) {
        if (Objects.isNull(deptId)) {
            return Lists.newArrayList();
        }
        try {
            GetUpperDepartmentDtoArg arg = new GetUpperDepartmentDtoArg();
            arg.setSelf(self);
            arg.setDepartmentId(ConvertUtils.convertStringToInteger(deptId));
            arg.setEnterpriseId(ConvertUtils.convertStringToInteger(tenantId));
            GetUpperDepartmentDtoResult result = departmentProviderService.getUpperDepartmentDto(arg);
            return CollectionUtils.nullToEmpty(result.getDepartments());
        } catch (Exception e) {
            log.error("get upper departments error, tenantId:{}, deptId:{}", tenantId, deptId, e);
            throw new RuntimeException(e);
        }
    }

    private RunStatus convertDeptStatusToRunStatus(DeptStatusEnum status) {
        switch (status) {
            case ALL:
                return RunStatus.ALL;
            case ENABLE:
                return RunStatus.ACTIVE;
            case DISABLE:
                return RunStatus.STOP;
        }
        return RunStatus.ALL;
    }

    private DeptStatusEnum convertRunStatusToDeptStatus(RunStatus status) {
        switch (status) {
            case ACTIVE:
                return DeptStatusEnum.ENABLE;
            case STOP:
                return DeptStatusEnum.DISABLE;
            case ALL:
                return DeptStatusEnum.ALL;
        }
        return DeptStatusEnum.ALL;
    }
}
