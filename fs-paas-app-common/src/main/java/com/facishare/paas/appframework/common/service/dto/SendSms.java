package com.facishare.paas.appframework.common.service.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2023-09-20 11:22
 * @Description
 */
public interface SendSms {
    @Data
    class Arg {
        // 短信发送的使用方：1 营销通，2 服务通，5 PRM，6 工作流，8 CRM验证码
        private Integer channelType;
        // 企业账号，在 templateId 不为空时，该值可以为空
        private String ea;
        private List<PhoneData> phoneDataList;
        // 短信模板id
        private String templateId;
        // 无短信模板的短信发送内容
        private String templateContent;
        // 短信发送类型：1 及时发送；2 定时发送
        private Integer type;
        // 单位为 ms 的时间戳，type=2 时，需要填入该值，表示定时发送的发送时间
        private Long scheduleTime;
        // 短信发送人的纷享 userId
        private Integer userId;
        // 对象数据 id
        private String objectId;
    }

    @Data
    class Result {
        // 请求结果 0 表示请求成功
        int status;
    }

    /**
     * 短信的详细信息列表，
     * paramMap 是个 map,key 为参数名，value 为参数值
     * phoneList 为接受短信的手机号码可以多个（当前只支持中国大陆短信）
     */
    @Data
    class PhoneData {
        Map<String, String> paramMap;
        List<String> phoneList;
    }
}
