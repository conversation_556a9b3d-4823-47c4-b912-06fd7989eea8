package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.FindManageGroupObjects;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "PlatProviderServiceProxy",
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC")
public interface PlatProviderServiceProxy {
    @POST(value = "/getObjListByManageGroup", desc = "查询小组可用对象")
    FindManageGroupObjects.Result findManageGroupObjects(@HeaderMap Map<String, String> header, @Body FindManageGroupObjects.Arg arg);
}
