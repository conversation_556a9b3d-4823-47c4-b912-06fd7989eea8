package com.facishare.paas.appframework.common.util;

import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;


@Slf4j
public abstract class UdobjGrayUtil {


    private static GrayConfig GRAY_CONFIG;

    public static final String CUSTOM_OBJECT = "udobj";

    static {
        ConfigFactory.getConfig("fs-paas-gray-config", config -> {
            GrayConfig grayConfig = JacksonUtils.fromJson(config.getString(), GrayConfig.class);
            if (Objects.nonNull(grayConfig)) {
                GRAY_CONFIG = grayConfig;
            } else {
                log.error("load fs-paas-gray-config error");
            }
        });
    }


    public static boolean isObjectAndTenantGray(String functionName, String tenantId, String objectApiName) {
        if (Objects.isNull(GRAY_CONFIG)) {
            return false;
        }

        if (StringUtils.isAnyBlank(functionName, tenantId, objectApiName)) {
            return false;
        }

        if (isCustomObject(objectApiName)) {
            objectApiName = CUSTOM_OBJECT;
        }

        Optional<Function> optionalFunction = GRAY_CONFIG.getFunctions().stream()
                .filter(x -> Objects.equals(x.getFunctionName(), functionName))
                .findFirst();
        if (!optionalFunction.isPresent()) {
            return false;
        }

        Function function = optionalFunction.get();
        List<Rule> whiteRule = function.getWhiteRule();
        if (CollectionUtils.notEmpty(whiteRule)) {
            for (Rule rule : whiteRule) {
                if (CollectionUtils.nullToEmpty(rule.getKeys()).contains(objectApiName)) {
                    return rule.getEi().isAllow(tenantId);
                }
            }
        }
        Rule blackRule = function.getBlackRule();
        if (Objects.nonNull(blackRule)) {
            if (!CollectionUtils.nullToEmpty(blackRule.getKeys()).contains(objectApiName)) {
                return blackRule.getEi().isAllow(tenantId);
            }
        }
        return false;
    }

    public static boolean isCustomObject(String objectApiName) {
        return StringUtils.endsWith(objectApiName, "__c") || StringUtils.endsWithIgnoreCase(objectApiName, CUSTOM_OBJECT);
    }

    @Data
    public static class GrayConfig {
        private List<Function> functions;
    }

    @Data
    public static class Function {
        /**
         * 功能的灰度项
         */
        private String functionName;
        private Rule blackRule;
        private List<Rule> whiteRule;
    }


    @Data
    public static class Rule {
        /**
         * 一级分类，例如对象的API_NAME
         */
        private Set<String> keys;
        private GrayRule ei;

        public void setEi(String ei) {
            this.ei = new GrayRule(ei);
        }
    }
}
