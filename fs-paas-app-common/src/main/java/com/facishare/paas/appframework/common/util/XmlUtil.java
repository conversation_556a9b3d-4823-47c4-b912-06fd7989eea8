package com.facishare.paas.appframework.common.util;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * XML 工具类
 * 从 fs-paas-app-metadata 移动到 fs-paas-app-common，作为基础工具类
 * 创建xml
 * 
 * <AUTHOR>
 * @date 2019/1/7 9:47 AM
 */
public class XmlUtil {

    /* xml根元素名称 */
    private final static String DEFAULT_ROOT_NAME = "Document";
    /* 表示目录的属性 */
    private final static String DIRECTORY = "D";
    /* 表示名称的属性 */
    private final static String NAME = "N";
    /* 表示文件的属性 */
    private final static String FILE = "F";
    /* 表示NPath的属性 */
    private final static String NP = "NP";

    /**
     * 构建一级目录结构
     *
     * @param describe 对象名称作为一级目录
     * @param dataList 需要导出的数据
     * @return xml的字符串
     */
    public static String create1LayerXml(IObjectDescribe describe, List<IFieldDescribe> fieldsToExport, List<IObjectData> dataList) {
        return create1LayerXml(describe, fieldsToExport, dataList, false);
    }

    public static String create1LayerXml(IObjectDescribe describe, List<IFieldDescribe> fieldsToExport, List<IObjectData> dataList, boolean rename) {
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(DEFAULT_ROOT_NAME);
        // 以对象描述名称为名创建一级目录
        Element directory = root.addElement(DIRECTORY).addAttribute(NAME, FileExtUtil.filterName(describe.getDisplayName()));
        dataList.forEach(data -> {
            for (IFieldDescribe fieldDescribe : fieldsToExport) {
                if (Objects.nonNull(data.get(fieldDescribe.getApiName()))) {
                    // fullName形式为：filename.Npath
                    String[] fullNames = data.get(fieldDescribe.getApiName(), String.class).split("\\|");
                    for (String fullName : fullNames) {
                        if (StringUtils.isBlank(fullName)) {
                            continue;
                        }
                        String filename = FileExtUtil.getFileName(fullName);
                        if (rename) {
                            filename = FileExtUtil.renameFileName(data.getName(), filename);
                        }
                        String path = FileExtUtil.getPath(fullName);
                        //创建文件
                        if (!StringUtils.isEmpty(fullName)) {
                            directory.addElement(FILE)
                                    .addAttribute(NAME, filename)
                                    .addAttribute(NP, path);
                        }

                    }
                }
            }
        });
        return doc.asXML();
    }

    public static String createExportFileAndImage(Map<String, String> pathAndName, String fileName,
                                                  List<IObjectDescribe> describes,
                                                  Map<String, List<IFieldDescribe>> fieldsToExport,
                                                  Map<String, List<IObjectData>> dataListMap) {
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(DEFAULT_ROOT_NAME);
        root.addElement(FILE).addAttribute(NAME, fileName);
        if (CollectionUtils.notEmpty(pathAndName)) {
            pathAndName.forEach((path, name) -> {
                root.addElement(FILE)
                        .addAttribute(NAME, FileExtUtil.fillFileExt(name))
                        .addAttribute(NP, path);
            });
        }
        // 以对象描述名称为名创建一级目录
        describes.forEach(describe -> {
            Element first = root.addElement(DIRECTORY).addAttribute(NAME, FileExtUtil.filterName(describe.getDisplayName()));
            List<IFieldDescribe> fieldDescribes = fieldsToExport.get(describe.getApiName());
            if (CollectionUtils.empty(fieldDescribes)) {
                return;
            }
            List<IObjectData> dataList = dataListMap.get(describe.getApiName());
            if (CollectionUtils.empty(dataList)) {
                return;
            }
            // 以数据主属性为名建立二级目录
            dataList.forEach(data -> {
                Element second = first.addElement(DIRECTORY).addAttribute(NAME, FileExtUtil.filterName(data.getName()));
                // 以导出字段为名建立三级目录
                for (IFieldDescribe field : fieldDescribes) {
                    Element third = second.addElement(DIRECTORY).addAttribute(NAME, FileExtUtil.filterName(field.getLabel()));
                    //在第三级目录下创建文件
                    String names = data.get(field.getApiName(), String.class);
                    if (Objects.nonNull(names)) {
                        for (String fullName : names.split("\\|")) {
                            if (!StringUtils.isEmpty(fullName)) {
                                String filename = FileExtUtil.getFileName(fullName);
                                String path = FileExtUtil.getPath(fullName);
                                third.addElement(FILE).addAttribute(NAME, filename)
                                        .addAttribute(NP, path);
                            }
                        }
                    }
                }
            });
        });
        return doc.asXML();
    }

    public static String createExportFile(Map<String, String> pathAndName, String fileName) {
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(DEFAULT_ROOT_NAME);
        root.addElement(FILE).addAttribute(NAME, fileName);
        if (CollectionUtils.notEmpty(pathAndName)) {
            pathAndName.forEach((path, name) -> {
                root.addElement(FILE)
                        .addAttribute(NAME, name)
                        .addAttribute(NP, path);
            });
        }
        return doc.asXML();
    }

    /**
     * 创建三级目录接口的xml
     *
     * @param describe       对象描述
     * @param fieldsToExport 需要导出的字段
     * @param dataList       数据列表
     * @return xml字符串
     */
    public static String create3LayerXml(IObjectDescribe describe, List<IFieldDescribe> fieldsToExport, List<IObjectData> dataList) {
        Document doc = DocumentHelper.createDocument();
        Element root = doc.addElement(DEFAULT_ROOT_NAME);
        // 以对象描述名称为名创建一级目录
        Element first = root.addElement(DIRECTORY).addAttribute(NAME, FileExtUtil.filterName(describe.getDisplayName()));
        // 以数据主属性为名建立二级目录
        for (IObjectData data : dataList) {
            Element second = first.addElement(DIRECTORY).addAttribute(NAME, FileExtUtil.filterName(data.getName()));
            // 以导出字段为名建立三级目录
            for (IFieldDescribe field : fieldsToExport) {
                Element third = second.addElement(DIRECTORY).addAttribute(NAME, FileExtUtil.filterName(field.getLabel()));
                //在第三级目录下创建文件
                String names = data.get(field.getApiName(), String.class);
                if (Objects.nonNull(names)) {
                    for (String fullName : names.split("\\|")) {
                        if (!StringUtils.isEmpty(fullName)) {
                            String filename = FileExtUtil.getFileName(fullName);
                            String path = FileExtUtil.getPath(fullName);
                            third.addElement(FILE).addAttribute(NAME, filename)
                                    .addAttribute(NP, path);
                        }
                    }
                }
            }
        }
        return doc.asXML();
    }

    /**
     * 判断生成的xml是否包含图片或者附件
     */
    public static boolean hasFile(String xml) throws DocumentException {
        Document doc = DocumentHelper.parseText(xml);
        Element root = doc.getRootElement();
        Element target = (Element) root.selectSingleNode("//" + FILE);
        return Objects.nonNull(target);
    }
}
